# app_saas_merchant_store_detail_purchase_di
* comment: saas门店采购数据表
* last_data_modified_time: 2025-09-18 02:35:38

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_detail_purchase_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `store_id` BIGINT COMMENT '门店id',
  `store_name` STRING COMMENT '门店名',
  `type` BIGINT COMMENT '门店类型：0、直营店 1、加盟店 2、托管店',
  `purchase_num` BIGINT COMMENT '采购商品数',
  `purchase_price` DECIMAL(38,18) COMMENT '采购金额',
  `refund_num` BIGINT COMMENT '退款商品数',
  `refund_price` DECIMAL(38,18) COMMENT '退款金额',
  `contact` STRING COMMENT '联系人',
  `phone` STRING COMMENT '店长手机号',
  `bill_switch` BIGINT COMMENT '账期开关:1开启 0关闭',
  `store_no_delete` BIGINT COMMENT '门店编号',
  `group_name` STRING COMMENT '门店分组名称',
  `store_no` STRING COMMENT '门店编号'
)
COMMENT 'saas门店采购数据表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"7","1":"7","2":"7","3":"7","4":"7"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"store_id":{"0":"390607","1":"390664","2":"402740","3":"409604","4":"421833"},"store_name":{"0":"WX浙江台州椒江宝龙店","1":"WX浙江嘉兴桐乡洲泉湘溪大道店","2":"WX温州平阳腾蛟店","3":"WX嘉兴秀洲马厍汇历史街区店","4":"WX浙江台州黄岩新前街店"},"type":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"purchase_num":{"0":"3","1":"7","2":"3","3":"3","4":"6"},"purchase_price":{"0":"720","1":"1788","2":"910","3":"720","4":"1408"},"refund_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"refund_price":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"contact":{"0":"WX浙江台州椒江宝龙店","1":"WX浙江嘉兴桐乡洲泉湘溪大道店","2":"马瑶瑶","3":"屠冬燕","4":"钱龙帅"},"phone":{"0":"13566486593","1":"13867368450","2":"13616824997","3":"19533587788","4":"13957622703"},"bill_switch":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"store_no_delete":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"group_name":{"0":"雾鲜-嘉兴仓覆盖","1":"雾鲜-嘉兴仓覆盖","2":"雾鲜-嘉兴仓覆盖","3":"雾鲜-嘉兴仓覆盖","4":"雾鲜-嘉兴仓覆盖"},"store_no":{"0":"70","1":"127","2":"149","3":"161","4":"171"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   store_id |       type |   purchase_num |   refund_num |   bill_switch |
|:------|------------:|-----------:|-----------:|---------------:|-------------:|--------------:|
| count |    574      |     574    | 574        |      574       |     574      |    574        |
| mean  |     53.1969 |  327288    |   0.787456 |        9.33972 |      72.1254 |      0.275261 |
| std   |     40.6567 |  217187    |   0.430247 |       25.6358  |     487.646  |      0.447035 |
| min   |      7      |     410    |   0        |        0       |       0      |      0        |
| 25%   |     14      |    2920.75 |   1        |        2       |       0      |      0        |
| 50%   |     38      |  413806    |   1        |        4       |       0      |      0        |
| 75%   |     95      |  499970    |   1        |        8       |       0      |      1        |
| max   |    123      |  543362    |   2        |      424       |    8112      |      1        |