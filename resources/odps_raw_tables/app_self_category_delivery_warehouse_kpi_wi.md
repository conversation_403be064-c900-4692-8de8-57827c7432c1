# app_self_category_delivery_warehouse_kpi_wi
* comment: 履约口径kpi指标日汇总(自营)
* last_data_modified_time: 2025-09-18 03:39:45

# schema:
CREATE TABLE summerfarm_tech.`app_self_category_delivery_warehouse_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `category` STRING COMMENT '鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `day_point_cnt` BIGINT COMMENT '日均点位',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
  `damage_rate` DECIMAL(38,18) COMMENT '货损占比',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
)
COMMENT '履约口径kpi指标日汇总(自营)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025"},"week_of_year":{"0":"38","1":"38","2":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921"},"category":{"0":"乳制品","1":"其他","2":"鲜果"},"origin_total_amt":{"0":"6190467.25","1":"1934912.51","2":"3039193.73"},"real_total_amt":{"0":"6010661.691459096459096516","1":"1856394.422222222222222217","2":"2941975.490000000000000067"},"cost_amt":{"0":"5760543.49","1":"1568489.15","2":"2275849.17"},"timing_origin_total_amt":{"0":"486362","1":"195839","2":"0"},"timing_real_total_amt":{"0":"456524.971459096459096458","1":"180064.032222222222222221","2":"0"},"cust_cnt":{"0":"7265","1":"6742","2":"12961"},"order_cnt":{"0":"8598","1":"8111","2":"17814"},"point_cnt":{"0":"7549","1":"7024","2":"13566"},"day_point_cnt":{"0":"8170","1":"7629","2":"16301"},"sku_cnt":{"0":"20758","1":"19909","2":"65438"},"delivery_amt":{"0":"5336.41","1":"2073.59","2":"15440.93"},"after_sale_received_amt":{"0":"8871.8","1":"6326.74","2":"14820.69"},"inventory_loss_amt":{"0":"0","1":"498","2":"0"},"inventory_profit_amt":{"0":"0","1":"288","2":"0"},"damage_amt":{"0":"1233.38","1":"1596.7","2":"15324.72"},"damage_rate":{"0":"0.000199238595438818","1":"0.000825205269875484","2":"0.005042363653468053"},"storage_amt":{"0":"81056.692898788960655723","1":"62070.61904080094758633","2":"90947.1985136319129506"},"arterial_roads_amt":{"0":"65051.377492355423147613","1":"50717.918409464271773875","2":"72693.327142126243344991"},"deliver_amt":{"0":"239101.961847692071734628","1":"182481.776937347452459075","2":"289442.7521228748441255"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"other_amt":{"0":"9908.660698884389771711","1":"7228.83180346768805462","2":"12135.186533150408397869"},"allocation_amt":{"0":"15850.85662501484190908","1":"12113.293744122009643345","2":"14874.072303618986227439"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   order_cnt |   point_cnt |   day_point_cnt |   sku_cnt |
|:------|---------------:|-----------:|------------:|------------:|----------------:|----------:|
| count |              3 |       3    |        3    |        3    |            3    |       3   |
| mean  |             38 |    8989.33 |    11507.7  |     9379.67 |        10700    |   35368.3 |
| std   |              0 |    3449.49 |     5466.87 |     3634.96 |         4858.14 |   26044.6 |
| min   |             38 |    6742    |     8111    |     7024    |         7629    |   19909   |
| 25%   |             38 |    7003.5  |     8354.5  |     7286.5  |         7899.5  |   20333.5 |
| 50%   |             38 |    7265    |     8598    |     7549    |         8170    |   20758   |
| 75%   |             38 |   10113    |    13206    |    10557.5  |        12235.5  |   43098   |
| max   |             38 |   12961    |    17814    |    13566    |        16301    |   65438   |