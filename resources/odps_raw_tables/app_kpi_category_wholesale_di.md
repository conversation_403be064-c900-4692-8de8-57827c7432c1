# app_kpi_category_wholesale_di
* comment: 交易口径kpi指标日汇总
* last_data_modified_time: 2025-09-18 03:14:13

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_category_wholesale_di` (
  `date` STRING COMMENT '日期',
  `sku_type` STRING COMMENT '商品类型; 自营/代仓',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `order_gmv_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `deliver_cust_cnt` BIGINT COMMENT '履约客户数',
  `deliver_cust_arpu` DECIMAL(38,18) COMMENT '履约ARPU(应付总金额/客户数)',
  `deliver_order_cnt` BIGINT COMMENT '履约订单数',
  `deliver_order_avg` DECIMAL(38,18) COMMENT '履约订单均价(应付总金额/订单数)',
  `deliver_cost_amt` DECIMAL(38,18) COMMENT '履约总成本',
  `deliver_gross_profit` DECIMAL(38,18) COMMENT '履约毛利润(应付总金额-总成本)',
  `deliver_gross_profit_rate` DECIMAL(38,18) COMMENT '履约毛利率'
)
COMMENT '交易口径kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917"},"sku_type":{"0":"自营","1":"自营"},"category":{"0":"乳制品","1":"其他"},"order_gmv_amt":{"0":"544666.6","1":"76366.55"},"deliver_gmv_amt":{"0":"36120.6","1":"71466.55"},"deliver_cust_cnt":{"0":"5","1":"4"},"deliver_cust_arpu":{"0":"7224.12","1":"17866.6375"},"deliver_order_cnt":{"0":"10","1":"6"},"deliver_order_avg":{"0":"3612.06","1":"11911.091666666666666667"},"deliver_cost_amt":{"0":"33285.02","1":"69405.81"},"deliver_gross_profit":{"0":"2835.58","1":"2060.74"},"deliver_gross_profit_rate":{"0":"0.078503125640216386","1":"0.028835028415391536"},"ds":{"0":"20250917","1":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   deliver_cust_cnt |   deliver_order_cnt |
|:------|-------------------:|--------------------:|
| count |           2        |             2       |
| mean  |           4.5      |             8       |
| std   |           0.707107 |             2.82843 |
| min   |           4        |             6       |
| 25%   |           4.25     |             7       |
| 50%   |           4.5      |             8       |
| 75%   |           4.75     |             9       |
| max   |           5        |            10       |