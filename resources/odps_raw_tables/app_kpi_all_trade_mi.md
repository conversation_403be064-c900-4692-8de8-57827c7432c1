# app_kpi_all_trade_mi
* comment: 交易口径业务线kpi指标月汇总
* last_data_modified_time: 2025-09-18 02:52:34

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_all_trade_mi` (
  `month` STRING COMMENT '月份',
  `manage_type` STRING COMMENT '业务线：自营，代仓，代售，批发，SAAS鲜沐自营，SAAS鲜沐代仓，SAAS品牌方自营',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `tenant_cnt` BIGINT COMMENT '租户数（saas才有）',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_order_cnt` BIGINT COMMENT '未到货售后订单数',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)'
)
COMMENT '交易口径业务线kpi指标月汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"manage_type":{"0":"SAAS品牌方自营","1":"SAAS鲜沐代仓","2":"SAAS鲜沐自营","3":"代仓","4":"代售"},"origin_total_amt":{"0":"3788586.92","1":"4002191.07","2":"1837858.02","3":"16754226.28","4":"5930867.1"},"real_total_amt":{"0":"3788586.92","1":"4002191.07","2":"1837858.02","3":"16754226.28","4":"5783961.6"},"cust_cnt":{"0":"870","1":"520","2":"1521","3":"249","4":"12587"},"order_cnt":{"0":"2484","1":"2255","2":"8030","3":"2403","4":"24920"},"tenant_cnt":{"0":"29","1":"19","2":"31","3":"0","4":"0"},"delivery_amt":{"0":"58753.62","1":"59832.03","2":"4742.5","3":"0","4":"45816.57"},"cust_arpu":{"0":"4354.697609195402298851","1":"7696.521288461538461538","2":"1208.322169625246548323","3":"67286.049317269076305221","4":"471.18988639072058473"},"order_avg":{"0":"1525.196022544283413849","1":"1774.807569844789356984","2":"228.873975093399750934","3":"6972.212351227632126509","4":"237.99627207062600321"},"after_sale_noreceived_order_cnt":{"0":"87","1":"59","2":"197","3":"0","4":"712"},"after_sale_noreceived_amt":{"0":"80348.64","1":"51364.14","2":"36250.92","3":"0","4":"211994.07"},"after_sale_rate":{"0":"0.021208076176328033","1":"0.012834004949193992","2":"0.019724548689566346","3":"0","4":"0.035744194976144382"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   tenant_cnt |   after_sale_noreceived_order_cnt |
|:------|-----------:|------------:|-------------:|----------------------------------:|
| count |       7    |         7   |       7      |                             7     |
| mean  |    8544.14 |     26756.3 |      11.2857 |                           743.286 |
| std   |   16281    |     53693.4 |      14.5569 |                          1521.81  |
| min   |      21    |       210   |       0      |                             0     |
| 25%   |     384.5  |      2329   |       0      |                            29.5   |
| 50%   |     870    |      2484   |       0      |                            87     |
| 75%   |    7054    |     16475   |      24      |                           454.5   |
| max   |   44041    |    146992   |      31      |                          4148     |