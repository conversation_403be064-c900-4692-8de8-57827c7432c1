# app_kpi_wholesale_mi
* comment: 交易口径kpi指标日汇总
* last_data_modified_time: 2025-09-18 03:12:54

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_wholesale_mi` (
  `month` STRING COMMENT '月份',
  `sku_type` STRING COMMENT '商品类型; 自营/代仓',
  `order_gmv_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `deliver_cust_cnt` BIGINT COMMENT '履约客户数',
  `deliver_cust_arpu` DECIMAL(38,18) COMMENT '履约ARPU(应付总金额/客户数)',
  `deliver_order_cnt` BIGINT COMMENT '履约订单数',
  `deliver_order_avg` DECIMAL(38,18) COMMENT '履约订单均价(应付总金额/订单数)',
  `deliver_cost_amt` DECIMAL(38,18) COMMENT '履约总成本',
  `deliver_gross_profit` DECIMAL(38,18) COMMENT '履约毛利润(应付总金额-总成本)',
  `deliver_gross_profit_rate` DECIMAL(38,18) COMMENT '履约毛利率'
)
COMMENT '交易口径kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509"},"sku_type":{"0":"自营"},"order_gmv_amt":{"0":"12308874.67"},"deliver_gmv_amt":{"0":"1805084.47"},"deliver_cust_cnt":{"0":"18"},"deliver_cust_arpu":{"0":"100282.470555555555555556"},"deliver_order_cnt":{"0":"199"},"deliver_order_avg":{"0":"9070.776231155778894472"},"deliver_cost_amt":{"0":"1781022.15"},"deliver_gross_profit":{"0":"24062.32"},"deliver_gross_profit_rate":{"0":"0.013330301379192521"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   deliver_cust_cnt |   deliver_order_cnt |
|:------|-------------------:|--------------------:|
| count |                  1 |                   1 |
| mean  |                 18 |                 199 |
| std   |                nan |                 nan |
| min   |                 18 |                 199 |
| 25%   |                 18 |                 199 |
| 50%   |                 18 |                 199 |
| 75%   |                 18 |                 199 |
| max   |                 18 |                 199 |