# app_service_kpi_wi
* comment: 客服KPI
* last_data_modified_time: 2025-09-18 03:13:17

# schema:
CREATE TABLE summerfarm_tech.`app_service_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `cust_team` STRING COMMENT '客户类型：全量客户，平台客户',
  `channel_type` STRING COMMENT '渠道类型: 鲜沐，SAAS',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `after_sale_received_quality_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（质量问题）',
  `after_sale_received_warehouse_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（仓配问题）',
  `after_sale_received_other_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（其他问题）',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `after_sale_received_ratio` DECIMAL(38,18) COMMENT '已到货售后率',
  `delivery_evaluation_low_cnt` BIGINT COMMENT '司机评价差评数(3星以下)',
  `delivery_evaluation_cnt` BIGINT COMMENT '司机评价总数'
)
COMMENT '客服KPI'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921"},"cust_team":{"0":"全量客户","1":"全量客户","2":"平台客户","3":"平台客户"},"channel_type":{"0":"鲜沐","1":"SAAS","2":"鲜沐","3":"SAAS"},"after_sale_received_amt":{"0":"59294.23","1":"5273.58","2":"57201.42","3":"1932.73"},"after_sale_received_quality_amt":{"0":"41720.89","1":"5224.26","2":"40687.08","3":"1883.41"},"after_sale_received_warehouse_amt":{"0":"4662.41","1":"0","2":"4662.41","3":"0"},"after_sale_received_other_amt":{"0":"11258.02","1":"49.32","2":"10199.02","3":"49.32"},"delivery_origin_total_amt":{"0":"10814656.45","1":"349917.04","2":"10766318.15","3":"256552.91"},"after_sale_received_ratio":{"0":"0.005482765936591541","1":"0.015070943672820278","2":"0.005312997368557235","3":"0.007533455769416141"},"delivery_evaluation_low_cnt":{"0":"10","1":"0","2":"10","3":"0"},"delivery_evaluation_cnt":{"0":"458","1":"0","2":"458","3":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   delivery_evaluation_low_cnt |   delivery_evaluation_cnt |
|:------|---------------:|------------------------------:|--------------------------:|
| count |              4 |                        4      |                     4     |
| mean  |             38 |                        5      |                   229     |
| std   |              0 |                        5.7735 |                   264.426 |
| min   |             38 |                        0      |                     0     |
| 25%   |             38 |                        0      |                     0     |
| 50%   |             38 |                        5      |                   229     |
| 75%   |             38 |                       10      |                   458     |
| max   |             38 |                       10      |                   458     |