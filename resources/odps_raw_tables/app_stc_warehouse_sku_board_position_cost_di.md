# app_stc_warehouse_sku_board_position_cost_di
* comment: 板位费汇总数据
* last_data_modified_time: 2025-09-18 02:09:41

# schema:
CREATE TABLE summerfarm_tech.`app_stc_warehouse_sku_board_position_cost_di` (
  `date` STRING COMMENT '日期',
  `warehouse_no` BIGINT COMMENT '库存仓ID',
  `warehouse_name` STRING COMMENT '库存仓名',
  `batch_no` STRING COMMENT '批次',
  `sku_id` STRING COMMENT 'sku编号',
  `sku_type` STRING COMMENT '自营，代仓，代售',
  `spu_id` BIGINT COMMENT 'pd_id',
  `spu_no` STRING COMMENT 'spu_no',
  `spu_name` STRING COMMENT 'spu名称',
  `sku_disc` STRING COMMENT 'sku描述',
  `category` STRING COMMENT '一级类目',
  `storage_way` STRING COMMENT '存储方式：冷冻、冷藏、常温等',
  `pack_unit` STRING COMMENT '包装单位：箱、包、盒等',
  `production_date` DATETIME COMMENT '生产日期',
  `quality_date` DATETIME COMMENT '保质期',
  `init_quantity` BIGINT COMMENT '期初库存',
  `warehouse_sku_quantity` BIGINT COMMENT '仓库库存',
  `storage_type` STRING COMMENT '储存类别:整件/拆包',
  `storage_num` BIGINT COMMENT '箱入数',
  `layer_height` BIGINT COMMENT '层高',
  `layer_total` BIGINT COMMENT '层码放数量',
  `layer_cnt` BIGINT COMMENT '单板码放数量',
  `layer_cnt_up` DECIMAL(38,18) COMMENT '板位数（原始）',
  `board_position_fee_cnt` DECIMAL(38,18) COMMENT '板位数（计费）',
  `board_position_warehouse_cnt` DECIMAL(38,18) COMMENT '板位数（仓维汇总）',
  `board_position_temperature_cnt` DECIMAL(38,18) COMMENT '板位数（仓温维汇总）',
  `warehouse_layer_tempature_amt` DECIMAL(38,18) COMMENT '板位费（板位费-板位数（仓温维汇总））',
  `board_position_unit` DECIMAL(38,18) COMMENT '板位价格'
)
COMMENT '板位费汇总数据'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"warehouse_no":{"0":"2","1":"2","2":"2","3":"2","4":"2","5":"2","6":"2","7":"2","8":"2","9":"2"},"warehouse_name":{"0":"上海总仓","1":"上海总仓","2":"上海总仓","3":"上海总仓","4":"上海总仓","5":"上海总仓","6":"上海总仓","7":"上海总仓","8":"上海总仓","9":"上海总仓"},"batch_no":{"0":"202509030423725162","1":"20250917118790144","2":"20250915118216192","3":"20250902107753049","4":"20250903107656020","5":"20250902107753049","6":"20250902107753049","7":"202508270423488159","8":"20250915118216192","9":"20250911118736140"},"sku_id":{"0":"1233884862","1":"1127021730864","2":"1127021730864","3":"1126632633558","4":"1126631531517","5":"1126658165818","6":"1126158211655","7":"1003572460835","8":"1017143724678","9":"1017143724678"},"sku_type":{"0":"自营","1":"代仓","2":"代仓","3":"代仓","4":"代仓","5":"代仓","6":"代仓","7":"自营","8":"代仓","9":"代仓"},"spu_id":{"0":"391","1":"17798","2":"17798","3":"18177","4":"17911","5":"18178","6":"18193","7":"3798","8":"16158","9":"16158"},"spu_no":{"0":"1233884","1":"1127021730","2":"1127021730","3":"1126632633","4":"1126631531","5":"1126658165","6":"1126158211","7":"1003572460","8":"1017143724","9":"1017143724"},"spu_name":{"0":"安佳碎条状马苏里拉干酪","1":"香草酱","2":"香草酱","3":"NFC菠萝汁","4":"血橙汁","5":"NFC菠萝汁","6":"NFC葡萄汁","7":"安佳片状乳酸黄油","8":"牛角包酱料","9":"牛角包酱料"},"sku_disc":{"0":"12KG*1箱\/红标(产品适用于堂食，胶质感较强，拉丝效果较好)","1":"250g*1包","2":"250g*1包","3":"1KG*12瓶","4":"950g*12瓶","5":"1KG*1瓶","6":"1KG*1瓶","7":"1KG*20包","8":"250g*1袋","9":"250g*1袋"},"category":{"0":"乳制品","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"乳制品","8":"其他","9":"其他"},"storage_way":{"0":"冷冻","1":"冷冻","2":"冷冻","3":"冷冻","4":"冷冻","5":"冷冻","6":"冷冻","7":"冷冻","8":"冷冻","9":"冷冻"},"pack_unit":{"0":"箱","1":"包","2":"包","3":"箱","4":"箱","5":"瓶","6":"瓶","7":"箱","8":"袋","9":"袋"},"production_date":{"0":"2025-04-04","1":"2025-09-17","2":"2025-09-15","3":"2025-06-21","4":"2025-08-09","5":"2025-06-21","6":"2025-07-31","7":"2025-04-05","8":"2025-09-15","9":"2025-09-11"},"quality_date":{"0":"2026-07-03","1":"2025-10-01","2":"2025-09-29","3":"2026-12-21","4":"2027-02-09","5":"2026-12-21","6":"2027-01-31","7":"2027-04-05","8":"2025-09-30","9":"2025-09-26"},"init_quantity":{"0":"9","1":"32","2":"20","3":"4","4":"7","5":"36","6":"23","7":"2","8":"27","9":"14"},"warehouse_sku_quantity":{"0":"9","1":"52","2":"52","3":"4","4":"7","5":"36","6":"23","7":"10","8":"41","9":"41"},"storage_type":{"0":"整件","1":"整件","2":"整件","3":"整件","4":"整件","5":"拆包","6":"拆包","7":"整件","8":"整件","9":"整件"},"storage_num":{"0":"1","1":"1","2":"1","3":"12","4":"12","5":"12","6":"12","7":"1","8":"1","9":"1"},"layer_height":{"0":"5","1":"4","2":"4","3":"6","4":"5","5":"6","6":"6","7":"5","8":"3","9":"3"},"layer_total":{"0":"8","1":"300","2":"300","3":"8","4":"10","5":"8","6":"8","7":"8","8":"100","9":"100"},"layer_cnt":{"0":"40","1":"1200","2":"1200","3":"48","4":"50","5":"576","6":"576","7":"40","8":"300","9":"300"},"layer_cnt_up":{"0":"0.225","1":"0.02666666666666667","2":"0.01666666666666667","3":"0.08333333333333333","4":"0.14","5":"0.0625","6":"0.03993055555555556","7":"0.05","8":"0.09000000000000001","9":"0.04666666666666667"},"board_position_fee_cnt":{"0":"0.25","1":"0.25","2":"0.25","3":"0.25","4":"0.25","5":"0.25","6":"0.25","7":"0.25","8":"0.25","9":"0.25"},"board_position_warehouse_cnt":{"0":"247.25","1":"247.25","2":"247.25","3":"247.25","4":"247.25","5":"247.25","6":"247.25","7":"247.25","8":"247.25","9":"247.25"},"board_position_temperature_cnt":{"0":"26.5","1":"26.5","2":"26.5","3":"26.5","4":"26.5","5":"26.5","6":"26.5","7":"26.5","8":"26.5","9":"26.5"},"warehouse_layer_tempature_amt":{"0":"None","1":"None","2":"None","3":"None","4":"None","5":"None","6":"None","7":"None","8":"None","9":"None"},"board_position_unit":{"0":"None","1":"None","2":"None","3":"None","4":"None","5":"None","6":"None","7":"None","8":"None","9":"None"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   spu_id | production_date               | quality_date               |   init_quantity |   warehouse_sku_quantity |   storage_num |   layer_height |   layer_total |   layer_cnt |
|:------|---------------:|---------:|:------------------------------|:---------------------------|----------------:|-------------------------:|--------------:|---------------:|--------------:|------------:|
| count |     10000      | 10000    | 10000                         | 10000                      |      10000      |                10000     |    10000      |     10000      |    10000      |   10000     |
| mean  |        31.734  |  6388.81 | 2025-05-29 13:47:25.440000256 | 2027-01-26 16:46:24.960000 |         76.7762 |                  142.636 |       10.8032 |         5.2023 |       39.0537 |     674.743 |
| min   |         2      |     7    | 2019-02-14 00:00:00           | 2023-11-21 00:00:00        |          1      |                    1     |        0      |         0      |        0      |       0     |
| 25%   |        10      |  1572    | 2025-04-24 00:00:00           | 2026-01-07 00:00:00        |          3      |                    5     |        1      |         3      |        6      |      30     |
| 50%   |        29      |  4235    | 2025-07-05 12:00:00           | 2026-06-19 00:00:00        |          7      |                   13     |        1      |         5      |        8      |      72     |
| 75%   |        48      | 10591.5  | 2025-08-20 00:00:00           | 2027-02-07 06:00:00        |         20      |                   39     |       12      |         5      |       15      |     360     |
| max   |        69      | 18910    | 2026-02-01 00:00:00           | 2125-09-17 00:00:00        |      58000      |                58000     |     8000      |       600      |    24000      |  720000     |
| std   |        20.3843 |  5723.49 | nan                           | nan                        |        935.406  |                 1593.92  |       97.2785 |        10.6081 |      665.113  |    9112.6   |