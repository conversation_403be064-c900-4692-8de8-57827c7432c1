# app_warehouse_category_supply_kpi_mi
* comment: 供应链kpi
* last_data_modified_time: 2025-09-18 03:15:53

# schema:
CREATE TABLE summerfarm_tech.`app_warehouse_category_supply_kpi_mi` (
  `month` STRING COMMENT '月份',
  `warehouse_no` BIGINT COMMENT '库存仓',
  `warehouse_name` STRING COMMENT '库存仓',
  `category` STRING COMMENT '鲜果，标品',
  `sale_out_time` DECIMAL(38,18) COMMENT '售罄时长',
  `on_sale_time` DECIMAL(38,18) COMMENT '上架时长',
  `store_cost_amt` DECIMAL(38,18) COMMENT '期末库存成本',
  `sale_amt` DECIMAL(38,18) COMMENT '销售出库成本',
  `temporary_store_amt` DECIMAL(38,18) COMMENT '临保成本',
  `damage_amt` DECIMAL(38,18) COMMENT '滞销过期货损出库成本',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV'
)
COMMENT '供应链kpi'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509","5":"202509","6":"202509","7":"202509","8":"202509","9":"202509"},"warehouse_no":{"0":"2","1":"24","2":"63","3":"69","4":"145","5":"38","6":"48","7":"60","8":"60","9":"62"},"warehouse_name":{"0":"上海总仓","1":"华西总仓","2":"贵阳总仓","3":"东莞总仓","4":"济南总仓","5":"福州总仓","6":"长沙总仓","7":"昆明总仓","8":"昆明总仓","9":"苏州总仓"},"category":{"0":"鲜果","1":"标品","2":"标品","3":"标品","4":"标品","5":"标品","6":"标品","7":"标品","8":"鲜果","9":"标品"},"sale_out_time":{"0":"0","1":"71688.121666666666354424","2":"17088.088888888888907212","3":"51237.282777777777742052","4":"38976.811388888888854066","5":"46366.574444444444515402","6":"46094.23499999999989325","7":"24513.463055555555580214","8":"7927.4852777777777663","9":"1932.798055555555552334"},"on_sale_time":{"0":"0","1":"258380.688611111111083445","2":"102120.661944444444445556","3":"243277.082222222222210799","4":"153773.581111111111123667","5":"238049.123333333333331778","6":"264773.290555555555541288","7":"124258.870555555555571","8":"27480.847222222222222","9":"11016"},"store_cost_amt":{"0":"0","1":"2612655.69","2":"625505.8","3":"9455741.14","4":"409296.51","5":"2372493.93","6":"4403251.04","7":"838418.13","8":"6853.51","9":"718138.37"},"sale_amt":{"0":"0","1":"2063430.08","2":"393051.94","3":"8623626.84","4":"261059.11","5":"1768056.99","6":"3918849.65","7":"742655.68","8":"9212.34","9":"828170.38"},"temporary_store_amt":{"0":"0","1":"49.08","2":"116.75","3":"1575.62","4":"1416","5":"773.34","6":"2146.67","7":"770","8":"0","9":"0"},"damage_amt":{"0":"0","1":"170.08","2":"638","3":"0","4":"99","5":"0","6":"98","7":"28","8":"769","9":"0"},"origin_total_amt":{"0":"0","1":"1875034.75","2":"443345.34","3":"9347246.71","4":"277625.75","5":"1931583.61","6":"4557941.08","7":"819162.41","8":"12133.4","9":"863084"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |
|:------|---------------:|
| count |        34      |
| mean  |        63.1176 |
| std   |        46.7979 |
| min   |         1      |
| 25%   |        29      |
| 50%   |        60      |
| 75%   |        69      |
| max   |       155      |