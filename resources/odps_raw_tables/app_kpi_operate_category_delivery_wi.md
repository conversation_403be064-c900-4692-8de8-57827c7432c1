# app_kpi_operate_category_delivery_wi
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:42:41

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_category_delivery_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` STRING COMMENT '周数',
  `monday` STRING COMMENT '周一',
  `sunday` STRING COMMENT '周天',
  `category` STRING COMMENT '鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025"},"week_of_year":{"0":"38","1":"38","2":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921"},"category":{"0":"乳制品","1":"其他","2":"鲜果"},"origin_total_amt":{"0":"6189561.25","1":"1932930.51","2":"2900379.3"},"real_total_amt":{"0":"6009805.691459096459096516","1":"1854467.422222222222222217","2":"2803611.960000000000000067"},"marketing_amt":{"0":"179755.558540903540903484","1":"78463.087777777777777783","2":"96767.339999999999999933"},"cost_amt":{"0":"5759782.49","1":"1566952.85","2":"2166299.07"},"origin_gross":{"0":"429778.76","1":"365977.66","2":"734080.23"},"real_gross":{"0":"250023.201459096459096516","1":"287514.572222222222222217","2":"637312.890000000000000067"},"origin_gross_margin":{"0":"0.06943606220877126","1":"0.18933823958317053","2":"0.253098010318857261"},"real_gross_margin":{"0":"0.041602543292609238","1":"0.155038890830280186","2":"0.227318508799627178"},"cust_cnt":{"0":"7263","1":"6734","2":"12582"},"point_cnt":{"0":"8168","1":"7621","2":"15774"},"origin_pre_cust_price":{"0":"852.20449538758088944","1":"287.040467775467775468","2":"230.518144969003338102"},"real_pre_cust_price":{"0":"827.455003642998273316","1":"275.388687588687588688","2":"222.827210300429184549"},"timing_origin_amt":{"0":"486362","1":"195839","2":"0"},"timing_real_amt":{"0":"456524.971459096459096458","1":"180064.032222222222222221","2":"0"},"consign_origin_amt":{"0":"0","1":"0","2":"0"},"consign_real_amt":{"0":"0","1":"0","2":"0"},"consign_marketing_amt":{"0":"0","1":"0","2":"0"},"consign_origin_gross":{"0":"0","1":"0","2":"0"},"consign_real_gross":{"0":"0","1":"0","2":"0"},"consign_cust_cnt":{"0":"0","1":"0","2":"0"},"turnover_day_cnt":{"0":"18.671766625470153278","1":"28.542656844210300076","2":"2.451593936718562929"},"damage_amt":{"0":"1233.38","1":"1596.7","2":"15324.72"},"storage_amt":{"0":"81032.316533659999619463","1":"61936.963867431652782193","2":"88381.930992879181519725"},"arterial_roads_amt":{"0":"65043.531016920736633069","1":"50681.760101487770786796","2":"71482.807358824736194928"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"allocation_amt":{"0":"15849.850252944506272383","1":"12099.539668847081995294","2":"13884.789513253854228421"},"other_amt":{"0":"9902.226055033923280313","1":"7203.027687684339216088","2":"11894.472583312216254374"},"deliver_amt":{"0":"239031.218991241954991875","1":"182026.227936154501211307","2":"281246.387934327137041833"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |       3    |        3    |                  3 |
| mean  |    8859.67 |    10521    |                  0 |
| std   |    3234.47 |     4557.45 |                  0 |
| min   |    6734    |     7621    |                  0 |
| 25%   |    6998.5  |     7894.5  |                  0 |
| 50%   |    7263    |     8168    |                  0 |
| 75%   |    9922.5  |    11971    |                  0 |
| max   |   12582    |    15774    |                  0 |