# app_spu_trade_selfowend_wi
* comment: 自营品城市整体交易数据周表
* last_data_modified_time: 2025-09-18 02:39:53

# schema:
CREATE TABLE summerfarm_tech.`app_spu_trade_selfowend_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `cause_type` STRING COMMENT '鲜沐，SAAS',
  `register_province` STRING COMMENT '注册省',
  `register_city` STRING COMMENT '注册市',
  `register_area` STRING COMMENT '注册区',
  `spu_id` STRING COMMENT 'pd_id',
  `spu_name` STRING COMMENT 'SPU名称',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举：大客户,普通,批发客户;<以前的：单店,批发大客户,普通大客户,KA大客户 已弃用>',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目',
  `category2_id` STRING COMMENT '二级类目id',
  `category2` STRING COMMENT '二级类目',
  `category3_id` STRING COMMENT '三级类目id',
  `category3` STRING COMMENT '三级类目',
  `category4_id` STRING COMMENT '四级类目id',
  `category4` STRING COMMENT '四级类目',
  `origin_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `new_cust_cnt` BIGINT COMMENT '（历史截止今天）当天新客户数',
  `order_time_cnt` DECIMAL(38,18) COMMENT '客户下单时间间隔之和,天',
  `order_time_avg` DECIMAL(38,18) COMMENT '平均下单时间间隔,天',
  `order_time_cnt_m` DECIMAL(38,18) COMMENT '客户下单时间间隔之和,分',
  `order_time_avg_m` DECIMAL(38,18) COMMENT '平均下单时间间隔,分'
)
COMMENT '自营品城市整体交易数据周表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025","5":"2025","6":"2025","7":"2025","8":"2025","9":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38","5":"38","6":"38","7":"38","8":"38","9":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915","5":"20250915","6":"20250915","7":"20250915","8":"20250915","9":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921","5":"20250921","6":"20250921","7":"20250921","8":"20250921","9":"20250921"},"cause_type":{"0":"鲜沐","1":"鲜沐","2":"鲜沐","3":"鲜沐","4":"鲜沐","5":"鲜沐","6":"鲜沐","7":"鲜沐","8":"鲜沐","9":"鲜沐"},"register_province":{"0":"上海","1":"广东","2":"广东","3":"江西","4":"江西","5":"浙江","6":"浙江","7":"浙江","8":"浙江","9":"浙江"},"register_city":{"0":"上海市","1":"江门市","2":"珠海市","3":"九江市","4":"南昌市","5":"宁波市","6":"杭州市","7":"杭州市","8":"杭州市","9":"温州市"},"register_area":{"0":"青浦区","1":"鹤山市","2":"金湾区","3":"共青城市","4":"南昌县","5":"鄞州区","6":"富阳区","7":"富阳区","8":"萧山区","9":"瓯海区"},"spu_id":{"0":"1529","1":"1691","2":"1691","3":"1691","4":"1691","5":"1691","6":"1691","7":"1691","8":"1691","9":"1691"},"spu_name":{"0":"精选拼配咖啡豆(意式经典)","1":"C味原味波波晶球","2":"C味原味波波晶球","3":"C味原味波波晶球","4":"C味原味波波晶球","5":"C味原味波波晶球","6":"C味原味波波晶球","7":"C味原味波波晶球","8":"C味原味波波晶球","9":"C味原味波波晶球"},"cust_type":{"0":"咖啡","1":"咖啡","2":"茶饮","3":"面包蛋糕","4":"面包蛋糕","5":"面包蛋糕","6":"其他","7":"茶饮","8":"其他","9":"茶饮"},"brand_type":{"0":"普通","1":"普通","2":"普通","3":"普通","4":"普通","5":"普通","6":"普通","7":"普通","8":"普通","9":"普通"},"brand_name":{"0":"SUMMERFARM","1":"C味","2":"C味","3":"C味","4":"C味","5":"C味","6":"C味","7":"C味","8":"C味","9":"C味"},"category1":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"category2_id":{"0":"421","1":"405","2":"405","3":"405","4":"405","5":"405","6":"405","7":"405","8":"405","9":"405"},"category2":{"0":"饮品原料","1":"成品原料","2":"成品原料","3":"成品原料","4":"成品原料","5":"成品原料","6":"成品原料","7":"成品原料","8":"成品原料","9":"成品原料"},"category3_id":{"0":"523","1":"458","2":"458","3":"458","4":"458","5":"458","6":"458","7":"458","8":"458","9":"458"},"category3":{"0":"咖啡豆及其制品","1":"果冻类配料","2":"果冻类配料","3":"果冻类配料","4":"果冻类配料","5":"果冻类配料","6":"果冻类配料","7":"果冻类配料","8":"果冻类配料","9":"果冻类配料"},"category4_id":{"0":"832","1":"666","2":"666","3":"666","4":"666","5":"666","6":"666","7":"666","8":"666","9":"666"},"category4":{"0":"咖啡豆","1":"波波丨晶球","2":"波波丨晶球","3":"波波丨晶球","4":"波波丨晶球","5":"波波丨晶球","6":"波波丨晶球","7":"波波丨晶球","8":"波波丨晶球","9":"波波丨晶球"},"origin_total_amt":{"0":"117","1":"15.6","2":"7.8","3":"460","4":"7.8","5":"7.8","6":"15.6","7":"92","8":"39","9":"184"},"real_total_amt":{"0":"117","1":"15.6","2":"4.5","3":"410","4":"6","5":"6","6":"15.6","7":"92","8":"37.2","9":"184"},"cust_cnt":{"0":"1","1":"1","2":"1","3":"1","4":"1","5":"1","6":"1","7":"1","8":"1","9":"1"},"new_cust_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"1","5":"0","6":"0","7":"0","8":"1","9":"0"},"order_time_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"order_time_avg":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"order_time_cnt_m":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"order_time_avg_m":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |    cust_cnt |   new_cust_cnt |
|:------|---------------:|------------:|---------------:|
| count |           5492 | 5492        |    5492        |
| mean  |             38 |    1.17079  |       0.190459 |
| std   |              0 |    0.600157 |       0.40232  |
| min   |             38 |    0        |       0        |
| 25%   |             38 |    1        |       0        |
| 50%   |             38 |    1        |       0        |
| 75%   |             38 |    1        |       0        |
| max   |             38 |   11        |       3        |