# app_follow_up_relation_release_detail_df
* comment: crm客户释放公海规则和倒计时
* last_data_modified_time: 2025-09-18 02:48:06

# schema:
CREATE TABLE summerfarm_tech.`app_follow_up_relation_release_detail_df` (
  `m_id` BIGINT COMMENT '客户m_id',
  `release_rule` STRING COMMENT '释放规则 1. 30天未下单且15天未拜访 2. 60天未下单 3. 首单客户 4. 公海转私海',
  `danger_day` BIGINT COMMENT '释放倒计时',
  `has_unfinished_delivery` BIGINT COMMENT '是否有未履约订单,0:否，1：是',
  `release_date` DATETIME COMMENT '释放日期',
  `cust_type` STRING COMMENT '客户类型：高校，非高校，不掉落客户',
  `date_flag` STRING COMMENT '日期标识'
)
COMMENT 'crm客户释放公海规则和倒计时'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"m_id":{"0":"572490","1":"464076","2":"463690","3":"373079","4":"350504"},"release_rule":{"0":"不掉落客户","1":"不掉落客户","2":"不掉落客户","3":"不掉落客户","4":"不掉落客户"},"danger_day":{"0":"999","1":"999","2":"999","3":"999","4":"999"},"has_unfinished_delivery":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"release_date":{"0":"9999-12-31 00:00:00","1":"9999-12-31 00:00:00","2":"9999-12-31 00:00:00","3":"9999-12-31 00:00:00","4":"9999-12-31 00:00:00"},"cust_type":{"0":"不掉落客户","1":"不掉落客户","2":"不掉落客户","3":"不掉落客户","4":"不掉落客户"},"date_flag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   m_id |   danger_day |   has_unfinished_delivery |
|:------|-------:|-------------:|--------------------------:|
| count |  10000 |        10000 |                     10000 |
| mean  | 382176 |          999 |                         0 |
| std   | 142638 |            0 |                         0 |
| min   |     71 |          999 |                         0 |
| 25%   | 320025 |          999 |                         0 |
| 50%   | 416596 |          999 |                         0 |
| 75%   | 490606 |          999 |                         0 |
| max   | 574951 |          999 |                         0 |