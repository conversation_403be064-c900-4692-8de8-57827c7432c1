# app_warehouse_path_time_df
* comment: 开放仓到仓之间路途时间
* last_data_modified_time: 2025-09-18 01:33:21

# schema:
CREATE TABLE summerfarm_tech.`app_warehouse_path_time_df` (
  `in_warehouse_no` BIGINT COMMENT '转入仓编号',
  `out_warehouse_no` BIGINT COMMENT '转出仓编号',
  `cost_time` BIGINT COMMENT '用时(天)'
)
COMMENT '开放仓到仓之间路途时间'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"in_warehouse_no":{"0":"7","1":"10","2":"12","3":"24","4":"29","5":"31","6":"38","7":"46","8":"48","9":"59"},"out_warehouse_no":{"0":"2","1":"2","2":"2","3":"2","4":"2","5":"2","6":"2","7":"2","8":"2","9":"2"},"cost_time":{"0":"2","1":"2","2":"2","3":"2","4":"2","5":"2","6":"2","7":"2","8":"2","9":"2"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   in_warehouse_no |   out_warehouse_no |    cost_time |
|:------|------------------:|-------------------:|-------------:|
| count |        10000      |          10000     | 10000        |
| mean  |          110.518  |             96.488 |     3.8806   |
| std   |           41.6163 |             34.859 |     0.473884 |
| min   |            2      |              2     |     2        |
| 25%   |           83      |             77     |     4        |
| 50%   |          113      |            101     |     4        |
| 75%   |          142      |            124     |     4        |
| max   |          177      |            147     |     4        |