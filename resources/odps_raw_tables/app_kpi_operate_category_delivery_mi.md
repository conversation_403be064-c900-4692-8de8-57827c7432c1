# app_kpi_operate_category_delivery_mi
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:40:12

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_category_delivery_mi` (
  `month` STRING COMMENT '月份',
  `category` STRING COMMENT '鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509"},"category":{"0":"乳制品","1":"其他","2":"鲜果"},"origin_total_amt":{"0":"36100731.57","1":"11082709.97","2":"16752358.7"},"real_total_amt":{"0":"35075324.208000000000000249","1":"10604717.575454545454545484","2":"16252274.050000000000000304"},"marketing_amt":{"0":"1025407.361999999999999751","1":"477992.394545454545454516","2":"500084.649999999999999696"},"cost_amt":{"0":"33644764.3","1":"8982659.87","2":"12543517.02"},"origin_gross":{"0":"2455967.27","1":"2100050.1","2":"4208841.68"},"real_gross":{"0":"1430559.908000000000000249","1":"1622057.705454545454545484","2":"3708757.030000000000000304"},"origin_gross_margin":{"0":"0.06803095569511751","1":"0.189488861991757058","2":"0.251238751233281556"},"real_gross_margin":{"0":"0.040785365219053829","1":"0.152956238005708501","2":"0.228199267289613542"},"cust_cnt":{"0":"23752","1":"22528","2":"29778"},"point_cnt":{"0":"48220","1":"44285","2":"91437"},"origin_pre_cust_price":{"0":"1519.902811131694173122","1":"491.952679776278409091","2":"562.575011753643629525"},"real_pre_cust_price":{"0":"1476.7313997979117548","1":"470.734977603628615702","2":"545.781249580227013231"},"timing_origin_amt":{"0":"2476140","1":"1031243","2":"0"},"timing_real_amt":{"0":"2318594.94799999999999999","1":"953791.315454545454545436","2":"0"},"consign_origin_amt":{"0":"0","1":"0","2":"0"},"consign_real_amt":{"0":"0","1":"0","2":"0"},"consign_marketing_amt":{"0":"0","1":"0","2":"0"},"consign_origin_gross":{"0":"0","1":"0","2":"0"},"consign_real_gross":{"0":"0","1":"0","2":"0"},"consign_cust_cnt":{"0":"0","1":"0","2":"0"},"turnover_day_cnt":{"0":"18.386302700716658799","1":"26.402520197683574904","2":"2.450614641502696303"},"damage_amt":{"0":"10287.36","1":"13859.45","2":"105392.01"},"storage_amt":{"0":"459810.501254622769941043","1":"352113.050695888004326376","2":"499012.374044022916644921"},"arterial_roads_amt":{"0":"359466.401810551067473318","1":"278873.303238034653301865","2":"395592.438835738888434964"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"allocation_amt":{"0":"97750.885219844812587308","1":"75486.237446684243835575","2":"79506.341177358506331462"},"other_amt":{"0":"55046.665339680875492665","1":"41006.629150443692248148","2":"68278.696392573044666395"},"deliver_amt":{"0":"1339322.27609290175158778","1":"1019140.540072984816035087","2":"1569171.204326681088767446"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |       3    |         3   |                  3 |
| mean  |   25352.7  |     61314   |                  0 |
| std   |    3881.01 |     26161.4 |                  0 |
| min   |   22528    |     44285   |                  0 |
| 25%   |   23140    |     46252.5 |                  0 |
| 50%   |   23752    |     48220   |                  0 |
| 75%   |   26765    |     69828.5 |                  0 |
| max   |   29778    |     91437   |                  0 |