# app_saas_market_item_on_sale_sold_out_detail_di
* comment: saas商品上架售罄汇总表
* last_data_modified_time: 2025-09-18 02:18:16

# schema:
CREATE TABLE summerfarm_tech.`app_saas_market_item_on_sale_sold_out_detail_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `item_id` BIGINT COMMENT '商品id',
  `sale_price` DECIMAL(38,18) COMMENT '售价',
  `sold_out_time` BIGINT COMMENT '日累计售罄时长（秒级）',
  `on_sale_time` BIGINT COMMENT '日累计上架时长（秒级）'
)
COMMENT 'saas商品上架售罄汇总表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"item_id":{"0":"1","1":"2","2":"4","3":"5","4":"6"},"sale_price":{"0":"0.01","1":"0.02","2":"550","3":"550","4":"100"},"sold_out_time":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"on_sale_time":{"0":"86400","1":"86400","2":"86400","3":"86400","4":"86400"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   item_id |   sold_out_time |   on_sale_time |
|:------|------------:|----------:|----------------:|---------------:|
| count |  10000      |   10000   |         10000   |       10000    |
| mean  |     56.8978 |   24234.6 |         38572.9 |       86300.7  |
| std   |     30.8422 |   12090.6 |         42485.3 |        2475.14 |
| min   |      2      |       1   |             0   |          73    |
| 25%   |     40      |   15808.8 |             0   |       86400    |
| 50%   |     53      |   24993.5 |             0   |       86400    |
| 75%   |     85      |   33818.2 |         86400   |       86400    |
| max   |    108      |   44901   |         86400   |       86400    |