# app_warehouse_in_out_di
* comment: 出入库仓库维度按天汇总表
* last_data_modified_time: 2025-09-18 02:02:42

# schema:
CREATE TABLE summerfarm_tech.`app_warehouse_in_out_di` (
  `date` STRING COMMENT '日期',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '仓库名',
  `category1` STRING COMMENT '一级分类',
  `allocation_out_cnt` BIGINT COMMENT '调拨出库发货量',
  `allocation_out_cost` DECIMAL(38,18) COMMENT '调拨出库发货总成本',
  `sale_out_cnt` BIGINT COMMENT '销售出库发货量',
  `sale_out_cost` DECIMAL(38,18) COMMENT '销售出库发货总成本',
  `selef_sale_out_cnt` BIGINT COMMENT '销售自提出库发货量',
  `selef_sale_out_cost` DECIMAL(38,18) COMMENT '销售自提出库发货总成本',
  `sample_out_cnt` BIGINT COMMENT '出样出库发货量',
  `sample_out_cost` DECIMAL(38,18) COMMENT '出样出库发货总成本',
  `damage_out_cnt` BIGINT COMMENT '货损出库发货量',
  `damage_out_cost` DECIMAL(38,18) COMMENT '货损出库发货总成本',
  `transfer_out_cnt` BIGINT COMMENT '转换出库发货量',
  `transfer_out_cost` DECIMAL(38,18) COMMENT '转换出库发货总成本',
  `purchase_back_cnt` BIGINT COMMENT '采购退货出库发货量',
  `purchase_back_cost` DECIMAL(38,18) COMMENT '采购退货出库发货总成本',
  `replenish_out_cnt` BIGINT COMMENT '补货出库发货量',
  `replenish_out_cost` DECIMAL(38,18) COMMENT '补货出库发货总成本',
  `allocation_in_cnt` BIGINT COMMENT '调拨入库收货量',
  `allocation_in_cost` DECIMAL(38,18) COMMENT '调拨入库收货总成本',
  `purchase_in_cnt` BIGINT COMMENT '采购入库收货量',
  `purchase_in_cost` DECIMAL(38,18) COMMENT '采购入库收货总成本',
  `return_in_cnt` BIGINT COMMENT '退货入库收货量',
  `return_in_cost` DECIMAL(38,18) COMMENT '退货入库收货总成本',
  `transfer_in_cnt` BIGINT COMMENT '转换入库收货量',
  `transfer_in_cost` DECIMAL(38,18) COMMENT '转换入库收货总成本'
)
COMMENT '出入库仓库维度按天汇总表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"warehouse_no":{"0":"10","1":"10","2":"10","3":"121","4":"121","5":"150","6":"117","7":"117","8":"2","9":"2"},"warehouse_name":{"0":"嘉兴总仓","1":"嘉兴总仓","2":"嘉兴总仓","3":"嘉兴海盐总仓","4":"嘉兴海盐总仓","5":"嘉兴水果批发总仓","6":"东莞冷冻总仓","7":"东莞冷冻总仓","8":"上海总仓","9":"上海总仓"},"category1":{"0":"鲜果","1":"乳制品","2":"其他","3":"其他","4":"乳制品","5":"鲜果","6":"其他","7":"乳制品","8":"乳制品","9":"其他"},"allocation_out_cnt":{"0":"0.0","1":"776.0","2":"1188.0","3":"836.0","4":"49.0","5":"0.0","6":"271.0","7":"100.0","8":"0.0","9":"0.0"},"allocation_out_cost":{"0":"0","1":"92445.08","2":"139072.84","3":"54963.39","4":"14930","5":"0","6":"35015.82","7":"133099","8":"0","9":"0"},"sale_out_cnt":{"0":"12094.0","1":"3096.0","2":"3549.0","3":"1572.0","4":"66.0","5":"43.0","6":"621.0","7":"118.0","8":"221.0","9":"252.0"},"sale_out_cost":{"0":"412946.66","1":"635477.86","2":"271149.1","3":"68084.96","4":"50831.58","5":"0","6":"40863.67","7":"106792","8":"93348.37","9":"30609.32"},"selef_sale_out_cnt":{"0":"0.0","1":"45.0","2":"12.0","3":"0.0","4":"0.0","5":"0.0","6":"8.0","7":"0.0","8":"0.0","9":"195.0"},"selef_sale_out_cost":{"0":"0","1":"32600.84","2":"897.3","3":"0","4":"0","5":"0","6":"123.5","7":"0","8":"0","9":"69192.75"},"sample_out_cnt":{"0":"11.0","1":"167.0","2":"11.0","3":"5.0","4":"0.0","5":"0.0","6":"2.0","7":"0.0","8":"0.0","9":"0.0"},"sample_out_cost":{"0":"241.81","1":"135.37","2":"323.1","3":"71.35","4":"0","5":"0","6":"34","7":"0","8":"0","9":"0"},"damage_out_cnt":{"0":"75.0","1":"5.0","2":"11.0","3":"0.0","4":"0.0","5":"0.0","6":"0.0","7":"0.0","8":"0.0","9":"0.0"},"damage_out_cost":{"0":"2766.26","1":"61.35","2":"187","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"transfer_out_cnt":{"0":"824.0","1":"65.0","2":"63.0","3":"47.0","4":"0.0","5":"0.0","6":"24.0","7":"5.0","8":"5.0","9":"0.0"},"transfer_out_cost":{"0":"53507.69","1":"11690.76","2":"11911.8","3":"9352.51","4":"0","5":"0","6":"4562.67","7":"7700","8":"4830","9":"0"},"purchase_back_cnt":{"0":"6.0","1":"29.0","2":"100.0","3":"0.0","4":"0.0","5":"0.0","6":"0.0","7":"0.0","8":"0.0","9":"0.0"},"purchase_back_cost":{"0":"683.34","1":"3685.61","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"replenish_out_cnt":{"0":"1.0","1":"7.0","2":"5.0","3":"2.0","4":"0.0","5":"0.0","6":"0.0","7":"0.0","8":"0.0","9":"0.0"},"replenish_out_cost":{"0":"72","1":"889.63","2":"440.92","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"allocation_in_cnt":{"0":"0.0","1":"0.0","2":"0.0","3":"0.0","4":"0.0","5":"0.0","6":"0.0","7":"0.0","8":"0.0","9":"0.0"},"allocation_in_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"purchase_in_cnt":{"0":"9138.0","1":"1462.0","2":"3306.0","3":"3207.0","4":"0.0","5":"43.0","6":"350.0","7":"310.0","8":"1635.0","9":"385.0"},"purchase_in_cost":{"0":"314292.06","1":"395771.6","2":"410376.91","3":"0","4":"0","5":"0","6":"44530","7":"507300","8":"854630.25","9":"101598.18"},"return_in_cnt":{"0":"0.0","1":"0.0","2":"1.0","3":"2.0","4":"0.0","5":"0.0","6":"0.0","7":"0.0","8":"6.0","9":"0.0"},"return_in_cost":{"0":"0","1":"0","2":"320","3":"19","4":"0","5":"0","6":"0","7":"0","8":"232.98","9":"0"},"transfer_in_cnt":{"0":"2529.0","1":"369.0","2":"815.0","3":"530.0","4":"0.0","5":"0.0","6":"266.0","7":"20.0","8":"44.0","9":"0.0"},"transfer_in_cost":{"0":"53508.85","1":"11690.95","2":"11911.32","3":"9352.54","4":"0","5":"0","6":"4562.6","7":"7700","8":"4830.12","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   allocation_out_cnt |   sale_out_cnt |   selef_sale_out_cnt |   sample_out_cnt |   damage_out_cnt |   transfer_out_cnt |   purchase_back_cnt |   replenish_out_cnt |   allocation_in_cnt |   purchase_in_cnt |   return_in_cnt |   transfer_in_cnt |
|:------|---------------:|---------------------:|---------------:|---------------------:|-----------------:|-----------------:|-------------------:|--------------------:|--------------------:|--------------------:|------------------:|----------------:|------------------:|
| count |       135      |               58     |         58     |              58      |         58       |         58       |            58      |            58       |           58        |             58      |            58     |       58        |            58     |
| mean  |       104.17   |              109.828 |        822.379 |              34.7069 |          3.96552 |          2.91379 |            32.7586 |             3.63793 |            0.465517 |             64.6552 |           617.655 |        0.965517 |           170.655 |
| std   |        45.8703 |              282.35  |       1782.71  |             176.452  |         22.0127  |         10.445   |           111.697  |            14.5019  |            1.27342  |            216.392  |          1473.56  |        3.08912  |           381.122 |
| min   |         2      |                0     |          0     |               0      |          0       |          0       |             0      |             0       |            0        |              0      |             0     |        0        |             0     |
| 25%   |        69.5    |                0     |         52.75  |               0      |          0       |          0       |             0      |             0       |            0        |              0      |             0     |        0        |             0     |
| 50%   |       107      |                0     |        246     |               0      |          0       |          0       |             4.5    |             0       |            0        |              0      |            12.5   |        0        |            28     |
| 75%   |       142.5    |                0     |        675.5   |               1.75   |          0       |          1       |            18.75   |             0       |            0        |              0      |           403.75  |        0        |           175     |
| max   |       177      |             1188     |      12094     |            1303      |        167       |         75       |           824      |           100       |            7        |           1347      |          9138     |       16        |          2529     |