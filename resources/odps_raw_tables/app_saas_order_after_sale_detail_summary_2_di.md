# app_saas_order_after_sale_detail_summary_2_di
* comment: SAAS对账单-售后明细汇总表(刷新近90天的数据)
* last_data_modified_time: 2025-09-18 02:53:13

# schema:
CREATE TABLE summerfarm_tech.`app_saas_order_after_sale_detail_summary_2_di` (
  `tenant_id` BIGINT COMMENT '租户Id',
  `time_tag` STRING COMMENT '时间标签',
  `after_sale_order_no` STRING COMMENT '售后单号',
  `order_no` STRING COMMENT '订单编号',
  `order_item_id` STRING COMMENT '订单项编号',
  `order_time` DATETIME COMMENT '下单时间',
  `finished_time` DATETIME COMMENT '售后成功时间',
  `item_id` BIGINT COMMENT '商品item_id',
  `item_code` STRING COMMENT '商品自有编码',
  `item_title` STRING COMMENT '商品标题',
  `item_specification` STRING COMMENT '商品规格',
  `item_amount` BIGINT COMMENT '订单商品数量',
  `after_sale_amount` BIGINT COMMENT '售后商品数量',
  `after_sale_type` BIGINT COMMENT '售后类型 0 已到货 1 未到货',
  `service_type` BIGINT COMMENT '售后服务类型 1 退款 2 退款录入账单 3 退货退款 4 退货退款录入账单 5 换货 6 补发 7、退款录入余额 8、 退货退款录入余额',
  `responsibility_type` BIGINT COMMENT '售后责任方 0、供应商1、品牌方2、门店',
  `reason` STRING COMMENT '售后原因',
  `total_refund_price` DECIMAL(38,18) COMMENT '售后总金额',
  `item_refund_price` DECIMAL(38,18) COMMENT '商品退款金额',
  `delivery_refund_fee` DECIMAL(38,18) COMMENT '运费退款金额',
  `goods_sku` STRING COMMENT '货品sku',
  `goods_title` STRING COMMENT '货品名称',
  `goods_specification` STRING COMMENT '货品规格',
  `goods_supplier_name` STRING COMMENT '货品供应商名称',
  `goods_type` BIGINT COMMENT '货品类型 1、鲜沐直供 2、代仓',
  `goods_agent_fee` DECIMAL(38,18) COMMENT '货品代仓费用',
  `goods_refund_price` DECIMAL(38,18) COMMENT '货品退款金额',
  `goods_supply_price` DECIMAL(38,18) COMMENT '货品采购单价',
  `pay_type` BIGINT COMMENT '支付方式 1,线上支付 2,账期 3、余额支付',
  `store_id` BIGINT COMMENT '门店ID',
  `store_name` STRING COMMENT '门店名称',
  `first_classification` STRING COMMENT '一级分组',
  `second_classification` STRING COMMENT '二级分组',
  `store_no` STRING COMMENT '门店编号',
  `supplier_id` BIGINT COMMENT '供应商id',
  `after_sale_time` DATETIME COMMENT '售后发起时间',
  `goods_delivery_fee_refund` DECIMAL(38,18) COMMENT '货品配送费售后金额',
  `delivery_refund_fee_flag` BIGINT COMMENT '退运费标识 0、不退 1、退',
  `order_confirm_receipt_time` DATETIME COMMENT '订单确认收货时间',
  `supplier_sku` STRING COMMENT '鲜沐sku',
  `after_sale_unit` STRING COMMENT '售后单位',
  `province` STRING COMMENT '省',
  `city` STRING COMMENT '市',
  `area` STRING COMMENT '区',
  `address` STRING COMMENT '收货地址',
  `special_time_tag` STRING COMMENT '特殊的时间标签（处理跨期）'
)
COMMENT 'SAAS对账单-售后明细汇总表(刷新近90天的数据)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"13","1":"13","2":"14","3":"123","4":"35"},"time_tag":{"0":"20250916","1":"20250916","2":"20250916","3":"20250916","4":"20250916"},"after_sale_order_no":{"0":"AS1967562201750794240","1":"AS1967869665801105408","2":"AS1967788791328485376","3":"AS1967658839367036928","4":"AS1967821954519363584"},"order_no":{"0":"OR175774076773837","1":"OR175781652530315","2":"OR175781862898788","3":"OR175782909638657","4":"OR175799968312194"},"order_item_id":{"0":"1774947","1":"1777711","2":"1777754","3":"1778122","4":"1784237"},"order_time":{"0":"2025-09-13 13:19:27","1":"2025-09-14 10:22:05","2":"2025-09-14 10:57:08","3":"2025-09-14 13:51:36","4":"2025-09-16 13:14:43"},"finished_time":{"0":"2025-09-16 13:51:00","1":"2025-09-16 16:41:12","2":"2025-09-16 13:49:12","3":"2025-09-16 13:58:01","4":"2025-09-16 13:25:07"},"item_id":{"0":"23354","1":"1601","2":"44037","3":"44624","4":"34433"},"item_code":{"0":"None","1":"","2":"None","3":"None","4":""},"item_title":{"0":"即食秘鲁牛油果","1":"即食秘鲁牛油果","2":"南非橙","3":"越南金煌芒","4":"Protag纯牛奶"},"item_specification":{"0":"20个*1箱\/一级\/单果90-130g","1":"20个*1箱\/一级\/单果130-160g","2":"5斤*1包\/普通\/单果180g+","3":"净重12-12.5斤\/一级\/果规500g+\/村上专用","4":"1L*12盒"},"item_amount":{"0":"2","1":"1","2":"2","3":"3","4":"20"},"after_sale_amount":{"0":"1","1":"2","2":"252","3":"2199","4":"20"},"after_sale_type":{"0":"0","1":"0","2":"0","3":"0","4":"1"},"service_type":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"responsibility_type":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"reason":{"0":"商品品质问题","1":"商品品质问题","2":"商品品质问题","3":"商品品质问题","4":"拍多\/拍错\/不想要"},"total_refund_price":{"0":"4.93","1":"12.5","2":"3.89","3":"28.8","4":"1440"},"item_refund_price":{"0":"4.93","1":"12.5","2":"3.89","3":"28.8","4":"1440"},"delivery_refund_fee":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"goods_sku":{"0":"102189","1":"102191","2":"125179","3":"125855","4":"101508"},"goods_title":{"0":"即食秘鲁牛油果","1":"即食秘鲁牛油果","2":"南非橙","3":"越南金煌芒","4":"Protag纯牛奶"},"goods_specification":{"0":"20个*1箱\/一级\/单果90-130g","1":"20个*1箱\/一级\/单果130-160g","2":"5斤*1包\/普通\/单果180g+","3":"净重12-12.5斤\/一级\/果规500g+\/村上专用","4":"1L*12盒"},"goods_supplier_name":{"0":"鲜沐供应商","1":"鲜沐供应商","2":"鲜沐供应商","3":"鲜沐供应商","4":"鲜沐供应商"},"goods_type":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"goods_agent_fee":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"goods_refund_price":{"0":"4.93","1":"12.5","2":"3.6","3":"26.18","4":"1400"},"goods_supply_price":{"0":"98.58","1":"125","2":"35.7","3":"71.43","4":"70"},"pay_type":{"0":"2","1":"1","2":"1","3":"2","4":"1"},"store_id":{"0":"963","1":"397138","2":"1472","3":"534686","4":"537473"},"store_name":{"0":"一只酸奶牛观音桥步行街店","1":"一只酸奶牛西流沱","2":"湘西永顺府正街店","3":"杭州遇见村上(下沙天街店)","4":"爆珠公首创奥特莱斯"},"first_classification":{"0":"鲜果","1":"鲜果","2":"鲜果","3":"遇见村上","4":"乳制品"},"second_classification":{"0":"鲜果","1":"鲜果","2":"橙","3":"水果","4":"牛乳牛奶"},"store_no":{"0":"CQ00374","1":"1001","2":"Y803204465","3":"MD00006","4":"163"},"supplier_id":{"0":"0.0","1":"0.0","2":"0.0","3":"0.0","4":"0.0"},"after_sale_time":{"0":"2025-09-15 20:12:39","1":"2025-09-16 16:34:24","2":"2025-09-16 11:13:02","3":"2025-09-16 02:36:39","4":"2025-09-16 13:24:49"},"goods_delivery_fee_refund":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"delivery_refund_fee_flag":{"0":"0","1":"0","2":"0","3":"0","4":"1"},"order_confirm_receipt_time":{"0":"2025-09-14 12:30:30","1":"2025-09-15 12:08:44","2":"2025-09-18 00:00:01","3":"2025-09-15 09:45:32","4":"2025-09-16 13:25:06"},"supplier_sku":{"0":"5404785633","1":"5404785202","2":"612248466","3":"36801","4":"607164503701"},"after_sale_unit":{"0":"个","1":"个","2":"g","3":"g","4":"盒"},"province":{"0":"重庆","1":"重庆","2":"湖南","3":"浙江","4":"重庆"},"city":{"0":"重庆市","1":"重庆市","2":"湘西土家族苗族自治州","3":"杭州市","4":"重庆市"},"area":{"0":"江北区","1":"巴南区","2":"永顺县","3":"钱塘区","4":"巴南区"},"address":{"0":"观音桥步行街新世界百货门口岗亭","1":"戏台一只酸奶牛","2":"益禾堂(府正街店)103门面","3":"下沙街道金沙大道470号","4":"巴南区首创奥特莱斯(重庆店)F1-5"},"special_time_tag":{"0":"20250916","1":"20250916","2":"20250918","3":"20250916","4":"20250916"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id | order_time                    | finished_time                 |   item_id |   item_amount |   after_sale_amount |   after_sale_type |   service_type |   responsibility_type |   goods_type |     pay_type |   store_id |   supplier_id | after_sale_time            |   delivery_refund_fee_flag | order_confirm_receipt_time    |
|:------|------------:|:------------------------------|:------------------------------|----------:|--------------:|--------------------:|------------------:|---------------:|----------------------:|-------------:|-------------:|-----------:|--------------:|:---------------------------|---------------------------:|:------------------------------|
| count |  10000      | 10000                         | 10000                         |   10000   |   10000       |            10000    |      10000        |    10000       |          10000        | 10000        | 10000        |      10000 |      8781     | 10000                      |               10000        | 9982                          |
| mean  |     36.7339 | 2025-08-02 02:27:21.013799936 | 2025-08-04 03:20:08.472199936 |   21085.9 |       2.2623  |              412.21 |          0.3628   |        1.3382  |              0.0354   |     1.0288   |     1.4493   |     230239 |       304.381 | 2025-08-03 23:17:15.772800 |                   0.1212   | 2025-08-02 22:06:41.926567936 |
| min   |      2      | 2024-09-24 12:50:00           | 2025-06-20 08:28:03           |     279   |       1       |                1    |          0        |        1       |              0        |     0        |     1        |          2 |         0     | 2025-06-02 10:21:35        |                   0        | 2024-10-01 09:35:01           |
| 25%   |     13      | 2025-07-09 21:55:24           | 2025-07-11 20:03:11.750000128 |    1602   |       1       |                1    |          0        |        1       |              0        |     1        |     1        |       1242 |         0     | 2025-07-11 19:51:12        |                   0        | 2025-07-11 09:52:54           |
| 50%   |     13      | 2025-08-01 20:14:18           | 2025-08-04 13:31:37           |   23353   |       2       |                2    |          0        |        1       |              0        |     1        |     1        |     349951 |         0     | 2025-08-03 20:47:44.500000 |                   0        | 2025-08-02 11:57:43           |
| 75%   |     53      | 2025-08-24 21:49:55.500000    | 2025-08-26 17:57:15.249999872 |   35356   |       2       |               12    |          1        |        1       |              0        |     1        |     2        |     458104 |         0     | 2025-08-26 17:19:02        |                   0        | 2025-08-25 15:16:42           |
| max   |    123      | 2025-09-17 21:12:13           | 2025-09-17 21:30:11           |   44845   |     192       |            23250    |          1        |        8       |              2        |     2        |     8        |     542937 |      3406     | 2025-09-17 21:29:33        |                   1        | 2025-09-18 00:00:01           |
| std   |     37.2174 | nan                           | nan                           |   15004   |       5.40132 |             1405.68 |          0.480832 |        1.07179 |              0.248099 |     0.462809 |     0.878355 |     229141 |       894.398 | nan                        |                   0.326376 | nan                           |