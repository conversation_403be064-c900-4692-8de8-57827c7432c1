# app_stc_kpi_wi
* comment: 仓配KPI汇总表
* last_data_modified_time: 2025-09-18 03:47:38

# schema:
CREATE TABLE summerfarm_tech.`app_stc_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `check_sku_cnt` BIGINT COMMENT '抽检数量',
  `in_bound_sku_cnt` BIGINT COMMENT '入库数量',
  `check_rate` DECIMAL(38,18) COMMENT '抽检比例',
  `back_order_cnt` BIGINT COMMENT '退货总单数',
  `finish_order_cnt` BIGINT COMMENT '已完成单数',
  `back_finish_rate` DECIMAL(38,18) COMMENT '退货完结率',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责',
  `sale_amt` DECIMAL(38,18) COMMENT '销售金额金额',
  `error_sku_cnt` BIGINT COMMENT '错误件数',
  `error_sku_cnt_wah` BIGINT COMMENT '错误件数_仓配责',
  `error_cust_cnt` BIGINT COMMENT '错误客户数',
  `error_cust_cnt_wah` BIGINT COMMENT '错误客户数_仓配责',
  `cust_cnt` BIGINT COMMENT '活跃客户数',
  `sku_cnt` BIGINT COMMENT '配送件数',
  `total_point_cnt` BIGINT COMMENT '总点位数',
  `point_cnt` BIGINT COMMENT '点位数（不含喜茶）',
  `no_in_time_point_cnt` BIGINT COMMENT '不及时点位数（不含喜茶）',
  `delay_time_point_cnt_2` BIGINT COMMENT '前置2小时不及时点位数（不含喜茶）',
  `out_time` DECIMAL(38,18) COMMENT '配送超时时间（不含喜茶）',
  `delay_time_2` DECIMAL(38,18) COMMENT '前置2小时配送超时时间（不含喜茶）',
  `path_cnt` BIGINT COMMENT '线路数（不含喜茶）',
  `delay_path_cnt` BIGINT COMMENT '出库不及时线路数（不含喜茶）',
  `out_distance_point_cnt` BIGINT COMMENT '超距离点位数（含喜茶）',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额',
  `after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责',
  `after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责',
  `after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责',
  `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责',
  `after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付GMV',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本'
)
COMMENT '仓配KPI汇总表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025"},"week_of_year":{"0":"38"},"monday":{"0":"20250915"},"sunday":{"0":"20250921"},"check_sku_cnt":{"0":"0"},"in_bound_sku_cnt":{"0":"0"},"check_rate":{"0":"0"},"back_order_cnt":{"0":"201"},"finish_order_cnt":{"0":"54"},"back_finish_rate":{"0":"0.2686567164179104"},"damage_amt":{"0":"18154.8"},"damage_amt_wah":{"0":"403.27"},"sale_amt":{"0":"10181302.38"},"error_sku_cnt":{"0":"69"},"error_sku_cnt_wah":{"0":"69"},"error_cust_cnt":{"0":"47"},"error_cust_cnt_wah":{"0":"47"},"cust_cnt":{"0":"22915"},"sku_cnt":{"0":"119547"},"total_point_cnt":{"0":"25523"},"point_cnt":{"0":"25523"},"no_in_time_point_cnt":{"0":"3"},"delay_time_point_cnt_2":{"0":"1606"},"out_time":{"0":"0"},"delay_time_2":{"0":"1067.5025"},"path_cnt":{"0":"1325"},"delay_path_cnt":{"0":"6"},"out_distance_point_cnt":{"0":"73"},"after_sale_amt":{"0":"70624.89"},"after_sale_amt_wah":{"0":"7930.05"},"after_sale_amt_pur":{"0":"6779.5"},"after_sale_amt_che":{"0":"41602.47"},"after_sale_amt_pur_che":{"0":"48381.97"},"after_sale_amt_oth":{"0":"14312.87"},"delivery_total_amt":{"0":"16138948.239999999999892537"},"coupon_amt":{"0":"128250.73219169719158962"},"origin_total_amt":{"0":"11164573.49"},"real_total_amt":{"0":"10809031.6036813186813188"},"storage_amt":{"0":"234074.510453222250635253"},"arterial_roads_amt":{"0":"188462.623043946216927852"},"deliver_amt":{"0":"711026.4909079156183646"},"self_picked_amt":{"0":"0"},"other_amt":{"0":"29272.679035502538315171"},"allocation_amt":{"0":"42838.222672755910737692"},"cost_amt":{"0":"9604881.81"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   check_sku_cnt |   in_bound_sku_cnt |   back_order_cnt |   finish_order_cnt |   error_sku_cnt |   error_sku_cnt_wah |   error_cust_cnt |   error_cust_cnt_wah |   cust_cnt |   sku_cnt |   total_point_cnt |   point_cnt |   no_in_time_point_cnt |   delay_time_point_cnt_2 |   path_cnt |   delay_path_cnt |   out_distance_point_cnt |
|:------|---------------:|----------------:|-------------------:|-----------------:|-------------------:|----------------:|--------------------:|-----------------:|---------------------:|-----------:|----------:|------------------:|------------:|-----------------------:|-------------------------:|-----------:|-----------------:|-------------------------:|
| count |              1 |               1 |                  1 |                1 |                  1 |               1 |                   1 |                1 |                    1 |          1 |         1 |                 1 |           1 |                      1 |                        1 |          1 |                1 |                        1 |
| mean  |             38 |               0 |                  0 |              201 |                 54 |              69 |                  69 |               47 |                   47 |      22915 |    119547 |             25523 |       25523 |                      3 |                     1606 |       1325 |                6 |                       73 |
| std   |            nan |             nan |                nan |              nan |                nan |             nan |                 nan |              nan |                  nan |        nan |       nan |               nan |         nan |                    nan |                      nan |        nan |              nan |                      nan |
| min   |             38 |               0 |                  0 |              201 |                 54 |              69 |                  69 |               47 |                   47 |      22915 |    119547 |             25523 |       25523 |                      3 |                     1606 |       1325 |                6 |                       73 |
| 25%   |             38 |               0 |                  0 |              201 |                 54 |              69 |                  69 |               47 |                   47 |      22915 |    119547 |             25523 |       25523 |                      3 |                     1606 |       1325 |                6 |                       73 |
| 50%   |             38 |               0 |                  0 |              201 |                 54 |              69 |                  69 |               47 |                   47 |      22915 |    119547 |             25523 |       25523 |                      3 |                     1606 |       1325 |                6 |                       73 |
| 75%   |             38 |               0 |                  0 |              201 |                 54 |              69 |                  69 |               47 |                   47 |      22915 |    119547 |             25523 |       25523 |                      3 |                     1606 |       1325 |                6 |                       73 |
| max   |             38 |               0 |                  0 |              201 |                 54 |              69 |                  69 |               47 |                   47 |      22915 |    119547 |             25523 |       25523 |                      3 |                     1606 |       1325 |                6 |                       73 |