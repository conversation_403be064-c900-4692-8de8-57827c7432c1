# app_self_delivery_warehouse_kpi_di
* comment: 履约口径kpi指标日汇总(自营)
* last_data_modified_time: 2025-09-18 03:42:13

# schema:
CREATE TABLE summerfarm_tech.`app_self_delivery_warehouse_kpi_di` (
  `date` STRING COMMENT '日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `day_point_cnt` BIGINT COMMENT '日均点位',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
  `damage_rate` DECIMAL(38,18) COMMENT '货损占比',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶仓储成本',
  `heytea_arterial_roads_amt` DECIMAL(38,18) COMMENT '喜茶调拨成本',
  `heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送成本',
  `heytea_way_deliver_amt` DECIMAL(38,18) COMMENT '喜茶专配成本',
  `saas_point_cnt` BIGINT COMMENT 'SaaS点位数',
  `sample_point_cnt` BIGINT COMMENT '出样点位数',
  `after_sale_point_cnt` BIGINT COMMENT '补货点位数',
  `wholesale_point_cnt` BIGINT COMMENT '批发客户点位数',
  `heytea_point_cnt` BIGINT COMMENT '喜茶共配点位数',
  `heytea_way_point_cnt` BIGINT COMMENT '喜茶专配点位数',
  `delivery_out_times_amt` DECIMAL(38,18) COMMENT '运费+超时加单费',
  `deliver_coupon_amt` DECIMAL(38,18) COMMENT '优惠券费',
  `total_point_cnt` BIGINT COMMENT '总配送点位',
  `out_times_amt` DECIMAL(38,18) COMMENT '超时加单费',
  `precision_delivery_fee` DECIMAL(38,18) COMMENT '精准送费用'
)
COMMENT '履约口径kpi指标日汇总(自营)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917"},"origin_total_amt":{"0":"3967014.19"},"real_total_amt":{"0":"3827155.941904761904761989"},"cost_amt":{"0":"3420755.59"},"timing_origin_total_amt":{"0":"253286"},"timing_real_total_amt":{"0":"237279.261904761904761907"},"cust_cnt":{"0":"7554"},"order_cnt":{"0":"9035"},"point_cnt":{"0":"7838"},"day_point_cnt":{"0":"7840"},"sku_cnt":{"0":"36568"},"delivery_amt":{"0":"7465.68"},"after_sale_received_amt":{"0":"9618.96"},"inventory_loss_amt":{"0":"498"},"inventory_profit_amt":{"0":"288"},"damage_amt":{"0":"5983.13"},"damage_rate":{"0":"0.001508219964295111"},"storage_amt":{"0":"82452.731271303317116568"},"arterial_roads_amt":{"0":"69854.363153924175749072"},"deliver_amt":{"0":"258544.825569522321148225"},"self_picked_amt":{"0":"0"},"other_amt":{"0":"10534.089157949077962481"},"allocation_amt":{"0":"13955.847358090349593986"},"heytea_storage_amt":{"0":"0"},"heytea_arterial_roads_amt":{"0":"0"},"heytea_deliver_amt":{"0":"0"},"heytea_way_deliver_amt":{"0":"0"},"saas_point_cnt":{"0":"486"},"sample_point_cnt":{"0":"64"},"after_sale_point_cnt":{"0":"4"},"wholesale_point_cnt":{"0":"6"},"heytea_point_cnt":{"0":"0"},"heytea_way_point_cnt":{"0":"0"},"delivery_out_times_amt":{"0":"13713.72"},"deliver_coupon_amt":{"0":"6248.04"},"total_point_cnt":{"0":"8488"},"out_times_amt":{"0":"20"},"precision_delivery_fee":{"0":"150"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   point_cnt |   day_point_cnt |   sku_cnt |   saas_point_cnt |   sample_point_cnt |   after_sale_point_cnt |   wholesale_point_cnt |   heytea_point_cnt |   heytea_way_point_cnt |   total_point_cnt |
|:------|-----------:|------------:|------------:|----------------:|----------:|-----------------:|-------------------:|-----------------------:|----------------------:|-------------------:|-----------------------:|------------------:|
| count |          1 |           1 |           1 |               1 |         1 |                1 |                  1 |                      1 |                     1 |                  1 |                      1 |                 1 |
| mean  |       7554 |        9035 |        7838 |            7840 |     36568 |              486 |                 64 |                      4 |                     6 |                  0 |                      0 |              8488 |
| std   |        nan |         nan |         nan |             nan |       nan |              nan |                nan |                    nan |                   nan |                nan |                    nan |               nan |
| min   |       7554 |        9035 |        7838 |            7840 |     36568 |              486 |                 64 |                      4 |                     6 |                  0 |                      0 |              8488 |
| 25%   |       7554 |        9035 |        7838 |            7840 |     36568 |              486 |                 64 |                      4 |                     6 |                  0 |                      0 |              8488 |
| 50%   |       7554 |        9035 |        7838 |            7840 |     36568 |              486 |                 64 |                      4 |                     6 |                  0 |                      0 |              8488 |
| 75%   |       7554 |        9035 |        7838 |            7840 |     36568 |              486 |                 64 |                      4 |                     6 |                  0 |                      0 |              8488 |
| max   |       7554 |        9035 |        7838 |            7840 |     36568 |              486 |                 64 |                      4 |                     6 |                  0 |                      0 |              8488 |