# app_temporary_insurance_risk_df
* comment: 临保风险品
* last_data_modified_time: 2025-09-15 01:43:26

# schema:
CREATE TABLE summerfarm_tech.`app_temporary_insurance_risk_df` (
  `sku` STRING COMMENT 'sku',
  `warehouse_no` BIGINT COMMENT '库存仓编号',
  `area_no` BIGINT COMMENT '城市编号',
  `large_area_no` BIGINT COMMENT '运营大区编号',
  `date_flag` STRING COMMENT '日期'
)
COMMENT '临保风险品'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"sku":{"0":"106306","1":"106306","2":"106306","3":"106306","4":"110025","5":"110025","6":"110025","7":"110025","8":"1207763507","9":"1207763507"},"warehouse_no":{"0":"2","1":"2","2":"2","3":"2","4":"2","5":"2","6":"2","7":"2","8":"2","9":"2"},"area_no":{"0":"2750","1":"2767","2":"23575","3":"44225","4":"2750","5":"2767","6":"23575","7":"44225","8":"2750","9":"2767"},"large_area_no":{"0":"2","1":"2","2":"2","3":"2","4":"2","5":"2","6":"2","7":"2","8":"2","9":"2"},"date_flag":{"0":"20250914","1":"20250914","2":"20250914","3":"20250914","4":"20250914","5":"20250914","6":"20250914","7":"20250914","8":"20250914","9":"20250914"},"ds":{"0":"20250914","1":"20250914","2":"20250914","3":"20250914","4":"20250914","5":"20250914","6":"20250914","7":"20250914","8":"20250914","9":"20250914"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   area_no |   large_area_no |
|:------|---------------:|----------:|----------------:|
| count |    10000       |   10000   |      10000      |
| mean  |        9.8976  |   19288.9 |         25.7546 |
| std   |        0.89933 |   14660.8 |         38.3224 |
| min   |        2       |    1001   |          1      |
| 25%   |       10       |    8577   |          1      |
| 50%   |       10       |   18704   |          1      |
| 75%   |       10       |   25541   |         84      |
| max   |       10       |   44271   |         89      |