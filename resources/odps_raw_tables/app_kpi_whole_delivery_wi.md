# app_kpi_whole_delivery_wi
* comment: 履约kpi汇总
* last_data_modified_time: 2025-09-18 10:39:01

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_whole_delivery_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `cust_group` STRING COMMENT '客户类型:大客户、平台客户、批发客户、ALL',
  `sku_type` STRING COMMENT '商品类型:自营、代仓、全品类、SAAS客户自营、ALL',
  `category` STRING COMMENT '商品类目:鲜果、乳制品、其他、ALL',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `dlv_cost_amt` DECIMAL(38,18) COMMENT '履约总成本',
  `dlv_origin_gross_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `dlv_real_gross_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `dlv_cust_cnt` BIGINT COMMENT '履约客户数',
  `dlv_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（含精准送，超时加单费，去除优惠券）',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `offine_delivey_amt` DECIMAL(38,18) COMMENT '履约费用',
  `offine_no_delivey_amt` DECIMAL(38,18) COMMENT '非履约费用',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额',
  `timing_cost_amt` DECIMAL(38,18) COMMENT '省心送履约总成本'
)
COMMENT '履约kpi汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"cust_group":{"0":"大客户","1":"ALL","2":"ALL","3":"ALL","4":"ALL"},"sku_type":{"0":"ALL","1":"ALL","2":"代仓","3":"代仓","4":"自营"},"category":{"0":"鲜果","1":"鲜果","2":"其他","3":"鲜果","4":"其他"},"dlv_origin_total_amt":{"0":"906872.76","1":"3446491.23","2":"3594738.98","3":"314181.78","4":"2010858.46"},"dlv_real_total_amt":{"0":"904441.630000000000000004","1":"3346683.320000000000000067","2":"3594738.98","3":"314181.78","4":"1932340.372222222222222217"},"dlv_cost_amt":{"0":"448609.14","1":"2355787.37","2":"511993.9","3":"11022.43","4":"1640968.34"},"dlv_origin_gross_profit":{"0":"458263.62","1":"1090703.86","2":"3045343.55","3":"303159.35","4":"369890.12"},"dlv_real_gross_profit":{"0":"455832.490000000000000004","1":"990895.950000000000000067","2":"3045343.55","3":"303159.35","4":"291372.032222222222222217"},"dlv_cust_cnt":{"0":"1808","1":"13332","2":"353","3":"164","4":"6747"},"dlv_point_cnt":{"0":"2343","1":"16883","2":"448","3":"189","4":"7639"},"delivery_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"after_sale_received_amt":{"0":"10665.3","1":"51693.76","2":"0","3":"47.92","4":"7377.18"},"damage_amt":{"0":"0","1":"15324.72","2":"1198.75","3":"0","4":"1596.7"},"offine_delivey_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"offine_no_delivey_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"timing_origin_total_amt":{"0":"0","1":"0","2":"0","3":"0","4":"195839"},"timing_real_total_amt":{"0":"0","1":"0","2":"0","3":"0","4":"180064.032222222222222221"},"timing_cost_amt":{"0":"0","1":"0","2":"0","3":"0","4":"158272.77"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   dlv_cust_cnt |   dlv_point_cnt |
|:------|---------------:|---------------:|----------------:|
| count |             54 |          54    |           54    |
| mean  |             38 |        3862.48 |         4713.26 |
| std   |              0 |        5598.05 |         7025.94 |
| min   |             38 |           1    |            3    |
| 25%   |             38 |         106.25 |          119.25 |
| 50%   |             38 |         498.5  |          579    |
| 75%   |             38 |        7053    |         7938.25 |
| max   |             38 |       19885    |        25515    |