# app_saas_merchant_store_order_proportion_analysis_quarter_df
* comment: saas-门店订货占比分析-季
* last_data_modified_time: 2025-09-18 02:19:34

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_order_proportion_analysis_quarter_df` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `item_id` BIGINT COMMENT '商品id',
  `store_id` BIGINT COMMENT '门店id',
  `order_amount` BIGINT COMMENT '订货数量',
  `order_amount_proportion` DECIMAL(38,18) COMMENT '订货数量占比',
  `order_amount_proportion_upper_period` DECIMAL(38,18) COMMENT '订货数量占比环比',
  `order_price` DECIMAL(38,18) COMMENT '订货金额',
  `order_price_proportion` DECIMAL(38,18) COMMENT '订货金额占比',
  `order_price_proportion_upper_period` DECIMAL(38,18) COMMENT '订货金额占比环比',
  `total_order_amount` BIGINT COMMENT '总订货数量',
  `total_order_price` DECIMAL(38,18) COMMENT '总订货金额',
  `order_amount_upper_period` BIGINT COMMENT '上周期订货数量',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '上周期订货金额',
  `total_order_amount_upper_period` BIGINT COMMENT '上周期总订货数量',
  `total_order_price_upper_period` DECIMAL(38,18) COMMENT '上周期总订货金额'
)
COMMENT 'saas-门店订货占比分析-季'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"time_tag":{"0":"20220401","1":"20220401","2":"20220401","3":"20220401","4":"20220401"},"item_id":{"0":"1","1":"3","2":"9","3":"16","4":"17"},"store_id":{"0":"5","1":"3","2":"1","3":"1","4":"1"},"order_amount":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"order_amount_proportion":{"0":"0.1111111111111111","1":"0.1111111111111111","2":"0.1111111111111111","3":"0.1111111111111111","4":"0.1111111111111111"},"order_amount_proportion_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_price":{"0":"0.01","1":"0.02","2":"26","3":"27","4":"58"},"order_price_proportion":{"0":"0.000050753692331117","1":"0.000101507384662234","2":"0.131959600060904431","3":"0.13703496929401614","4":"0.294371415520479115"},"order_price_proportion_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"total_order_amount":{"0":"9","1":"9","2":"9","3":"9","4":"9"},"total_order_price":{"0":"197.03","1":"197.03","2":"197.03","3":"197.03","4":"197.03"},"order_amount_upper_period":{"0":"nan","1":"nan","2":"nan","3":"nan","4":"nan"},"order_price_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"total_order_amount_upper_period":{"0":"nan","1":"nan","2":"nan","3":"nan","4":"nan"},"total_order_price_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   item_id |   store_id |   order_amount |   total_order_amount |   order_amount_upper_period |   total_order_amount_upper_period |
|:------|------------:|----------:|-----------:|---------------:|---------------------:|----------------------------:|----------------------------------:|
| count | 10000       |  10000    |  10000     |      10000     |              10000   |                  1936       |                           1936    |
| mean  |     4.8254  |    240.24 |    264.096 |          8.762 |              26340.3 |                     9.57076 |                          12607.7  |
| std   |     1.25406 |    123.25 |    190.427 |         20.324 |              16305.5 |                    17.4638  |                           1376.96 |
| min   |     2       |      1    |      1     |          1     |                  4   |                     1       |                          11589    |
| 25%   |     4       |    162    |     99     |          1     |              11589   |                     1       |                          11589    |
| 50%   |     4       |    230    |    232     |          2     |              14468   |                     4       |                          11589    |
| 75%   |     6       |    276    |    375     |          7     |              44033   |                    11       |                          14468    |
| max   |     8       |    889    |    801     |        350     |              44033   |                   350       |                          14468    |