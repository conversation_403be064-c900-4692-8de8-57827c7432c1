# app_self_delivery_warehouse_kpi_wi
* comment: 履约口径kpi指标日汇总(自营)
* last_data_modified_time: 2025-09-18 03:41:24

# schema:
CREATE TABLE summerfarm_tech.`app_self_delivery_warehouse_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `day_point_cnt` BIGINT COMMENT '日均点位',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
  `damage_rate` DECIMAL(38,18) COMMENT '货损占比',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶仓储成本',
  `heytea_arterial_roads_amt` DECIMAL(38,18) COMMENT '喜茶调拨成本',
  `heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送成本',
  `heytea_way_deliver_amt` DECIMAL(38,18) COMMENT '喜茶专配成本',
  `saas_point_cnt` BIGINT COMMENT 'SaaS点位数',
  `sample_point_cnt` BIGINT COMMENT '出样点位数',
  `after_sale_point_cnt` BIGINT COMMENT '补货点位数',
  `wholesale_point_cnt` BIGINT COMMENT '批发客户点位数',
  `heytea_point_cnt` BIGINT COMMENT '喜茶共配点位数',
  `heytea_way_point_cnt` BIGINT COMMENT '喜茶专配点位数',
  `delivery_out_times_amt` DECIMAL(38,18) COMMENT '运费+超时加单费',
  `deliver_coupon_amt` DECIMAL(38,18) COMMENT '优惠券费',
  `total_point_cnt` BIGINT COMMENT '总配送点位',
  `out_times_amt` DECIMAL(38,18) COMMENT '超时加单费',
  `precision_delivery_fee` DECIMAL(38,18) COMMENT '精准送费用'
)
COMMENT '履约口径kpi指标日汇总(自营)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025"},"week_of_year":{"0":"38"},"monday":{"0":"20250915"},"sunday":{"0":"20250921"},"origin_total_amt":{"0":"11164573.49"},"real_total_amt":{"0":"10809031.6036813186813188"},"cost_amt":{"0":"9604881.81"},"timing_origin_total_amt":{"0":"682201"},"timing_real_total_amt":{"0":"636589.003681318681318679"},"cust_cnt":{"0":"18592"},"order_cnt":{"0":"26985"},"point_cnt":{"0":"19610"},"day_point_cnt":{"0":"23449"},"sku_cnt":{"0":"106105"},"delivery_amt":{"0":"22850.93"},"after_sale_received_amt":{"0":"30019.23"},"inventory_loss_amt":{"0":"498"},"inventory_profit_amt":{"0":"288"},"damage_amt":{"0":"18154.8"},"damage_rate":{"0":"0.001626107796796813"},"storage_amt":{"0":"234074.510453222250635253"},"arterial_roads_amt":{"0":"188462.623043946216927852"},"deliver_amt":{"0":"711026.4909079156183646"},"self_picked_amt":{"0":"0"},"other_amt":{"0":"29272.679035502538315171"},"allocation_amt":{"0":"42838.222672755910737692"},"heytea_storage_amt":{"0":"0"},"heytea_arterial_roads_amt":{"0":"0"},"heytea_deliver_amt":{"0":"0"},"heytea_way_deliver_amt":{"0":"0"},"saas_point_cnt":{"0":"1415"},"sample_point_cnt":{"0":"114"},"after_sale_point_cnt":{"0":"22"},"wholesale_point_cnt":{"0":"19"},"heytea_point_cnt":{"0":"0"},"heytea_way_point_cnt":{"0":"0"},"delivery_out_times_amt":{"0":"41906.79"},"deliver_coupon_amt":{"0":"19055.86"},"total_point_cnt":{"0":"25523"},"out_times_amt":{"0":"45"},"precision_delivery_fee":{"0":"420"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   order_cnt |   point_cnt |   day_point_cnt |   sku_cnt |   saas_point_cnt |   sample_point_cnt |   after_sale_point_cnt |   wholesale_point_cnt |   heytea_point_cnt |   heytea_way_point_cnt |   total_point_cnt |
|:------|---------------:|-----------:|------------:|------------:|----------------:|----------:|-----------------:|-------------------:|-----------------------:|----------------------:|-------------------:|-----------------------:|------------------:|
| count |              1 |          1 |           1 |           1 |               1 |         1 |                1 |                  1 |                      1 |                     1 |                  1 |                      1 |                 1 |
| mean  |             38 |      18592 |       26985 |       19610 |           23449 |    106105 |             1415 |                114 |                     22 |                    19 |                  0 |                      0 |             25523 |
| std   |            nan |        nan |         nan |         nan |             nan |       nan |              nan |                nan |                    nan |                   nan |                nan |                    nan |               nan |
| min   |             38 |      18592 |       26985 |       19610 |           23449 |    106105 |             1415 |                114 |                     22 |                    19 |                  0 |                      0 |             25523 |
| 25%   |             38 |      18592 |       26985 |       19610 |           23449 |    106105 |             1415 |                114 |                     22 |                    19 |                  0 |                      0 |             25523 |
| 50%   |             38 |      18592 |       26985 |       19610 |           23449 |    106105 |             1415 |                114 |                     22 |                    19 |                  0 |                      0 |             25523 |
| 75%   |             38 |      18592 |       26985 |       19610 |           23449 |    106105 |             1415 |                114 |                     22 |                    19 |                  0 |                      0 |             25523 |
| max   |             38 |      18592 |       26985 |       19610 |           23449 |    106105 |             1415 |                114 |                     22 |                    19 |                  0 |                      0 |             25523 |