# app_kpi_wholesale_di
* comment: 交易口径kpi指标日汇总
* last_data_modified_time: 2025-09-18 03:05:17

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_wholesale_di` (
  `date` STRING COMMENT '日期',
  `sku_type` STRING COMMENT '商品类型; 自营/代仓',
  `order_gmv_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `deliver_cust_cnt` BIGINT COMMENT '履约客户数',
  `deliver_cust_arpu` DECIMAL(38,18) COMMENT '履约ARPU(应付总金额/客户数)',
  `deliver_order_cnt` BIGINT COMMENT '履约订单数',
  `deliver_order_avg` DECIMAL(38,18) COMMENT '履约订单均价(应付总金额/订单数)',
  `deliver_cost_amt` DECIMAL(38,18) COMMENT '履约总成本',
  `deliver_gross_profit` DECIMAL(38,18) COMMENT '履约毛利润(应付总金额-总成本)',
  `deliver_gross_profit_rate` DECIMAL(38,18) COMMENT '履约毛利率'
)
COMMENT '交易口径kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917"},"sku_type":{"0":"自营"},"order_gmv_amt":{"0":"621033.15"},"deliver_gmv_amt":{"0":"107587.15"},"deliver_cust_cnt":{"0":"6"},"deliver_cust_arpu":{"0":"17931.191666666666666667"},"deliver_order_cnt":{"0":"11"},"deliver_order_avg":{"0":"9780.65"},"deliver_cost_amt":{"0":"102690.83"},"deliver_gross_profit":{"0":"4896.32"},"deliver_gross_profit_rate":{"0":"0.045510267722492881"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   deliver_cust_cnt |   deliver_order_cnt |
|:------|-------------------:|--------------------:|
| count |                  1 |                   1 |
| mean  |                  6 |                  11 |
| std   |                nan |                 nan |
| min   |                  6 |                  11 |
| 25%   |                  6 |                  11 |
| 50%   |                  6 |                  11 |
| 75%   |                  6 |                  11 |
| max   |                  6 |                  11 |