# app_spu_delivery_selfowend_wi
* comment: 城市整体配送数据日表
* last_data_modified_time: 2025-09-18 03:19:07

# schema:
CREATE TABLE summerfarm_tech.`app_spu_delivery_selfowend_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `cause_type` STRING COMMENT '鲜沐，SAAS',
  `province` STRING COMMENT '省',
  `admin_city` STRING COMMENT '市',
  `area` STRING COMMENT '区',
  `spu_id` BIGINT COMMENT 'pd_id',
  `spu_name` STRING COMMENT 'spu名称',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举：大客户,普通,批发客户;<以前的：单店,批发大客户,普通大客户,KA大客户 已弃用>',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目',
  `category2_id` STRING COMMENT '二级类目id',
  `category2` STRING COMMENT '二级类目',
  `category3_id` STRING COMMENT '三级类目id',
  `category3` STRING COMMENT '三级类目',
  `category4_id` STRING COMMENT '四级类目id',
  `category4` STRING COMMENT '四级类目',
  `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '总成本',
  `origin_pay_margin` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_pay_margin` DECIMAL(38,18) COMMENT '实付毛利润',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `deliver_total_amt` DECIMAL(38,18) COMMENT '配送GMV'
)
COMMENT '城市整体配送数据日表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025","5":"2025","6":"2025","7":"2025","8":"2025","9":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38","5":"38","6":"38","7":"38","8":"38","9":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915","5":"20250915","6":"20250915","7":"20250915","8":"20250915","9":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921","5":"20250921","6":"20250921","7":"20250921","8":"20250921","9":"20250921"},"cause_type":{"0":"SAAS","1":"SAAS","2":"SAAS","3":"SAAS","4":"SAAS","5":"SAAS","6":"SAAS","7":"SAAS","8":"SAAS","9":"SAAS"},"province":{"0":"上海","1":"上海","2":"上海","3":"上海","4":"上海","5":"上海","6":"上海","7":"上海","8":"上海","9":"上海"},"admin_city":{"0":"上海市","1":"上海市","2":"上海市","3":"上海市","4":"上海市","5":"上海市","6":"上海市","7":"上海市","8":"上海市","9":"上海市"},"area":{"0":"徐汇区","1":"徐汇区","2":"徐汇区","3":"徐汇区","4":"普陀区","5":"普陀区","6":"普陀区","7":"浦东新区","8":"浦东新区","9":"浦东新区"},"spu_id":{"0":"6974","1":"7978","2":"9260","3":"13605","4":"3830","5":"9260","6":"13605","7":"3830","8":"7978","9":"9260"},"spu_name":{"0":"澄善草莓果酱","1":"澄善HPP速冻芭乐浆","2":"酷盖纯牛奶","3":"C味冷冻黑糖珍珠（快煮）","4":"C味马蹄爆爆珠","5":"酷盖纯牛奶","6":"C味冷冻黑糖珍珠（快煮）","7":"C味马蹄爆爆珠","8":"澄善HPP速冻芭乐浆","9":"酷盖纯牛奶"},"cust_type":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"brand_type":{"0":"大客户","1":"大客户","2":"大客户","3":"大客户","4":"大客户","5":"大客户","6":"大客户","7":"大客户","8":"大客户","9":"大客户"},"brand_name":{"0":"澄善","1":"澄善","2":"酷盖","3":"C味","4":"C味","5":"酷盖","6":"C味","7":"C味","8":"澄善","9":"酷盖"},"category1":{"0":"其他","1":"其他","2":"乳制品","3":"其他","4":"其他","5":"乳制品","6":"其他","7":"其他","8":"其他","9":"乳制品"},"category2_id":{"0":"417","1":"421","2":"401","3":"405","4":"405","5":"401","6":"405","7":"405","8":"421","9":"401"},"category2":{"0":"水果制品","1":"饮品原料","2":"乳制品","3":"成品原料","4":"成品原料","5":"乳制品","6":"成品原料","7":"成品原料","8":"饮品原料","9":"乳制品"},"category3_id":{"0":"506","1":"522","2":"435","3":"457","4":"458","5":"435","6":"457","7":"458","8":"522","9":"435"},"category3":{"0":"水果风味制品","1":"果汁原料","2":"液体乳","3":"粉圆类配料","4":"果冻类配料","5":"液体乳","6":"粉圆类配料","7":"果冻类配料","8":"果汁原料","9":"液体乳"},"category4_id":{"0":"785","1":"830","2":"607","3":"663","4":"671","5":"607","6":"663","7":"671","8":"830","9":"607"},"category4":{"0":"果茶酱","1":"果汁原浆","2":"常温牛奶","3":"珍珠","4":"爆爆珠","5":"常温牛奶","6":"珍珠","7":"爆爆珠","8":"果汁原浆","9":"常温牛奶"},"origin_total_amt":{"0":"200","1":"50","2":"2214","3":"1050","4":"4","5":"246","6":"210","7":"6","8":"100","9":"1640"},"real_total_amt":{"0":"200","1":"50","2":"2214","3":"1050","4":"4","5":"246","6":"210","7":"6","8":"100","9":"1640"},"cost_amt":{"0":"144","1":"34","2":"1377","3":"600","4":"28","5":"153","6":"120","7":"42","8":"68","9":"1020"},"origin_pay_margin":{"0":"56","1":"16","2":"837","3":"450","4":"-24","5":"93","6":"90","7":"-36","8":"32","9":"620"},"real_pay_margin":{"0":"56","1":"16","2":"837","3":"450","4":"-24","5":"93","6":"90","7":"-36","8":"32","9":"620"},"preferential_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"after_sale_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"deliver_total_amt":{"0":"200","1":"50","2":"2214","3":"1050","4":"4","5":"246","6":"210","7":"6","8":"100","9":"1640"},"ds":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915","5":"20250915","6":"20250915","7":"20250915","8":"20250915","9":"20250915"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   spu_id |
|:------|---------------:|---------:|
| count |           5937 |  5937    |
| mean  |             38 |  6610.16 |
| std   |              0 |  3302.83 |
| min   |             38 |  1528    |
| 25%   |             38 |  4145    |
| 50%   |             38 |  5779    |
| 75%   |             38 |  9260    |
| max   |             38 | 18589    |