# app_trd_city_sku_price_df
* comment: 定价模型效果监控
* last_data_modified_time: 2025-09-18 02:48:49

# schema:
CREATE TABLE summerfarm_tech.`app_trd_city_sku_price_df` (
  `order_date` STRING COMMENT '日期',
  `area_no` STRING COMMENT '运营服务区',
  `area_name` STRING COMMENT '运营服务区名称',
  `sku_id` STRING COMMENT 'sku',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品规格',
  `real_price` DECIMAL(38,18) COMMENT '平均实付价',
  `sku_cnt` BIGINT COMMENT '销量',
  `cost_price` DECIMAL(38,18) COMMENT '平均成本价',
  `gross_profit` DECIMAL(38,18) COMMENT '总毛利润',
  `prediction_price` DECIMAL(38,18) COMMENT '预测实付价格',
  `prediction_price_max` DECIMAL(38,18) COMMENT '预测实付价格上限',
  `prediction_price_min` DECIMAL(38,18) COMMENT '预测实付价格下限',
  `prediction_sku_cnt` DECIMAL(38,18) COMMENT '预测商品销量',
  `prediction_sku_cnt_max` DECIMAL(38,18) COMMENT '预测商品销量上限',
  `prediction_sku_cnt_min` DECIMAL(38,18) COMMENT '预测商品销量下限',
  `prediction_profit` DECIMAL(38,18) COMMENT '预测商品毛利润',
  `prediction_profit_max` DECIMAL(38,18) COMMENT '预测商品毛利润上限',
  `prediction_profit_min` DECIMAL(38,18) COMMENT '预测商品毛利润下限'
)
COMMENT '定价模型效果监控'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"order_date":{"0":"20240311","1":"20240312","2":"20240313","3":"20240315","4":"20240315","5":"20240316","6":"20240317","7":"20240318","8":"20240319","9":"20240319"},"area_no":{"0":"9585","1":"44125","2":"9585","3":"9585","4":"44125","5":"44125","6":"9585","7":"9585","8":"9585","9":"9585"},"area_name":{"0":"苏州","1":"武汉普冷","2":"苏州","3":"苏州","4":"武汉普冷","5":"武汉普冷","6":"苏州","7":"苏州","8":"苏州","9":"苏州"},"sku_id":{"0":"T001S01H001","1":"60552504355","2":"3816076886","3":"K001N01Z001","4":"N001S01R002","5":"60552504355","6":"3816076886","7":"3816076886","8":"3803673132","9":"N001S01R005"},"spu_name":{"0":"TS韩国幼砂糖","1":"纽麦福新西兰稀奶油","2":"日清山茶花高筋粉","3":"kiri奶油奶酪","4":"爱乐薇(铁塔)淡奶油","5":"纽麦福新西兰稀奶油","6":"日清山茶花高筋粉","7":"日清山茶花高筋粉","8":"王后精制低筋粉","9":"安佳淡奶油"},"sku_disc":{"0":"30KG*1包","1":"1L*12瓶","2":"25KG*1包","3":"1KG*1块","4":"1L*12盒","5":"1L*12瓶","6":"25KG*1包","7":"25KG*1包","8":"25KG*1包","9":"1L*12瓶"},"real_price":{"0":"None","1":"None","2":"None","3":"None","4":"None","5":"None","6":"None","7":"None","8":"None","9":"None"},"sku_cnt":{"0":"nan","1":"nan","2":"nan","3":"nan","4":"nan","5":"nan","6":"nan","7":"nan","8":"nan","9":"nan"},"cost_price":{"0":"252.9","1":"407.77","2":"305","3":"80","4":"528.39","5":"None","6":"308","7":"308","8":"137","9":"456"},"gross_profit":{"0":"None","1":"None","2":"None","3":"None","4":"None","5":"None","6":"None","7":"None","8":"None","9":"None"},"prediction_price":{"0":"275","1":"435","2":"320.8","3":"None","4":"565.0377777777778","5":"None","6":"None","7":"None","8":"None","9":"514.4444444444445"},"prediction_price_max":{"0":"302.5","1":"478.5000000000001","2":"352.8800000000001","3":"None","4":"621.5415555555556","5":"None","6":"None","7":"None","8":"None","9":"565.8888888888889"},"prediction_price_min":{"0":"247.5","1":"391.5","2":"288.72","3":"None","4":"508.534","5":"None","6":"None","7":"None","8":"None","9":"463"},"prediction_sku_cnt":{"0":"3.470410107819521","1":"2.603333333333333","2":"5.108971797895946","3":"None","4":"6.605156586221524","5":"None","6":"None","7":"None","8":"None","9":"7.311938518986713"},"prediction_sku_cnt_max":{"0":"8.386445065064132","1":"4.676666666666667","2":"8.892666666666667","3":"None","4":"11.2307196275384","5":"None","6":"None","7":"None","8":"None","9":"18.18519035971574"},"prediction_sku_cnt_min":{"0":"3.715941763513655","1":"2.977037037037037","2":"3.037382159994003","3":"None","4":"4.351467165939708","5":"None","6":"None","7":"None","8":"None","9":"5.43153392095807"},"prediction_profit":{"0":"95.43627796503682","1":"96.92210000000006","2":"112.3973795537108","3":"None","4":"241.8147826215701","5":"None","6":"None","7":"None","8":"None","9":"371.355948001909"},"prediction_profit_max":{"0":"221.0985349290625","1":"210.5658296296299","2":"136.317711340531","3":"None","4":"405.345935456209","5":"None","6":"None","7":"None","8":"None","9":"596.8652275363925"},"prediction_profit_min":{"0":"37.7390027927886","1":"-76.08936666666658","2":"-171.4506133333331","3":"None","4":"-222.9971689244018","5":"None","6":"None","7":"None","8":"None","9":"127.2963325180102"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |    sku_cnt |
|:------|-----------:|
| count | 5174       |
| mean  |    8.87998 |
| std   |   28.719   |
| min   |    1       |
| 25%   |    2       |
| 50%   |    4       |
| 75%   |   11       |
| max   | 1769       |