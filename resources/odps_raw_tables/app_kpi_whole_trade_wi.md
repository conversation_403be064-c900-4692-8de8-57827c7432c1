# app_kpi_whole_trade_wi
* comment: 交易kpi金额汇总
* last_data_modified_time: 2025-09-18 02:35:34

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_whole_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `cust_group` STRING COMMENT '客户类型:大客户、平台客户、批发客户、ALL',
  `sku_type` STRING COMMENT '商品类型:自营、代仓、全品类、SAAS客户自营、ALL',
  `category` STRING COMMENT '商品类目:鲜果、乳制品、其他、ALL',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额'
)
COMMENT '交易kpi金额汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"cust_group":{"0":"ALL","1":"ALL","2":"ALL","3":"ALL","4":"ALL"},"sku_type":{"0":"ALL","1":"ALL","2":"ALL","3":"ALL","4":"代仓"},"category":{"0":"ALL","1":"乳制品","2":"其他","3":"鲜果","4":"ALL"},"origin_total_amt":{"0":"17997457.24","1":"8079124.39","2":"6313294.82","3":"3605038.03","4":"4354402.1"},"real_total_amt":{"0":"17577296.9","1":"7876626.27","2":"6212970.37","3":"3487700.26","4":"4354402.1"},"cust_cnt":{"0":"19309","1":"7864","2":"8164","3":"12877","4":"482"},"order_cnt":{"0":"29194","1":"9500","2":"10376","3":"18339","4":"956"},"timing_origin_total_amt":{"0":"735704","1":"575964","2":"159740","3":"0","4":"0"},"after_sale_noreceived_amt":{"0":"541864.05","1":"365561.36","2":"108514.84","3":"67787.85","4":"9792.04"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   order_cnt |
|:------|---------------:|-----------:|------------:|
| count |             57 |      57    |       57    |
| mean  |             38 |    3564.35 |     4865.11 |
| std   |              0 |    5371.4  |     7654.73 |
| min   |             38 |       1    |        1    |
| 25%   |             38 |      78    |      105    |
| 50%   |             38 |     452    |      502    |
| 75%   |             38 |    6549    |     7859    |
| max   |             38 |   19309    |    29194    |