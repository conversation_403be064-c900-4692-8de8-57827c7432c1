# app_kpi_cust_trade_mi
* comment: 交易口径kpi指标月汇总
* last_data_modified_time: 2025-09-18 02:55:42

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_cust_trade_mi` (
  `month` STRING COMMENT '月份',
  `cust_class` STRING COMMENT '客户类型:大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `target_origin_total_amt` DECIMAL(38,18) COMMENT '目标应付总金额',
  `target_cust_cnt` BIGINT COMMENT '目标客户数',
  `target_cust_arpu` DECIMAL(38,18) COMMENT '目标ARPU',
  `target_order_cnt` BIGINT COMMENT '目标订单数',
  `target_after_sale_rate` DECIMAL(38,18) COMMENT '目标退货率',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标月汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509"},"cust_class":{"0":"Mars大客户","1":"平台客户","2":"集团大客户（茶百道）"},"origin_total_amt":{"0":"1010584.51","1":"67642705.1","2":"81441.82"},"real_total_amt":{"0":"1009884.98","1":"65476143.81","2":"78586.16"},"cust_cnt":{"0":"624","1":"44797","2":"142"},"cust_arpu":{"0":"1619.526458333333333333","1":"1509.982925195883652923","2":"573.533943661971830986"},"order_cnt":{"0":"2917","1":"151718","2":"387"},"order_avg":{"0":"346.44652382584847446","1":"445.84495643232839874","2":"210.443979328165374677"},"after_sale_noreceived_amt":{"0":"13068.99","1":"2404173.26","2":"3584.7"},"after_sale_rate":{"0":"0.012932109952882614","1":"0.035542240016063462","2":"0.04401547018472819"},"target_origin_total_amt":{"0":"0","1":"0","2":"0"},"target_cust_cnt":{"0":"0","1":"0","2":"0"},"target_cust_arpu":{"0":"0","1":"0","2":"0"},"target_order_cnt":{"0":"0","1":"0","2":"0"},"target_after_sale_rate":{"0":"0","1":"0","2":"0"},"dire_origin_total_amt":{"0":"0","1":"0","2":"0"},"delivery_amt":{"0":"5070","1":"267533.93","2":"300"},"timing_origin_total_amt":{"0":"0","1":"5378500","2":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   target_cust_cnt |   target_order_cnt |
|:------|-----------:|------------:|------------------:|-------------------:|
| count |        3   |         3   |                 3 |                  3 |
| mean  |    15187.7 |     51674   |                 0 |                  0 |
| std   |    25643.6 |     86649.9 |                 0 |                  0 |
| min   |      142   |       387   |                 0 |                  0 |
| 25%   |      383   |      1652   |                 0 |                  0 |
| 50%   |      624   |      2917   |                 0 |                  0 |
| 75%   |    22710.5 |     77317.5 |                 0 |                  0 |
| max   |    44797   |    151718   |                 0 |                  0 |