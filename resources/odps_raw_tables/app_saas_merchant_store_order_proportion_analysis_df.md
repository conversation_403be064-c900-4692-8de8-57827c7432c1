# app_saas_merchant_store_order_proportion_analysis_df
* comment: saas-门店订货占比分析
* last_data_modified_time: 2025-09-18 02:38:29

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_order_proportion_analysis_df` (
  `tenant_id` BIGINT COMMENT '租户id',
  `type` BIGINT COMMENT '1、周 2、月 3、季度',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `item_id` BIGINT COMMENT '商品id',
  `title` STRING COMMENT '商品名称',
  `store_id` BIGINT COMMENT '门店id',
  `store_type` BIGINT COMMENT '门店类型：0、直营店 1、加盟店 2、托管店',
  `store_group_name` STRING COMMENT '门店分组名',
  `order_amount` BIGINT COMMENT '订货数量',
  `order_amount_proportion` DECIMAL(38,18) COMMENT '订货数量占比(百分数)',
  `order_amount_proportion_upper_period` DECIMAL(38,18) COMMENT '订货数量占比环比(百分数)',
  `order_price` DECIMAL(38,18) COMMENT '订货金额',
  `order_price_proportion` DECIMAL(38,18) COMMENT '订货金额占比(百分数)',
  `order_price_proportion_upper_period` DECIMAL(38,18) COMMENT '订货金额占比环比(百分数)',
  `total_order_amount` BIGINT COMMENT '总订货数量',
  `total_order_price` DECIMAL(38,18) COMMENT '总订货金额',
  `order_amount_upper_period` BIGINT COMMENT '上周期订货数量',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '上周期订货金额',
  `total_order_amount_upper_period` BIGINT COMMENT '上周期总订货数量',
  `total_order_price_upper_period` DECIMAL(38,18) COMMENT '上周期总订货金额'
)
COMMENT 'saas-门店订货占比分析'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"type":{"0":"3","1":"3","2":"3","3":"3","4":"3"},"time_tag":{"0":"20220401","1":"20220401","2":"20220401","3":"20220401","4":"20220401"},"item_id":{"0":"1","1":"3","2":"9","3":"16","4":"17"},"title":{"0":"大草莓","1":"爱护植脂甜奶油","2":"玉菇甜瓜","3":"蒙特瑞草莓","4":"台农芒果"},"store_id":{"0":"5","1":"3","2":"1","3":"1","4":"1"},"store_type":{"0":"2","1":"0","2":"0","3":"0","4":"0"},"store_group_name":{"0":"成都","1":"默认分组","2":"默认分组","3":"默认分组","4":"默认分组"},"order_amount":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"order_amount_proportion":{"0":"11.11111111111111","1":"11.11111111111111","2":"11.11111111111111","3":"11.11111111111111","4":"11.11111111111111"},"order_amount_proportion_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_price":{"0":"0.01","1":"0.02","2":"26","3":"27","4":"58"},"order_price_proportion":{"0":"0.0050753692331117","1":"0.0101507384662234","2":"13.1959600060904431","3":"13.703496929401614","4":"29.4371415520479115"},"order_price_proportion_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"total_order_amount":{"0":"9","1":"9","2":"9","3":"9","4":"9"},"total_order_price":{"0":"197.03","1":"197.03","2":"197.03","3":"197.03","4":"197.03"},"order_amount_upper_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_price_upper_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"total_order_amount_upper_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"total_order_price_upper_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   type |   item_id |   store_id |   store_type |   order_amount |   total_order_amount |   order_amount_upper_period |   total_order_amount_upper_period |
|:------|------------:|-------:|----------:|-----------:|-------------:|---------------:|---------------------:|----------------------------:|----------------------------------:|
| count | 10000       |  10000 |  10000    |  10000     | 10000        |      10000     |              10000   |                 10000       |                          10000    |
| mean  |     4.8254  |      3 |    240.24 |    264.096 |     0.9028   |          8.762 |              26340.3 |                     1.8529  |                           2440.84 |
| std   |     1.25406 |      0 |    123.25 |    190.427 |     0.337585 |         20.324 |              16305.5 |                     8.56285 |                           5018.46 |
| min   |     2       |      3 |      1    |      1     |     0        |          1     |                  4   |                     0       |                              0    |
| 25%   |     4       |      3 |    162    |     99     |     1        |          1     |              11589   |                     0       |                              0    |
| 50%   |     4       |      3 |    230    |    232     |     1        |          2     |              14468   |                     0       |                              0    |
| 75%   |     6       |      3 |    276    |    375     |     1        |          7     |              44033   |                     0       |                              0    |
| max   |     8       |      3 |    889    |    801     |     2        |        350     |              44033   |                   350       |                          14468    |