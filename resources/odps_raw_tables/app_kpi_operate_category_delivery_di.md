# app_kpi_operate_category_delivery_di
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:40:07

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_category_delivery_di` (
  `date` STRING COMMENT '日期',
  `category` STRING COMMENT '鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917"},"category":{"0":"乳制品","1":"其他","2":"鲜果"},"origin_total_amt":{"0":"2253212.57","1":"656624.15","2":"1013098.88"},"real_total_amt":{"0":"2182362.985238095238095271","1":"627009.606666666666666672","2":"973718.520000000000000046"},"marketing_amt":{"0":"70849.584761904761904729","1":"29614.543333333333333328","2":"39380.359999999999999954"},"cost_amt":{"0":"2099094.26","1":"537665","2":"749350.17"},"origin_gross":{"0":"154118.31","1":"118959.15","2":"263748.71"},"real_gross":{"0":"83268.725238095238095271","1":"89344.606666666666666672","2":"224368.350000000000000046"},"origin_gross_margin":{"0":"0.068399365444690378","1":"0.181167795914908095","2":"0.260338566359879896"},"real_gross_margin":{"0":"0.038155304961337878","1":"0.142493202204100201","2":"0.230424240056561726"},"cust_cnt":{"0":"2681","1":"2496","2":"5090"},"point_cnt":{"0":"2741","1":"2546","2":"5266"},"origin_pre_cust_price":{"0":"840.437362924281984334","1":"263.070572916666666667","2":"199.037108055009823183"},"real_pre_cust_price":{"0":"814.010811353261931404","1":"251.205771901709401709","2":"191.300298624754420432"},"timing_origin_amt":{"0":"192044","1":"61242","2":"0"},"timing_real_amt":{"0":"180678.59523809523809524","1":"56600.666666666666666667","2":"0"},"consign_origin_amt":{"0":"0","1":"0","2":"0"},"consign_real_amt":{"0":"0","1":"0","2":"0"},"consign_marketing_amt":{"0":"0","1":"0","2":"0"},"consign_origin_gross":{"0":"0","1":"0","2":"0"},"consign_real_gross":{"0":"0","1":"0","2":"0"},"consign_cust_cnt":{"0":"0","1":"0","2":"0"},"turnover_day_cnt":{"0":"16.20570114887246476","1":"22.389170727119910933","2":"1.93487823898715438"},"damage_amt":{"0":"61.35","1":"922.34","2":"4999.44"},"storage_amt":{"0":"29325.028984121573194182","1":"21274.012468209395712377","2":"31009.113974495162530855"},"arterial_roads_amt":{"0":"25239.188794936919819011","1":"18779.63271371833380616","2":"25423.162505158168493032"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"allocation_amt":{"0":"5356.918007027113903245","1":"3768.247009971903430097","2":"4542.742401833346679994"},"other_amt":{"0":"3769.506981466155815919","1":"2502.578715038862361537","2":"4166.931889659549544163"},"deliver_amt":{"0":"90229.816432614780479781","1":"65367.347561975106462968","2":"100296.731632054272543888"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |       3    |        3    |                  3 |
| mean  |    3422.33 |     3517.67 |                  0 |
| std   |    1447.2  |     1517.24 |                  0 |
| min   |    2496    |     2546    |                  0 |
| 25%   |    2588.5  |     2643.5  |                  0 |
| 50%   |    2681    |     2741    |                  0 |
| 75%   |    3885.5  |     4003.5  |                  0 |
| max   |    5090    |     5266    |                  0 |