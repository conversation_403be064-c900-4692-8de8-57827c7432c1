# app_sale_city_kpi_trade_mi
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:27:12

# schema:
CREATE TABLE summerfarm_tech.`app_sale_city_kpi_trade_mi` (
  `month` STRING COMMENT '月份',
  `administrative_city` STRING COMMENT '注册行政城市',
  `zone_name` STRING COMMENT '销售区域名称',
  `m1` STRING COMMENT '城市负责人（M1）',
  `m2` STRING COMMENT '区域负责人（M2）',
  `m3` STRING COMMENT '部门负责人（M3）',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `at_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约应付总金额',
  `at_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付总金额',
  `at_delivery_cust_cnt` BIGINT COMMENT '(乳品)安佳铁塔履约活跃客户数',
  `at_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付毛利润',
  `noat_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约应付总金额',
  `noat_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付总金额',
  `noat_delivery_cust_cnt` BIGINT COMMENT '(乳品)非安佳铁塔履约活跃客户数',
  `noat_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付毛利润',
  `timing_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额',
  `timing_delivery_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额',
  `timing_delivery_cust_cnt` BIGINT COMMENT '省心送履约活跃客户数',
  `timing_delivery_real_profit` DECIMAL(38,18) COMMENT '省心送履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"administrative_city":{"0":"铜陵市","1":"贵阳市","2":"None","3":"舟山市","4":"昆明市"},"zone_name":{"0":"徽京","1":"贵阳","2":"无","3":"杭州湾","4":"昆明"},"m1":{"0":"冯朝皇","1":"汪林俊","2":"无","3":"翟远方","4":"蒋柳选"},"m2":{"0":"桂少达","1":"姜浪","2":"无","3":"翟远方","4":"孙日达"},"m3":{"0":"孙日达","1":"孙日达","2":"无","3":"吕建杰","4":"孙日达"},"order_origin_total_amt":{"0":"30965.6","1":"487228.74","2":"0","3":"85065.87","4":"728231.45"},"order_real_total_amt":{"0":"30286.3","1":"475746.77","2":"0","3":"82371.63","4":"699729.75"},"order_cust_cnt":{"0":"19","1":"285","2":"0","3":"54","4":"286"},"order_cust_arpu":{"0":"1629.768421052631578947","1":"1709.574526315789473684","2":"0","3":"1575.293888888888888889","4":"2546.263811188811188811"},"order_cnt":{"0":"44","1":"709","2":"0","3":"172","4":"637"},"delivery_origin_total_amt":{"0":"37538.8","1":"447824.04","2":"0","3":"88613.54","4":"698590.39"},"delivery_real_total_amt":{"0":"36632.3","1":"437624.660000000000000002","2":"0","3":"86015.301111111111111115","4":"671038.650000000000000007"},"delivery_cust_cnt":{"0":"21","1":"287","2":"0","3":"54","4":"290"},"delivery_origin_profit":{"0":"2965.18","1":"38689.54","2":"0","3":"11258.3","4":"59207.77"},"delivery_real_profit":{"0":"2058.68","1":"28490.160000000000000002","2":"0","3":"8660.061111111111111115","4":"31656.030000000000000007"},"delivery_after_profit":{"0":"-1120.03398124507806384","1":"1337.62253442022042598","2":"-1165.834828533855429069","3":"2678.060636969155022271","4":"-2618.364620914158778974"},"delivery_days_avg":{"0":"2.047619047619047","1":"2.191637630662021","2":"0","3":"2.462962962962963","4":"1.968965517241379"},"delivery_point_cnt":{"0":"43","1":"645","2":"0","3":"137","4":"584"},"delivery_amt":{"0":"3178.71398124507806384","1":"27152.537465579779574022","2":"1165.834828533855429069","3":"5982.000474141956088844","4":"34274.394620914158778981"},"new_delivery_origin_total_amt":{"0":"30","1":"7946.52","2":"0","3":"0","4":"7385"},"new_delivery_real_total_amt":{"0":"15.500000000000000001","1":"7563.520000000000000001","2":"0","3":"0","4":"7016"},"new_delivery_cust_cnt":{"0":"1","1":"18","2":"0","3":"0","4":"8"},"new_delivery_real_profit":{"0":"1.880000000000000001","1":"440.830000000000000001","2":"0","3":"0","4":"462.01"},"old_delivery_origin_total_amt":{"0":"37508.8","1":"439877.52","2":"0","3":"88613.54","4":"691205.39"},"old_delivery_real_total_amt":{"0":"36616.799999999999999999","1":"430061.140000000000000001","2":"0","3":"86015.301111111111111115","4":"664022.650000000000000007"},"old_delivery_cust_cnt":{"0":"20","1":"269","2":"0","3":"54","4":"282"},"old_delivery_real_profit":{"0":"2056.799999999999999999","1":"28049.330000000000000001","2":"0","3":"8660.061111111111111115","4":"31194.020000000000000007"},"at_delivery_origin_total_amt":{"0":"11861","1":"98841.1","2":"0","3":"38713","4":"202343"},"at_delivery_real_total_amt":{"0":"11711","1":"97455.46","2":"0","3":"38095.13","4":"198660"},"at_delivery_cust_cnt":{"0":"11","1":"89","2":"0","3":"17","4":"98"},"at_delivery_real_profit":{"0":"159.05","1":"341.1","2":"0","3":"454.98","4":"-602.8"},"noat_delivery_origin_total_amt":{"0":"17687","1":"226976","2":"0","3":"17711","4":"381568"},"noat_delivery_real_total_amt":{"0":"17135.4","1":"220866.86","2":"0","3":"16959.34111111111111111","4":"365904.450000000000000005"},"noat_delivery_cust_cnt":{"0":"14","1":"147","2":"0","3":"20","4":"165"},"noat_delivery_real_profit":{"0":"688.47","1":"12784.61","2":"0","3":"1311.56111111111111111","4":"19105.860000000000000005"},"timing_delivery_origin_total_amt":{"0":"951","1":"5157","2":"0","3":"2779","4":"32873"},"timing_delivery_real_total_amt":{"0":"855","1":"4672","2":"0","3":"2515.11111111111111111","4":"28871.7"},"timing_delivery_cust_cnt":{"0":"1","1":"5","2":"0","3":"3","4":"10"},"timing_delivery_real_profit":{"0":"14.49","1":"475","2":"0","3":"184.11111111111111111","4":"2479.79"},"order_sku_cnt":{"0":"54","1":"185","2":"0","3":"129","4":"177"},"order_sku_weight":{"0":"1246.91","1":"20864.42000000001","2":"0","3":"4457.48","4":"29924.34"},"delivery_sku_cnt":{"0":"61","1":"185","2":"0","3":"135","4":"180"},"delivery_sku_weight":{"0":"1497.67","1":"19964.72","2":"0","3":"4591.609999999999","4":"29551.55000000001"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   order_cust_cnt |   order_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   at_delivery_cust_cnt |   noat_delivery_cust_cnt |   timing_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|-----------------:|------------:|--------------------:|---------------------:|------------------------:|------------------------:|-----------------------:|-------------------------:|---------------------------:|----------------:|-------------------:|
| count |          186     |     186     |             186     |              186     |                186      |                 186     |               186      |                  186     |                  186       |         186     |            186     |
| mean  |          240.844 |     815.688 |             240.527 |              713.124 |                 12.1559 |                 228.371 |                57.7312 |                  100.946 |                    9.95161 |         167.613 |            168.091 |
| std   |          429.882 |    1654.58  |             428.424 |             1447.37  |                 21.903  |                 408.151 |                99.2235 |                  182.002 |                   20.3821  |         191.409 |            191.553 |
| min   |            0     |       0     |               0     |                0     |                  0      |                   0     |                 0      |                    0     |                    0       |           0     |              0     |
| 25%   |            2     |       3     |               2     |                3     |                  0      |                   2     |                 0      |                    0     |                    0       |           2.25  |              2.25  |
| 50%   |           55     |     152     |              54.5   |              130.5   |                  2      |                  54     |                13      |                   19.5   |                    2       |          99     |            102.5   |
| 75%   |          285.75  |     833.25  |             286     |              701.5   |                 15      |                 266.25  |                73.75   |                  122.75  |                   11       |         297.25  |            296.75  |
| max   |         2709     |   12690     |            2698     |            10940     |                142      |                2556     |               574      |                 1143     |                  175       |         770     |            776     |