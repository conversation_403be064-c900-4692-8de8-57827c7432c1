# app_saas_order_statement_summary_di
* comment: SAAS对账单-对账单概要表（近15天数据）
* last_data_modified_time: 2025-09-18 02:52:50

# schema:
CREATE TABLE summerfarm_tech.`app_saas_order_statement_summary_di` (
  `tenant_id` BIGINT COMMENT '租户Id',
  `time_tag` STRING COMMENT '时间标签',
  `total_price` DECIMAL(38,18) COMMENT '总金额',
  `wechat_pay_total_price` DECIMAL(38,18) COMMENT '总金额（微信支付）',
  `bill_balance_pay_total_price` DECIMAL(38,18) COMMENT '总金额（账期 + 余额支付）',
  `supply_total_price` DECIMAL(38,18) COMMENT '直供货品总金额',
  `supply_delivery_fee` DECIMAL(38,18) COMMENT '供应商运费',
  `refund_price_deducted_supply` DECIMAL(38,18) COMMENT '等比换算后采购售后金额合计',
  `total_difference` DECIMAL(38,18) COMMENT '商品售价与采购价倒挂差额',
  `goods_agent_fee` DECIMAL(38,18) COMMENT '代仓费',
  `goods_agent_refund_fee_supply_responsibility` DECIMAL(38,18) COMMENT '售后明细表_已到货售后_鲜沐责任_代仓费用合计',
  `sales_and_supply_difference` DECIMAL(38,18) COMMENT '鲜沐直供货品_售价与采购价差额总计（未剔售后）',
  `refund_sales_and_supply_difference` DECIMAL(38,18) COMMENT '有退款的_鲜沐直供货品的售价与采购差额总计',
  `gross_profit` DECIMAL(38,18) COMMENT '鲜沐直供货品售价与采购价差额总计（毛利润）',
  `wechat_pay_sales_and_supply_difference_deducted_refund` DECIMAL(38,18) COMMENT '销售与采购差额（剔除售后）（微信支付）',
  `bill_balance_pay_sales_and_supply_difference_deducted_refund` DECIMAL(38,18) COMMENT '销售与采购差额(剔除售后)(账期支付+余额支付)',
  `order_count` BIGINT COMMENT '订单数',
  `order_item_count` BIGINT COMMENT 'sku订单件数合计',
  `wechat_pay_total_price_deducted_delivery_fee` DECIMAL(38,18) COMMENT '采购应付总计（不含配送费）（微信支付）',
  `bill_balance_pay_total_price_deducted_delivery_fee` DECIMAL(38,18) COMMENT '采购应付总计（不含配送费）（账期和余额支付）',
  `wechat_pay_supply_delivery_fee` DECIMAL(38,18) COMMENT '供应商配送费（微信支付）',
  `bill_balance_pay_supply_delivery_fee` DECIMAL(38,18) COMMENT '供应商配送费（账期和余额支付）'
)
COMMENT 'SAAS对账单-对账单概要表（近15天数据）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"7","1":"8","2":"13","3":"14","4":"32"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"total_price":{"0":"0","1":"1700.89","2":"25786.53","3":"22220.26","4":"2468"},"wechat_pay_total_price":{"0":"0","1":"641.61","2":"15866.49","3":"22220.26","4":"2468"},"bill_balance_pay_total_price":{"0":"0","1":"1059.28","2":"9920.04","3":"0","4":"0"},"supply_total_price":{"0":"0","1":"964.43","2":"27850.56","3":"22121.4","4":"2468"},"supply_delivery_fee":{"0":"0","1":"0","2":"70","3":"340","4":"0"},"refund_price_deducted_supply":{"0":"0","1":"0","2":"2134.03","3":"241.14","4":"0"},"total_difference":{"0":"0","1":"0","2":"0","3":"0","4":"-6842"},"goods_agent_fee":{"0":"0","1":"736.46","2":"0","3":"0","4":"0"},"goods_agent_refund_fee_supply_responsibility":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"sales_and_supply_difference":{"0":"0","1":"18","2":"0","3":"1784.3","4":"92"},"refund_sales_and_supply_difference":{"0":"0","1":"0","2":"0","3":"19.3","4":"0"},"gross_profit":{"0":"0","1":"18","2":"0","3":"1765","4":"92"},"wechat_pay_sales_and_supply_difference_deducted_refund":{"0":"0","1":"0","2":"0","3":"1765","4":"92"},"bill_balance_pay_sales_and_supply_difference_deducted_refund":{"0":"0","1":"18","2":"0","3":"0","4":"0"},"order_count":{"0":"11","1":"24","2":"101","3":"113","4":"52"},"order_item_count":{"0":"28","1":"122","2":"357","3":"662","4":"510"},"wechat_pay_total_price_deducted_delivery_fee":{"0":"0","1":"641.61","2":"15826.49","3":"21880.26","4":"2468"},"bill_balance_pay_total_price_deducted_delivery_fee":{"0":"0","1":"1059.28","2":"9890.04","3":"0","4":"0"},"wechat_pay_supply_delivery_fee":{"0":"0","1":"0","2":"40","3":"340","4":"0"},"bill_balance_pay_supply_delivery_fee":{"0":"0","1":"0","2":"30","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   order_count |   order_item_count |
|:------|------------:|--------------:|-------------------:|
| count |     29      |       29      |             29     |
| mean  |     74.8621 |       25.6207 |            157.483 |
| std   |     37.7924 |       41.1482 |            192.197 |
| min   |      7      |        1      |              1     |
| 25%   |     47      |        7      |             34     |
| 50%   |     88      |       12      |             80     |
| 75%   |    105      |       23      |            165     |
| max   |    123      |      190      |            731     |