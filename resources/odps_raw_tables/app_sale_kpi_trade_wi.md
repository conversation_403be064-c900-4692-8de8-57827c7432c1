# app_sale_kpi_trade_wi
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:47:41

# schema:
CREATE TABLE summerfarm_tech.`app_sale_kpi_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025"},"week_of_year":{"0":"38"},"monday":{"0":"20250915"},"sunday":{"0":"20250921"},"order_origin_total_amt":{"0":"11324368.66"},"order_real_total_amt":{"0":"10928100.35"},"order_cust_cnt":{"0":"17816"},"order_cust_arpu":{"0":"635.629134485855410867"},"order_cnt":{"0":"25674"},"delivery_origin_total_amt":{"0":"11022871.06"},"delivery_real_total_amt":{"0":"10667885.0736813186813188"},"delivery_cust_cnt":{"0":"18210"},"delivery_origin_profit":{"0":"1529836.65"},"delivery_real_profit":{"0":"1174850.6636813186813188"},"delivery_after_profit":{"0":"53987.51894838898909446"},"delivery_days_avg":{"0":"1.209335529928611"},"delivery_point_cnt":{"0":"22919"},"delivery_amt":{"0":"1120863.14473292969222434"},"new_delivery_origin_total_amt":{"0":"353591.34"},"new_delivery_real_total_amt":{"0":"339325.620000000000000002"},"new_delivery_cust_cnt":{"0":"832"},"new_delivery_real_profit":{"0":"37866.790000000000000002"},"old_delivery_origin_total_amt":{"0":"10669279.72"},"old_delivery_real_total_amt":{"0":"10328559.453681318681318798"},"old_delivery_cust_cnt":{"0":"17378"},"old_delivery_real_profit":{"0":"1136983.873681318681318798"},"order_sku_cnt":{"0":"1233"},"order_sku_weight":{"0":"643896.9999999998"},"delivery_sku_cnt":{"0":"1238"},"delivery_sku_weight":{"0":"634142.96"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   order_cust_cnt |   order_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|---------------:|-----------------:|------------:|--------------------:|---------------------:|------------------------:|------------------------:|----------------:|-------------------:|
| count |              1 |                1 |           1 |                   1 |                    1 |                       1 |                       1 |               1 |                  1 |
| mean  |             38 |            17816 |       25674 |               18210 |                22919 |                     832 |                   17378 |            1233 |               1238 |
| std   |            nan |              nan |         nan |                 nan |                  nan |                     nan |                     nan |             nan |                nan |
| min   |             38 |            17816 |       25674 |               18210 |                22919 |                     832 |                   17378 |            1233 |               1238 |
| 25%   |             38 |            17816 |       25674 |               18210 |                22919 |                     832 |                   17378 |            1233 |               1238 |
| 50%   |             38 |            17816 |       25674 |               18210 |                22919 |                     832 |                   17378 |            1233 |               1238 |
| 75%   |             38 |            17816 |       25674 |               18210 |                22919 |                     832 |                   17378 |            1233 |               1238 |
| max   |             38 |            17816 |       25674 |               18210 |                22919 |                     832 |                   17378 |            1233 |               1238 |