# app_m1_target_achieve_rate_mi
* comment: 销售M1维度目标达成绩效得分_月度算薪
* last_data_modified_time: 2025-09-18 04:29:04

# schema:
CREATE TABLE summerfarm_tech.`app_m1_target_achieve_rate_mi` (
  `months` STRING COMMENT '月份',
  `m3` STRING COMMENT '最新M3',
  `m2` STRING COMMENT '最新M2',
  `m1` STRING COMMENT '最新M1',
  `zone_name` STRING COMMENT '销售区域',
  `no_at_gmv_target` DECIMAL(38,18) COMMENT '非ATGMV目标',
  `no_at_gmv_achieve_amt` DECIMAL(38,18) COMMENT '非ATGMV达成',
  `no_at_gmv_achieve_rate` DECIMAL(38,18) COMMENT '非ATGMV达成率',
  `no_at_gmv_score_num` DECIMAL(38,18) COMMENT '非ATGMV得分',
  `fruit_gmv_target` DECIMAL(38,18) COMMENT '鲜果GMV目标',
  `fruit_gmv_achieve_amt` DECIMAL(38,18) COMMENT '鲜果GMV达成',
  `fruit_gmv_achieve_rate` DECIMAL(38,18) COMMENT '鲜果GMV达成率',
  `fruit_gmv_score_num` DECIMAL(38,18) COMMENT '鲜果GMV得分',
  `effective_cust_target` DECIMAL(38,18) COMMENT '有效月活目标',
  `effect_cust_cnt` DECIMAL(38,18) COMMENT '有效月活达成',
  `effect_cust_achieve_rate` DECIMAL(38,18) COMMENT '有效月活达成率',
  `effect_cust_score_num` DECIMAL(38,18) COMMENT '有效月活得分',
  `total_score_num` DECIMAL(38,18) COMMENT '综合得分'
)
COMMENT '销售M1维度目标达成绩效得分_月度算薪'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"months":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"m3":{"0":"吕建杰","1":"孙日达","2":"吕建杰","3":"孙日达","4":"孙日达"},"m2":{"0":"林金秋","1":"桂少达","2":"赵奎","3":"陈欲豪","4":"桂少达"},"m1":{"0":"韦贵丰","1":"冯朝皇","2":"陈露露","3":"张茂权","4":"葛世豪"},"zone_name":{"0":"广西","1":"徽京","2":"浦西","3":"深圳","4":"苏北"},"no_at_gmv_target":{"0":"1720000","1":"4310000","2":"6200000","3":"4350000","4":"3830000"},"no_at_gmv_achieve_amt":{"0":"904037.80999999999999999","1":"1878685.852761904761904835","2":"2983843.998571428571428575","3":"2241071.250000000000000002","4":"1712826.480000000000000005"},"no_at_gmv_achieve_rate":{"0":"0.525603377906976744","1":"0.435889989039885096","2":"0.481265161059907834","3":"0.515188793103448276","4":"0.447213180156657963"},"no_at_gmv_score_num":{"0":"0.367922364534883721","1":"0.305122992327919567","2":"0.336885612741935484","3":"0.360632155172413793","4":"0.313049226109660574"},"fruit_gmv_target":{"0":"190000","1":"1390000","2":"2380000","3":"850000","4":"1180000"},"fruit_gmv_achieve_amt":{"0":"86098.169999999999999984","1":"559437.710000000000000028","2":"975814.739999999999999978","3":"436825.920000000000000009","4":"457126.660000000000000002"},"fruit_gmv_achieve_rate":{"0":"0.453148263157894737","1":"0.402473172661870504","2":"0.410006193277310924","3":"0.513912847058823529","4":"0.387395474576271186"},"fruit_gmv_score_num":{"0":"0.090629652631578947","1":"0.080494634532374101","2":"0.082001238655462185","3":"0.102782569411764706","4":"0.077479094915254237"},"effective_cust_target":{"0":"939","1":"2650","2":"1770","3":"1920","4":"2220"},"effect_cust_cnt":{"0":"767","1":"2180","2":"1517","3":"1684","4":"1781"},"effect_cust_achieve_rate":{"0":"0.8168264110756124","1":"0.8226415094339623","2":"0.8570621468926554","3":"0.8770833333333333","4":"0.8022522522522523"},"effect_cust_score_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"total_score_num":{"0":"0.458552017166462668","1":"0.385617626860293668","2":"0.418886851397397669","3":"0.463414724584178499","4":"0.390528321024914811"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|        |   months | m3     | m2     | m1     | zone_name   |   no_at_gmv_target |   no_at_gmv_achieve_amt |   no_at_gmv_achieve_rate |   no_at_gmv_score_num |   fruit_gmv_target |   fruit_gmv_achieve_amt |   fruit_gmv_achieve_rate |   fruit_gmv_score_num |   effective_cust_target |   effect_cust_cnt |   effect_cust_achieve_rate |   effect_cust_score_num |   total_score_num |           ds |
|:-------|---------:|:-------|:-------|:-------|:------------|-------------------:|------------------------:|-------------------------:|----------------------:|-------------------:|------------------------:|-------------------------:|----------------------:|------------------------:|------------------:|---------------------------:|------------------------:|------------------:|-------------:|
| count  |       26 | 26     | 26     | 26     | 26          |           26       |                      26 |                26        |             26        |                 26 |                    26   |                26        |            26         |                      26 |                26 |                  26        |                      26 |         26        | 26           |
| unique |        1 | 2      | 9      | 25     | 26          |           24       |                      26 |                26        |             26        |                 26 |                    26   |                26        |            26         |                      24 |                26 |                  26        |                       1 |         26        |  1           |
| top    |   202509 | 吕建杰 | 陈欲豪 | 陈忠良 | 广西        |            4.6e+06 |                  904038 |                 0.525603 |              0.367922 |             190000 |                 86098.2 |                 0.453148 |             0.0906297 |                     400 |               767 |                   0.816826 |                       0 |          0.458552 |  2.02509e+07 |
| freq   |       26 | 13     | 5      | 2      | 1           |            2       |                       1 |                 1        |              1        |                  1 |                     1   |                 1        |             1         |                       2 |                 1 |                   1        |                      26 |          1        | 26           |