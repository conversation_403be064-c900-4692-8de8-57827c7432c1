# app_saas_brand_health_mi
* comment: saas品牌健康度表
* last_data_modified_time: 2025-09-18 02:35:12

# schema:
CREATE TABLE summerfarm_tech.`app_saas_brand_health_mi` (
  `month` STRING COMMENT '月份',
  `brand_alias` STRING COMMENT '品牌名称',
  `order_store_cnt` BIGINT COMMENT '交易门店数',
  `all_store_cnt` BIGINT COMMENT '注册门店数',
  `total_gmv` DECIMAL(38,18) COMMENT '总交易GMV',
  `store_gmv` DECIMAL(38,18) COMMENT '店均交易GMV',
  `order_days` BIGINT COMMENT '下单天数',
  `home_login_days` BIGINT COMMENT '后台登录天数',
  `is_supplier` STRING COMMENT '是否完善供应商创建',
  `is_supplier_sku` STRING COMMENT '是否关联供应商',
  `is_purchases` STRING COMMENT '当月是否下采购单',
  `is_after_sale` STRING COMMENT '当月是否有售后',
  `is_store_log` STRING COMMENT '当月是否使用仓库模块',
  `is_finance_log` STRING COMMENT '当月是否使用财务模块',
  `tenant_id` BIGINT COMMENT '租户id'
)
COMMENT 'saas品牌健康度表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"brand_alias":{"0":"本来不该有咖啡","1":"人在茶在","2":"川町太郎","3":"艾炒酸奶","4":"茶仙记"},"order_store_cnt":{"0":"0","1":"0","2":"64","3":"55","4":"0"},"all_store_cnt":{"0":"217","1":"52","2":"156","3":"99","4":"6"},"total_gmv":{"0":"0","1":"0","2":"169464","3":"282555.57","4":"0"},"store_gmv":{"0":"0","1":"0","2":"2647.875","3":"5137.374","4":"0"},"order_days":{"0":"0","1":"0","2":"17","3":"17","4":"0"},"home_login_days":{"0":"0","1":"0","2":"15","3":"17","4":"0"},"is_supplier":{"0":"是","1":"是","2":"是","3":"是","4":"否"},"is_supplier_sku":{"0":"是","1":"是","2":"是","3":"是","4":"是"},"is_purchases":{"0":"否","1":"否","2":"是","3":"是","4":"否"},"is_after_sale":{"0":"否","1":"否","2":"是","3":"是","4":"否"},"is_store_log":{"0":"否","1":"否","2":"是","3":"是","4":"否"},"is_finance_log":{"0":"否","1":"否","2":"是","3":"是","4":"否"},"tenant_id":{"0":"4","1":"6","2":"7","3":"8","4":"10"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   order_store_cnt |   all_store_cnt |   order_days |   home_login_days |   tenant_id |
|:------|------------------:|----------------:|-------------:|------------------:|------------:|
| count |           67      |          67     |     67       |          67       |     67      |
| mean  |           35.6269 |         158.299 |      7.83582 |           7.1791  |     65.4478 |
| std   |           80.8307 |         332.135 |      7.86553 |           7.63946 |     36.0224 |
| min   |            0      |           1     |      0       |           0       |      4      |
| 25%   |            0      |           7.5   |      0       |           0       |     39      |
| 50%   |            3      |          15     |      4       |           3       |     61      |
| 75%   |           30      |         139.5   |     17       |          16       |    100.5    |
| max   |          386      |        1735     |     17       |          17       |    123      |