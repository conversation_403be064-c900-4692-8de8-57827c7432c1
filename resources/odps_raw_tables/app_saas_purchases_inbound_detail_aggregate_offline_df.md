# app_saas_purchases_inbound_detail_aggregate_offline_df
* comment: 采购入库单 离线表
* last_data_modified_time: 2025-09-18 02:15:27

# schema:
CREATE TABLE summerfarm_tech.`app_saas_purchases_inbound_detail_aggregate_offline_df` (
  `inbound_time` DATETIME COMMENT '入库时间',
  `batch_no` STRING COMMENT '采购批次',
  `inbound_stock` BIGINT COMMENT '入库数量',
  `inbound_price` DECIMAL(38,18) COMMENT '入库总金额',
  `purchases_stock` BIGINT COMMENT '采购数量',
  `purchases_price` DECIMAL(38,18) COMMENT '采购金额',
  `sku_no` STRING COMMENT 'sku编号',
  `sku_name` STRING COMMENT 'sku名称',
  `specification` STRING COMMENT '规格',
  `packaging` STRING COMMENT '规格单位',
  `saas_sku_no` STRING COMMENT 'saas-sku编号',
  `saas_sku_name` STRING COMMENT 'saas-sku名称',
  `saas_specification` STRING COMMENT 'saas-规格',
  `saas_packaging` STRING COMMENT 'saas-规格单位',
  `inbound_create_user_id` BIGINT COMMENT '采购人',
  `inbound_create_user_name` STRING COMMENT '采购人名称',
  `inbound_create_user_phone` STRING COMMENT '采购人电话',
  `warehouse_id` BIGINT COMMENT '库存仓ID',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `tenant_id` BIGINT COMMENT '租户ID',
  `tenant_name` STRING COMMENT '租户名称',
  `supplier_id` BIGINT COMMENT '供应商ID',
  `supplier_name` STRING COMMENT '供应商名称',
  `supplier_type` BIGINT COMMENT '供应商类型'
)
COMMENT '采购入库单 离线表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"inbound_time":{"0":"2023-03-22 20:18:17","1":"2023-03-22 20:20:48","2":"2023-04-14 16:58:54","3":"2023-02-24 16:30:02","4":"2023-05-04 16:17:52"},"batch_no":{"0":"20230322178746126","1":"20230322178746126","2":"20230414162948070","3":"20230221162910081","4":"20230502162954090"},"inbound_stock":{"0":"1","1":"1","2":"20","3":"70","4":"20"},"inbound_price":{"0":"1","1":"1","2":"4000","3":"14000","4":"4000"},"purchases_stock":{"0":"2","1":"2","2":"20","3":"70","4":"20"},"purchases_price":{"0":"2","1":"2","2":"4000","3":"14000","4":"4000"},"sku_no":{"0":"1038832323467","1":"1038832323467","2":"1038101062745","3":"1038101062745","4":"1038101062745"},"sku_name":{"0":"测试导入商品","1":"测试导入商品","2":"700ml螺纹杯","3":"700ml螺纹杯","4":"700ml螺纹杯"},"specification":{"0":"3L*3瓶","1":"3L*3瓶","2":"700mL*300个","3":"700mL*300个","4":"700mL*300个"},"packaging":{"0":"包","1":"包","2":"箱","3":"箱","4":"箱"},"saas_sku_no":{"0":"101536","1":"101536","2":"101650","3":"101650","4":"101650"},"saas_sku_name":{"0":"测试导入商品","1":"测试导入商品","2":"700ml螺纹杯","3":"700ml螺纹杯","4":"700ml螺纹杯"},"saas_specification":{"0":"3L*3瓶","1":"3L*3瓶","2":"700mL*300个","3":"700mL*300个","4":"700mL*300个"},"saas_packaging":{"0":"包","1":"包","2":"箱","3":"箱","4":"箱"},"inbound_create_user_id":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"inbound_create_user_name":{"0":"1","1":"1","2":"人在茶在管理员","3":"人在茶在管理员","4":"人在茶在管理员"},"inbound_create_user_phone":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"warehouse_id":{"0":"60","1":"60","2":"10","3":"10","4":"10"},"warehouse_name":{"0":"昆明总仓","1":"昆明总仓","2":"嘉兴总仓","3":"嘉兴总仓","4":"嘉兴总仓"},"tenant_id":{"0":"2","1":"2","2":"6","3":"6","4":"6"},"tenant_name":{"0":"鲜沐商城","1":"鲜沐商城","2":"人在茶在","3":"人在茶在","4":"人在茶在"},"supplier_id":{"0":"1823","1":"1823","2":"1857","3":"1857","4":"1857"},"supplier_name":{"0":"大鹏测试","1":"大鹏测试","2":"上海洁鋆实业有限公司","3":"上海洁鋆实业有限公司","4":"上海洁鋆实业有限公司"},"supplier_type":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | inbound_time                  |   inbound_stock |   purchases_stock |   warehouse_id |   tenant_id |   supplier_id |   supplier_type |
|:------|:------------------------------|----------------:|------------------:|---------------:|------------:|--------------:|----------------:|
| count | 399                           |        399      |          399      |       399      |   399       |      399      |      399        |
| mean  | 2023-03-20 19:34:34.182957056 |         87.3258 |           93.3784 |        11.1604 |     7.12281 |     1839.92   |        1.27068  |
| min   | 2023-01-12 19:46:08           |          1      |            1      |         1      |     2       |     1823      |        0        |
| 25%   | 2023-02-27 15:02:32           |         20      |           40      |        10      |     6       |     1830      |        0        |
| 50%   | 2023-03-19 18:13:57           |         50      |           60      |        10      |     8       |     1830      |        2        |
| 75%   | 2023-04-15 16:59:32.500000    |        100      |          100      |        10      |     8       |     1842      |        2        |
| max   | 2023-05-07 18:20:30           |        720      |          720      |        62      |    12       |     1927      |        2        |
| std   | nan                           |         98.8063 |           96.4089 |         9.0559 |     1.66774 |       19.4307 |        0.954712 |