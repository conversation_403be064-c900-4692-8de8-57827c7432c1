# app_kpi_category_trade_wi
* comment: 交易口径kpi指标周汇总
* last_data_modified_time: 2025-09-18 02:53:01

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_category_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标周汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025"},"week_of_year":{"0":"38","1":"38","2":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921"},"category":{"0":"乳制品","1":"其他","2":"鲜果"},"origin_total_amt":{"0":"6469327.03","1":"1995865.99","2":"2992487.73"},"real_total_amt":{"0":"6274617.03","1":"1908205.57","2":"2878327.73"},"cust_cnt":{"0":"7155","1":"6543","2":"12559"},"cust_arpu":{"0":"904.168697414395527603","1":"305.038360079474247287","2":"238.27436340472967593"},"order_cnt":{"0":"8473","1":"7840","2":"17383"},"order_avg":{"0":"763.52260474448247374","1":"254.574743622448979592","2":"172.150246217568889145"},"after_sale_noreceived_amt":{"0":"353561.48","1":"79220.61","2":"62067.28"},"after_sale_rate":{"0":"0.054651972046001205","1":"0.039692349284432669","2":"0.020741030741001568"},"dire_origin_total_amt":{"0":"0","1":"0","2":"0"},"delivery_amt":{"0":"13035.17","1":"4571.52","2":"26941.75"},"timing_origin_total_amt":{"0":"537269","1":"152215","2":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   order_cnt |
|:------|---------------:|-----------:|------------:|
| count |              3 |       3    |        3    |
| mean  |             38 |    8752.33 |    11232    |
| std   |              0 |    3310.84 |     5336.32 |
| min   |             38 |    6543    |     7840    |
| 25%   |             38 |    6849    |     8156.5  |
| 50%   |             38 |    7155    |     8473    |
| 75%   |             38 |    9857    |    12928    |
| max   |             38 |   12559    |    17383    |