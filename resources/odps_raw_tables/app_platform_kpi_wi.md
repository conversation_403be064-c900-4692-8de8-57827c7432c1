# app_platform_kpi_wi
* comment: 平台KPI
* last_data_modified_time: 2025-09-18 03:23:53

# schema:
CREATE TABLE summerfarm_tech.`app_platform_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV',
  `timing_cust_cnt` BIGINT COMMENT '省心送履约客户数',
  `timing_ratio` DECIMAL(38,18) COMMENT '省心送合单率',
  `login_uv` BIGINT COMMENT '登陆UV',
  `order_uv` BIGINT COMMENT '交易客户数',
  `activity_uv` BIGINT COMMENT '特价点击UV',
  `activity_order_uv` BIGINT COMMENT '特价下单人数',
  `exchange_uv` BIGINT COMMENT '换购点击UV',
  `exchange_order_uv` BIGINT COMMENT '换购下单人数',
  `expand_uv` BIGINT COMMENT '拓展购买点击UV',
  `expand_order_uv` BIGINT COMMENT '拓展购买下单人数',
  `meeting_uv` BIGINT COMMENT '会场活动页点击UV',
  `meeting_order_uv` BIGINT COMMENT '会场活动页下单人数',
  `other_uv` BIGINT COMMENT '其他点击UV',
  `other_order_uv` BIGINT COMMENT '其他下单人数'
)
COMMENT '平台KPI'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025"},"week_of_year":{"0":"38"},"monday":{"0":"20250915"},"sunday":{"0":"20250921"},"origin_total_amt":{"0":"11022871.06"},"real_total_amt":{"0":"10667885.0736813186813188"},"cust_cnt":{"0":"18210"},"preferential_amt":{"0":"354985.9863186813186812"},"timing_origin_amt":{"0":"682201"},"timing_cust_cnt":{"0":"698"},"timing_ratio":{"0":"0.6303724928366762"},"login_uv":{"0":"32868"},"order_uv":{"0":"17871"},"activity_uv":{"0":"1078"},"activity_order_uv":{"0":"0"},"exchange_uv":{"0":"166"},"exchange_order_uv":{"0":"0"},"expand_uv":{"0":"0"},"expand_order_uv":{"0":"0"},"meeting_uv":{"0":"0"},"meeting_order_uv":{"0":"0"},"other_uv":{"0":"31319"},"other_order_uv":{"0":"11091"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   timing_cust_cnt |   login_uv |   order_uv |   activity_uv |   activity_order_uv |   exchange_uv |   exchange_order_uv |   expand_uv |   expand_order_uv |   meeting_uv |   meeting_order_uv |   other_uv |   other_order_uv |
|:------|---------------:|-----------:|------------------:|-----------:|-----------:|--------------:|--------------------:|--------------:|--------------------:|------------:|------------------:|-------------:|-------------------:|-----------:|-----------------:|
| count |              1 |          1 |                 1 |          1 |          1 |             1 |                   1 |             1 |                   1 |           1 |                 1 |            1 |                  1 |          1 |                1 |
| mean  |             38 |      18210 |               698 |      32868 |      17871 |          1078 |                   0 |           166 |                   0 |           0 |                 0 |            0 |                  0 |      31319 |            11091 |
| std   |            nan |        nan |               nan |        nan |        nan |           nan |                 nan |           nan |                 nan |         nan |               nan |          nan |                nan |        nan |              nan |
| min   |             38 |      18210 |               698 |      32868 |      17871 |          1078 |                   0 |           166 |                   0 |           0 |                 0 |            0 |                  0 |      31319 |            11091 |
| 25%   |             38 |      18210 |               698 |      32868 |      17871 |          1078 |                   0 |           166 |                   0 |           0 |                 0 |            0 |                  0 |      31319 |            11091 |
| 50%   |             38 |      18210 |               698 |      32868 |      17871 |          1078 |                   0 |           166 |                   0 |           0 |                 0 |            0 |                  0 |      31319 |            11091 |
| 75%   |             38 |      18210 |               698 |      32868 |      17871 |          1078 |                   0 |           166 |                   0 |           0 |                 0 |            0 |                  0 |      31319 |            11091 |
| max   |             38 |      18210 |               698 |      32868 |      17871 |          1078 |                   0 |           166 |                   0 |           0 |                 0 |            0 |                  0 |      31319 |            11091 |