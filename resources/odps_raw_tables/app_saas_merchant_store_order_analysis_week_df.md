# app_saas_merchant_store_order_analysis_week_df
* comment: saas-门店订货分析-周
* last_data_modified_time: 2025-09-18 02:19:08

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_order_analysis_week_df` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `store_id` BIGINT COMMENT '门店id',
  `average_order_period` DECIMAL(38,18) COMMENT '平均订货周期',
  `average_order_period_last_period` DECIMAL(38,18) COMMENT '上周期平均订货周期',
  `average_order_period_upper_period` DECIMAL(38,18) COMMENT '平均订货周期环比',
  `order_amount` BIGINT COMMENT '订货数量',
  `order_amount_last_period` BIGINT COMMENT '上周期订货数量',
  `order_amount_upper_period` DECIMAL(38,18) COMMENT '订货数量环比',
  `order_price` DECIMAL(38,18) COMMENT '订货金额',
  `order_price_last_period` DECIMAL(38,18) COMMENT '上周期订货金额',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '订货金额环比',
  `last_order_time` STRING COMMENT '最后订货日期 yyyy-MM-dd',
  `last_order_amount` BIGINT COMMENT '最后订货数量',
  `last_order_price` DECIMAL(38,18) COMMENT '最后订货金额'
)
COMMENT 'saas-门店订货分析-周'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"6","4":"6"},"time_tag":{"0":"20220530","1":"20220613","2":"20220613","3":"20220711","4":"20220718"},"store_id":{"0":"3","1":"1","2":"3","3":"29","4":"29"},"average_order_period":{"0":"1","1":"1","2":"1","3":"1","4":"1.25"},"average_order_period_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"1"},"average_order_period_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"0.25"},"order_amount":{"0":"1","1":"5","2":"1","3":"6","4":"112"},"order_amount_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"6"},"order_amount_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"17.66666666666667"},"order_price":{"0":"0.02","1":"148","2":"23","3":"248","4":"5556.4"},"order_price_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"248"},"order_price_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"21.404838709677419355"},"last_order_time":{"0":"2022-06-02","1":"2022-06-19","2":"2022-06-19","3":"2022-07-14","4":"2022-07-23"},"last_order_amount":{"0":"1","1":"5","2":"1","3":"6","4":"14"},"last_order_price":{"0":"0.02","1":"148","2":"23","3":"248","4":"843.8"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   store_id |   order_amount |   order_amount_last_period |   last_order_amount |
|:------|------------:|-----------:|---------------:|---------------------------:|--------------------:|
| count | 10000       |  10000     |     10000      |                 10000      |          10000      |
| mean  |     7.9295  |    853.799 |        17.7078 |                    15.6583 |              6.4626 |
| std   |     4.37057 |    651.624 |        25.5548 |                    25.1022 |              9.5798 |
| min   |     2       |      1     |         1      |                     0      |              1      |
| 25%   |     4       |    329     |         5      |                     3      |              2      |
| 50%   |     6       |    636     |        11      |                     9      |              4      |
| 75%   |    13       |   1294.25  |        21      |                    20      |              7      |
| max   |    20       |   2855     |       412      |                   412      |            342      |