# app_operate_kpi_trade_di
* comment: 交易（订单）运营KPI指标汇总
* last_data_modified_time: 2025-09-18 02:45:33

# schema:
CREATE TABLE summerfarm_tech.`app_operate_kpi_trade_di` (
  `date` STRING COMMENT '日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `cust_cnt` BIGINT COMMENT '交易客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `consign_origin_total_amt` DECIMAL(38,18) COMMENT '代售应付总金额',
  `consign_real_total_amt` DECIMAL(38,18) COMMENT '代售实付总金额',
  `consign_preferential_amt` DECIMAL(38,18) COMMENT '代售营销金额',
  `consign_cust_cnt` BIGINT COMMENT '代售客户数',
  `consign_order_cnt` BIGINT COMMENT '代售订单数',
  `register_cust_cnt` BIGINT COMMENT '注册客户数',
  `new_active_cust_cnt` BIGINT COMMENT '有效拉新客户数（注册且首日下单金额>=15）',
  `lose_cust_cnt` BIGINT COMMENT '交易流失客户数（90天内活跃用户近60天未下单客户数）',
  `lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率'
)
COMMENT '交易（订单）运营KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917"},"origin_total_amt":{"0":"3754297.07"},"real_total_amt":{"0":"3618659.35"},"cust_cnt":{"0":"7122"},"cust_arpu":{"0":"527.140841055883178882"},"order_cnt":{"0":"8437"},"order_avg":{"0":"444.980096005689226028"},"consign_origin_total_amt":{"0":"0"},"consign_real_total_amt":{"0":"0"},"consign_preferential_amt":{"0":"0"},"consign_cust_cnt":{"0":"0"},"consign_order_cnt":{"0":"0"},"register_cust_cnt":{"0":"298"},"new_active_cust_cnt":{"0":"49"},"lose_cust_cnt":{"0":"7811"},"lose_cust_ratio":{"0":"0.1131799345060422"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   consign_cust_cnt |   consign_order_cnt |   register_cust_cnt |   new_active_cust_cnt |   lose_cust_cnt |
|:------|-----------:|------------:|-------------------:|--------------------:|--------------------:|----------------------:|----------------:|
| count |          1 |           1 |                  1 |                   1 |                   1 |                     1 |               1 |
| mean  |       7122 |        8437 |                  0 |                   0 |                 298 |                    49 |            7811 |
| std   |        nan |         nan |                nan |                 nan |                 nan |                   nan |             nan |
| min   |       7122 |        8437 |                  0 |                   0 |                 298 |                    49 |            7811 |
| 25%   |       7122 |        8437 |                  0 |                   0 |                 298 |                    49 |            7811 |
| 50%   |       7122 |        8437 |                  0 |                   0 |                 298 |                    49 |            7811 |
| 75%   |       7122 |        8437 |                  0 |                   0 |                 298 |                    49 |            7811 |
| max   |       7122 |        8437 |                  0 |                   0 |                 298 |                    49 |            7811 |