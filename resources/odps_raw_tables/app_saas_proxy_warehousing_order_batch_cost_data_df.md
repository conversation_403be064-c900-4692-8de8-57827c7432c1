# app_saas_proxy_warehousing_order_batch_cost_data_df
* comment: 
* last_data_modified_time: 2025-09-18 02:07:37

# schema:
CREATE TABLE summerfarm_tech.`app_saas_proxy_warehousing_order_batch_cost_data_df` (
  `tenant_id` BIGINT COMMENT '租户ID',
  `create_time` STRING COMMENT '出库任务创建时间',
  `out_order_no` STRING COMMENT '订单编号',
  `warehouse_no` STRING COMMENT '出库仓库编号',
  `sku` STRING COMMENT 'SKU',
  `batch` STRING COMMENT '出库批次',
  `quantity_sum` BIGINT COMMENT '总出库数量',
  `unit_cost` DECIMAL(38,18) COMMENT ' SKU该批次SKU成本单价',
  `total_cost` DECIMAL(38,18) COMMENT '总成本金额'
)
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区列'
)
STORED AS AliOrc
LIFECYCLE 365

# head data:
{"tenant_id":{"0":"58","1":"1","2":"1","3":"1","4":"1"},"create_time":{"0":"2025-09-17 16:38:14","1":"2025-09-18 00:01:18","2":"2025-09-17 22:19:16","3":"2025-09-17 22:59:54","4":"2025-09-17 22:59:54"},"out_order_no":{"0":"OR175809553448955","1":"OR175811582485915","2":"OR175809872569848","3":"OR175809644190165","4":"OR175809643964336"},"warehouse_no":{"0":"162","1":"10","2":"10","3":"48","4":"48"},"sku":{"0":"1121483137716","1":"16885761174","2":"16823457251","3":"5456725335","4":"5456725335"},"batch":{"0":"20250917601161139","1":"202509172166610018","2":"202509172166610018","3":"202509171830934034","4":"202509171830934034"},"quantity_sum":{"0":"7","1":"1","2":"1","3":"2","4":"1"},"unit_cost":{"0":"3.2","1":"30","2":"45","3":"22","4":"22"},"total_cost":{"0":"22.4","1":"30","2":"45","3":"44","4":"22"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   quantity_sum |
|:------|------------:|---------------:|
| count |  10000      |    10000       |
| mean  |      2.9648 |        1.7038  |
| std   |     10.7338 |        2.81994 |
| min   |      1      |        1       |
| 25%   |      1      |        1       |
| 50%   |      1      |        1       |
| 75%   |      1      |        2       |
| max   |     95      |      200       |