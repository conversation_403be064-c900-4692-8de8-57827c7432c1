# app_saas_brand_city_delivery_mi
* comment: saas履约网络可视化表
* last_data_modified_time: 2025-09-18 02:57:17

# schema:
CREATE TABLE summerfarm_tech.`app_saas_brand_city_delivery_mi` (
  `month` STRING COMMENT '月份',
  `brand_alias` STRING COMMENT '品牌名称',
  `province` STRING COMMENT '省',
  `city` STRING COMMENT '市',
  `area` STRING COMMENT '区',
  `point_cnt` BIGINT COMMENT '点位数'
)
COMMENT 'saas履约网络可视化表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"brand_alias":{"0":"GIGI LUCKY舒芙蕾","1":"GIGI LUCKY舒芙蕾","2":"GIGI LUCKY舒芙蕾","3":"GIGI LUCKY舒芙蕾","4":"GIGI LUCKY舒芙蕾"},"province":{"0":"广东","1":"广东","2":"广东","3":"广东","4":"广东"},"city":{"0":"东莞市","1":"东莞市","2":"中山市","3":"中山市","4":"佛山市"},"area":{"0":"万江街道","1":"南城街道","2":"东区街道","3":"石岐街道","4":"顺德区"},"point_cnt":{"0":"3","1":"3","2":"4","3":"2","4":"2"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   point_cnt |
|:------|------------:|
| count |    756      |
| mean  |     10.5661 |
| std   |     17.2839 |
| min   |      1      |
| 25%   |      2      |
| 50%   |      6      |
| 75%   |     12      |
| max   |    224      |