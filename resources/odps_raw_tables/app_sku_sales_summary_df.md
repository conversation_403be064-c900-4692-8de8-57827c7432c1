# app_sku_sales_summary_df
* comment: SKU销售数据汇总表(最近30天)
* last_data_modified_time: 2025-09-18 11:24:25

# schema:
CREATE TABLE summerfarm_tech.`app_sku_sales_summary_df` (
  `sku` STRING COMMENT 'SKU编码',
  `sales_volume` BIGINT COMMENT '最近30天销售数量',
  `total_gmv` DECIMAL(20,4) COMMENT '最近30天总GMV金额'
)
COMMENT 'SKU销售数据汇总表(最近30天)'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '数据日期分区'
)
STORED AS AliOrc
LIFECYCLE 90

# head data:
{"sku":{"0":"1001052874538","1":"1001156816080","2":"1001156816451","3":"1001156816767","4":"1001218743116","5":"1001218743130","6":"1001218743168","7":"1001218743510","8":"1001450537408","9":"1001450537647"},"sales_volume":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"7"},"total_gmv":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"48.3"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   sales_volume |
|:------|---------------:|
| count |     10000      |
| mean  |        50.6443 |
| std   |       810.976  |
| min   |         0      |
| 25%   |         0      |
| 50%   |         0      |
| 75%   |         0      |
| max   |     43832      |