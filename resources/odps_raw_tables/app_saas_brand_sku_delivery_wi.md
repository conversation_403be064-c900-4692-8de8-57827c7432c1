# app_saas_brand_sku_delivery_wi
* comment: saas利润数据表现表（仅为鲜沐自营数据）
* last_data_modified_time: 2025-09-18 02:57:07

# schema:
CREATE TABLE summerfarm_tech.`app_saas_brand_sku_delivery_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `brand_alias` STRING COMMENT '品牌名称',
  `sku_id` STRING COMMENT '商品SKU',
  `title` STRING COMMENT '商品标题',
  `specification` STRING COMMENT '商品规格',
  `category1` STRING COMMENT '后台一级类目',
  `delivery_gmv` DECIMAL(38,18) COMMENT '履约GMV',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本'
)
COMMENT 'saas利润数据表现表（仅为鲜沐自营数据）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"brand_alias":{"0":"一只酸奶牛","1":"八街手作（口口椰）","2":"GIGI LUCKY舒芙蕾","3":"新加坡斯味洛鲜奶茶 ","4":"日尝"},"sku_id":{"0":"G002S02R010","1":"G002S02R010","2":"N001S01R002","3":"N001S01R005","4":"N001S01R005"},"title":{"0":"国产红心火龙果","1":"国产红心火龙果","2":"爱乐薇(铁塔)淡奶油","3":"安佳淡奶油","4":"安佳淡奶油"},"specification":{"0":"5斤*1包\/一级\/单果约300g+","1":"5斤*1包\/一级\/单果约300g+","2":"1L*12盒","3":"1L*12瓶","4":"1L*12瓶"},"category1":{"0":"新鲜水果","1":"新鲜水果","2":"乳制品","3":"乳制品","4":"乳制品"},"delivery_gmv":{"0":"39","1":"25.56","2":"4240","3":"3297","4":"1413"},"cost_amt":{"0":"27","1":"21.1","2":"4149.2","3":"3262","4":"1398"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |
|:------|---------------:|
| count |            337 |
| mean  |             38 |
| std   |              0 |
| min   |             38 |
| 25%   |             38 |
| 50%   |             38 |
| 75%   |             38 |
| max   |             38 |