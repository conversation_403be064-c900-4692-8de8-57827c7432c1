# app_mkt_deliver_sku_preferential_mi
* comment: 商品粒度履约优惠明细月表
* last_data_modified_time: 2025-09-18 03:37:52

# schema:
CREATE TABLE summerfarm_tech.`app_mkt_deliver_sku_preferential_mi` (
  `month` STRING COMMENT '月份',
  `sku_id` STRING COMMENT '商品SKU',
  `spu_id` BIGINT COMMENT '商品pd_id',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品规格',
  `sku_type` STRING COMMENT '商品类型; 自营 ,代仓 ,代售',
  `category1` STRING COMMENT '一级分类',
  `category2` STRING COMMENT '二级分类',
  `category3` STRING COMMENT '三级分类',
  `category4` STRING COMMENT '四级分类',
  `city_id` BIGINT COMMENT '运营服务区ID',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细）',
  `warehouse_no` BIGINT COMMENT '库存仓no',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `store_no` BIGINT COMMENT '城配仓no',
  `store_name` STRING COMMENT '城配仓名称',
  `sku_cnt` BIGINT COMMENT '履约商品数量',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付金额',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本',
  `activity_coupon_amt` DECIMAL(38,18) COMMENT '平台活动券优惠金额',
  `sh_coupon_amt` DECIMAL(38,18) COMMENT '售后补偿券优惠金额',
  `new_cust_coupon_amt` DECIMAL(38,18) COMMENT '区域拉新券优惠金额',
  `qy_coupon_amt` DECIMAL(38,18) COMMENT '会员权益券优惠金额',
  `bd_coupon_amt` DECIMAL(38,18) COMMENT '销售客情券优惠金额',
  `recall_coupon_amt` DECIMAL(38,18) COMMENT '销售月活券优惠金额',
  `xp_coupon_amt` DECIMAL(38,18) COMMENT '行业活动券优惠金额',
  `return_coupon_amt` DECIMAL(38,18) COMMENT '满返优惠金额(作废)',
  `ygfl_coupon_amt` DECIMAL(38,18) COMMENT '员工福利券优惠金额',
  `thbt_coupon_amt` DECIMAL(38,18) COMMENT '销售囤货券优惠金额',
  `jjbc_coupon_amt` DECIMAL(38,18) COMMENT '区域活动券优惠金额',
  `pllx_coupon_amt` DECIMAL(38,18) COMMENT '销售品类券优惠金额',
  `plzh_coupon_amt` DECIMAL(38,18) COMMENT '品类召回优惠金额(作废)',
  `lsfx_coupon_amt` DECIMAL(38,18) COMMENT '销售现货券优惠金额',
  `zxbt_coupon_amt` DECIMAL(38,18) COMMENT '滞销补贴优惠金额(作废)',
  `lbbt_coupon_amt` DECIMAL(38,18) COMMENT '临保补贴优惠金额(作废)',
  `tsqk_coupon_amt` DECIMAL(38,18) COMMENT '功能测试券优惠金额',
  `jzs_coupon_amt` DECIMAL(38,18) COMMENT '精准送优惠金额',
  `bed_pack_amt` DECIMAL(38,18) COMMENT '红包优惠金额',
  `deliver_coupon_amt` DECIMAL(38,18) COMMENT '运费优惠券金额',
  `activity_nolb_amt` DECIMAL(38,18) COMMENT '活动优惠金额（不含临保）',
  `activity_lb_amt` DECIMAL(38,18) COMMENT '临保活动优惠金额',
  `ladder_price_amt` DECIMAL(38,18) COMMENT '阶梯价优惠金额',
  `collocation_amt` DECIMAL(38,18) COMMENT '搭配购优惠金额',
  `suit_amt` DECIMAL(38,18) COMMENT '组合包优惠金额',
  `expand_amt` DECIMAL(38,18) COMMENT '拓展购买优惠金额',
  `replace_amt` DECIMAL(38,18) COMMENT '换购优惠金额',
  `presale_amt` DECIMAL(38,18) COMMENT '预售优惠金额',
  `presale_balance_amt` DECIMAL(38,18) COMMENT '预售尾款立减优惠金额',
  `group_purchase_amt` DECIMAL(38,18) COMMENT '多人拼团优惠金额',
  `reduce_amt` DECIMAL(38,18) COMMENT '满减优惠金额',
  `seckill_amt` DECIMAL(38,18) COMMENT '秒杀优惠金额',
  `cream_card_amt` DECIMAL(38,18) COMMENT '奶油卡优惠金额',
  `milk_card_amt` DECIMAL(38,18) COMMENT '鲜奶卡优惠金额',
  `other_card_amt` DECIMAL(38,18) COMMENT '其他黄金卡优惠金额',
  `gift_amt` DECIMAL(38,18) COMMENT '赠品优惠金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费应付',
  `delivery_real_amt` DECIMAL(38,18) COMMENT '运费实付',
  `live_exclusive_amt` DECIMAL(38,18) COMMENT '直播专享优惠金额',
  `live_sharing_amt` DECIMAL(38,18) COMMENT '直播同享优惠金额',
  `ppbc_coupon_amt` DECIMAL(38,18) COMMENT '平台补偿券优惠金额',
  `psbc_coupon_amt` DECIMAL(38,18) COMMENT '配送补偿券优惠金额',
  `qyzh_coupon_amt` DECIMAL(38,18) COMMENT '区域召回券优惠金额',
  `schd_coupon_amt` DECIMAL(38,18) COMMENT '市场活动券优惠金额',
  `qt_coupon_amt` DECIMAL(38,18) COMMENT '其他优惠券优惠金额'
)
COMMENT '商品粒度履约优惠明细月表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"sku_id":{"0":"1003074364015","1":"1003074364015","2":"1003074364015","3":"1003074364015","4":"1003074364015"},"spu_id":{"0":"3902","1":"3902","2":"3902","3":"3902","4":"3902"},"spu_name":{"0":"ProtagxEva乳酸黄油","1":"ProtagxEva乳酸黄油","2":"ProtagxEva乳酸黄油","3":"ProtagxEva乳酸黄油","4":"ProtagxEva乳酸黄油"},"sku_disc":{"0":"10KG*1箱","1":"10KG*1箱","2":"10KG*1箱","3":"10KG*1箱","4":"10KG*1箱"},"sku_type":{"0":"自营","1":"自营","2":"自营","3":"自营","4":"自营"},"category1":{"0":"乳制品","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品"},"category2":{"0":"乳制品","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品"},"category3":{"0":"黄油","1":"黄油","2":"黄油","3":"黄油","4":"黄油"},"category4":{"0":"乳酸黄油","1":"乳酸黄油","2":"乳酸黄油","3":"乳酸黄油","4":"乳酸黄油"},"city_id":{"0":"1001","1":"1001","2":"1218","3":"2750","4":"14343"},"city_name":{"0":"杭州","1":"杭州","2":"诸暨","3":"上海","4":"广州"},"large_area_id":{"0":"1","1":"1","2":"1","3":"2","4":"14"},"large_area_name":{"0":"杭州大区","1":"杭州大区","2":"杭州大区","3":"上海大区","4":"广州大区"},"cust_team":{"0":"平台客户","1":"平台客户","2":"平台客户","3":"平台客户","4":"平台客户"},"cust_type":{"0":"其他","1":"茶饮","2":"面包蛋糕","3":"面包蛋糕","4":"甜品冰淇淋"},"life_cycle_detail":{"0":"A3","1":"S2","2":"S1","3":"S1","4":"S1"},"warehouse_no":{"0":"121","1":"121","2":"121","3":"121","4":"117"},"warehouse_name":{"0":"嘉兴海盐总仓","1":"嘉兴海盐总仓","2":"嘉兴海盐总仓","3":"嘉兴海盐总仓","4":"东莞冷冻总仓"},"store_no":{"0":"98","1":"98","2":"1","3":"99","4":"14"},"store_name":{"0":"杭州三仓","1":"杭州三仓","2":"杭州仓","3":"上海六仓","4":"广州仓"},"sku_cnt":{"0":"1","1":"1","2":"2","3":"14","4":"1"},"origin_total_amt":{"0":"472","1":"472","2":"944","3":"6608","4":"472"},"real_total_amt":{"0":"457","1":"457","2":"873.333333333333333334","3":"5909","4":"472"},"cost_amt":{"0":"350","1":"350","2":"700","3":"4900","4":"350"},"activity_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"sh_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"new_cust_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"qy_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"bd_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"recall_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"xp_coupon_amt":{"0":"15","1":"15","2":"16.666666666666665","3":"15","4":"0"},"return_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ygfl_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"thbt_coupon_amt":{"0":"0","1":"0","2":"0","3":"225","4":"0"},"jjbc_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"pllx_coupon_amt":{"0":"0","1":"0","2":"0","3":"75","4":"0"},"plzh_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"lsfx_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"zxbt_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"lbbt_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"tsqk_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"jzs_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"bed_pack_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"deliver_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"activity_nolb_amt":{"0":"0","1":"0","2":"53.9999999999999946","3":"384","4":"0"},"activity_lb_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ladder_price_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"collocation_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"suit_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"expand_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"replace_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"presale_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"presale_balance_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"group_purchase_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"reduce_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"seckill_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"cream_card_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"milk_card_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_card_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"gift_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"delivery_amt":{"0":"0","1":"0","2":"0","3":"9.52","4":"0"},"delivery_real_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"live_exclusive_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"live_sharing_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ppbc_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"psbc_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"qyzh_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"schd_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"qt_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   spu_id |   city_id |   large_area_id |   warehouse_no |   store_no |    sku_cnt |
|:------|---------:|----------:|----------------:|---------------:|-----------:|-----------:|
| count | 10000    |   10000   |      10000      |     10000      | 10000      | 10000      |
| mean  |  2218.05 |   24067.6 |         34.0686 |        49.0263 |    43.7742 |     4.7099 |
| std   |  3552.38 |   15675.7 |         32.0326 |        43.0507 |    34.5745 |    66.1858 |
| min   |    14    |    1001   |          1      |         2      |     1      |     1      |
| 25%   |   867    |    9585   |          2      |        10      |    21      |     1      |
| 50%   |  1484    |   20627   |         24      |        38      |    30      |     2      |
| 75%   |  1685    |   44122   |         72      |        69      |    63      |     3      |
| max   | 18857    |   44269   |         91      |       155      |   154      |  6559      |