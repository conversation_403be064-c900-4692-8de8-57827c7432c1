# app_stc_kpi_mi
* comment: 仓配KPI汇总表
* last_data_modified_time: 2025-09-18 03:47:16

# schema:
CREATE TABLE summerfarm_tech.`app_stc_kpi_mi` (
  `month` STRING COMMENT '日期',
  `check_sku_cnt` BIGINT COMMENT '抽检数量',
  `in_bound_sku_cnt` BIGINT COMMENT '入库数量',
  `check_rate` DECIMAL(38,18) COMMENT '抽检比例',
  `back_order_cnt` BIGINT COMMENT '退货总单数',
  `finish_order_cnt` BIGINT COMMENT '已完成单数',
  `back_finish_rate` DECIMAL(38,18) COMMENT '退货完结率',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责',
  `sale_amt` DECIMAL(38,18) COMMENT '销售金额金额',
  `error_sku_cnt` BIGINT COMMENT '错误件数',
  `error_sku_cnt_wah` BIGINT COMMENT '错误件数_仓配责',
  `error_cust_cnt` BIGINT COMMENT '错误客户数',
  `error_cust_cnt_wah` BIGINT COMMENT '错误客户数_仓配责',
  `cust_cnt` BIGINT COMMENT '活跃客户数',
  `sku_cnt` BIGINT COMMENT '配送件数',
  `total_point_cnt` BIGINT COMMENT '总点位数',
  `point_cnt` BIGINT COMMENT '点位数（不含喜茶）',
  `no_in_time_point_cnt` BIGINT COMMENT '不及时点位数（不含喜茶）',
  `delay_time_point_cnt_2` BIGINT COMMENT '前置2小时不及时点位数（不含喜茶）',
  `out_time` DECIMAL(38,18) COMMENT '配送超时时间（不含喜茶）',
  `delay_time_2` DECIMAL(38,18) COMMENT '前置2小时配送超时时间（不含喜茶）',
  `path_cnt` BIGINT COMMENT '线路数（不含喜茶）',
  `delay_path_cnt` BIGINT COMMENT '出库不及时线路数（不含喜茶）',
  `out_distance_point_cnt` BIGINT COMMENT '超距离点位数（含喜茶）',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额',
  `after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责',
  `after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责',
  `after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责',
  `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责',
  `after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付GMV',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本'
)
COMMENT '仓配KPI汇总表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509"},"check_sku_cnt":{"0":"0"},"in_bound_sku_cnt":{"0":"0"},"check_rate":{"0":"0"},"back_order_cnt":{"0":"1068"},"finish_order_cnt":{"0":"830"},"back_finish_rate":{"0":"0.7771535580524345"},"damage_amt":{"0":"129448.82"},"damage_amt_wah":{"0":"5743.51"},"sale_amt":{"0":"58405082.11"},"error_sku_cnt":{"0":"520"},"error_sku_cnt_wah":{"0":"520"},"error_cust_cnt":{"0":"345"},"error_cust_cnt_wah":{"0":"345"},"cust_cnt":{"0":"141844"},"sku_cnt":{"0":"719234"},"total_point_cnt":{"0":"154911"},"point_cnt":{"0":"154911"},"no_in_time_point_cnt":{"0":"8304"},"delay_time_point_cnt_2":{"0":"17844"},"out_time":{"0":"10.82166666666667"},"delay_time_2":{"0":"6391.406666666667"},"path_cnt":{"0":"8075"},"delay_path_cnt":{"0":"562"},"out_distance_point_cnt":{"0":"430"},"after_sale_amt":{"0":"373349.47"},"after_sale_amt_wah":{"0":"46043.53"},"after_sale_amt_pur":{"0":"35156.42"},"after_sale_amt_che":{"0":"227651.16"},"after_sale_amt_pur_che":{"0":"262807.58"},"after_sale_amt_oth":{"0":"64498.36"},"delivery_total_amt":{"0":"86821584.581333333333041815"},"coupon_amt":{"0":"767132.96883116883087674"},"origin_total_amt":{"0":"65056920.03"},"real_total_amt":{"0":"63049972.233454545454546043"},"storage_amt":{"0":"1327926.142704809887349893"},"arterial_roads_amt":{"0":"1042439.169236149527764213"},"deliver_amt":{"0":"3981839.403346344850876726"},"self_picked_amt":{"0":"0"},"other_amt":{"0":"166121.217839971055634826"},"allocation_amt":{"0":"258667.969234109989238129"},"cost_amt":{"0":"56064150.91"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   check_sku_cnt |   in_bound_sku_cnt |   back_order_cnt |   finish_order_cnt |   error_sku_cnt |   error_sku_cnt_wah |   error_cust_cnt |   error_cust_cnt_wah |   cust_cnt |   sku_cnt |   total_point_cnt |   point_cnt |   no_in_time_point_cnt |   delay_time_point_cnt_2 |   path_cnt |   delay_path_cnt |   out_distance_point_cnt |
|:------|----------------:|-------------------:|-----------------:|-------------------:|----------------:|--------------------:|-----------------:|---------------------:|-----------:|----------:|------------------:|------------:|-----------------------:|-------------------------:|-----------:|-----------------:|-------------------------:|
| count |               1 |                  1 |                1 |                  1 |               1 |                   1 |                1 |                    1 |          1 |         1 |                 1 |           1 |                      1 |                        1 |          1 |                1 |                        1 |
| mean  |               0 |                  0 |             1068 |                830 |             520 |                 520 |              345 |                  345 |     141844 |    719234 |            154911 |      154911 |                   8304 |                    17844 |       8075 |              562 |                      430 |
| std   |             nan |                nan |              nan |                nan |             nan |                 nan |              nan |                  nan |        nan |       nan |               nan |         nan |                    nan |                      nan |        nan |              nan |                      nan |
| min   |               0 |                  0 |             1068 |                830 |             520 |                 520 |              345 |                  345 |     141844 |    719234 |            154911 |      154911 |                   8304 |                    17844 |       8075 |              562 |                      430 |
| 25%   |               0 |                  0 |             1068 |                830 |             520 |                 520 |              345 |                  345 |     141844 |    719234 |            154911 |      154911 |                   8304 |                    17844 |       8075 |              562 |                      430 |
| 50%   |               0 |                  0 |             1068 |                830 |             520 |                 520 |              345 |                  345 |     141844 |    719234 |            154911 |      154911 |                   8304 |                    17844 |       8075 |              562 |                      430 |
| 75%   |               0 |                  0 |             1068 |                830 |             520 |                 520 |              345 |                  345 |     141844 |    719234 |            154911 |      154911 |                   8304 |                    17844 |       8075 |              562 |                      430 |
| max   |               0 |                  0 |             1068 |                830 |             520 |                 520 |              345 |                  345 |     141844 |    719234 |            154911 |      154911 |                   8304 |                    17844 |       8075 |              562 |                      430 |