# app_history_price_log_df
* comment: 进货单-商品历史价格表
* last_data_modified_time: 2025-09-18 02:07:50

# schema:
CREATE TABLE summerfarm_tech.`app_history_price_log_df` (
  `sku` STRING COMMENT 'sku',
  `area_no` BIGINT COMMENT '城市编号',
  `price` DECIMAL(38,18) COMMENT '商品售价',
  `activity_price` DECIMAL(38,18) COMMENT '商品活动价格',
  `date_key` STRING COMMENT '日期标记'
)
COMMENT '进货单-商品历史价格表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"sku":{"0":"555148110401","1":"555148110401","2":"555148110401","3":"555148110401","4":"555148110401"},"area_no":{"0":"19462","1":"41224","2":"44127","3":"44128","4":"44146"},"price":{"0":"136","1":"136","2":"136","3":"136","4":"136"},"activity_price":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"date_key":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   area_no |
|:------|----------:|
| count |   10000   |
| mean  |   28660.5 |
| std   |   15644.6 |
| min   |    1001   |
| 25%   |   14816   |
| 50%   |   32751   |
| 75%   |   44167   |
| max   |   44270   |