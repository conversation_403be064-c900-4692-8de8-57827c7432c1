# app_pcs_supplier_rebate_cumulative_df
* comment: 供应商返利目标累计(剔除部分sku)
* last_data_modified_time: 2025-09-18 02:58:45

# schema:
CREATE TABLE summerfarm_tech.`app_pcs_supplier_rebate_cumulative_df` (
  `year` BIGINT COMMENT '年度',
  `period` STRING COMMENT '周期',
  `supplier_id` BIGINT COMMENT '供货商编号',
  `supplier_name` STRING COMMENT '供货商名称',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '仓库名称',
  `commodity_temperature_zone` STRING COMMENT '商品温区',
  `purchase_order_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购下单金额',
  `purchase_order_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购下单金额（不含税）',
  `purchase_order_quantity_in_period` BIGINT COMMENT '周期内采购下单件数',
  `purchase_order_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购下单重量',
  `purchase_order_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购下单体积',
  `purchase_inbound_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购入库金额',
  `purchase_inbound_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购入库金额（不含税）',
  `purchase_inbound_quantity_in_period` BIGINT COMMENT '周期内采购入库件数',
  `purchase_inbound_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购入库重量',
  `purchase_inbound_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购入库体积',
  `purchase_reservation_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购预约金额',
  `purchase_reservation_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购预约金额（不含税）',
  `purchase_reservation_quantity_in_period` BIGINT COMMENT '周期内采购预约件数',
  `purchase_reservation_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购预约重量',
  `purchase_reservation_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购预约体积',
  `sale_amount_in_period` DECIMAL(38,18) COMMENT '周期内销售金额',
  `sale_quantity_in_period` BIGINT COMMENT '周期内销售件数',
  `sale_weight_in_period` DECIMAL(38,18) COMMENT '周期内销售重量',
  `sale_volume_in_period` DECIMAL(38,18) COMMENT '周期内销售体积'
)
COMMENT '供应商返利目标累计(剔除部分sku)'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"year":{"0":"2024","1":"2024","2":"2024","3":"2024","4":"2024"},"period":{"0":"H1","1":"H1","2":"H1","3":"H1","4":"H1"},"supplier_id":{"0":"28","1":"28","2":"28","3":"28","4":"28"},"supplier_name":{"0":"西诺迪斯食品（上海）有限公司","1":"西诺迪斯食品（上海）有限公司","2":"西诺迪斯食品（上海）有限公司","3":"西诺迪斯食品（上海）有限公司","4":"西诺迪斯食品（上海）有限公司"},"warehouse_no":{"0":"1","1":"2","2":"2","3":"2","4":"10"},"warehouse_name":{"0":"杭州总仓","1":"上海总仓","2":"上海总仓","3":"上海总仓","4":"嘉兴总仓"},"commodity_temperature_zone":{"0":"冷藏","1":"冷冻","2":"冷藏","3":"常温","4":"冷藏"},"purchase_order_amount_in_period":{"0":"0","1":"0","2":"1383360.64","3":"54444.7","4":"10018932.45"},"purchase_order_amount_excluding_tax":{"0":"0","1":"0","2":"1224212.955752212389380529","3":"48181.150442477876106193","4":"8866311.902654867256637167"},"purchase_order_quantity_in_period":{"0":"0","1":"0","2":"3008","3":"196","4":"22363"},"purchase_order_weight_in_period":{"0":"0","1":"0","2":"35003.9","3":"280","4":"262967.1"},"purchase_order_volume_in_period":{"0":"0","1":"0","2":"45.786635000000003","3":"0.9468480000000001","4":"356.62907000000001"},"purchase_inbound_amount_in_period":{"0":"0","1":"0","2":"1354835.402933333333333262","3":"54444.699999999999999966","4":"10014029.674786324786324516"},"purchase_inbound_amount_excluding_tax":{"0":"0","1":"0","2":"1198969.383126843657817041","3":"48181.150442477876106163","4":"8861973.163527721049844703"},"purchase_inbound_quantity_in_period":{"0":"0","1":"0","2":"2944","3":"196","4":"22352"},"purchase_inbound_weight_in_period":{"0":"0","1":"0","2":"34210.300000000002","3":"280","4":"262830.70000000001"},"purchase_inbound_volume_in_period":{"0":"0","1":"0","2":"44.792075000000001","3":"0.9468480000000001","4":"356.45813000000004"},"purchase_reservation_amount_in_period":{"0":"0","1":"0","2":"1354835.402933333333333262","3":"54444.699999999999999966","4":"10014029.674786324786324516"},"purchase_reservation_amount_excluding_tax":{"0":"0","1":"0","2":"1198969.383126843657817041","3":"48181.150442477876106163","4":"8861973.163527721049844703"},"purchase_reservation_quantity_in_period":{"0":"0","1":"0","2":"2944","3":"196","4":"22352"},"purchase_reservation_weight_in_period":{"0":"0","1":"0","2":"34210.300000000002","3":"280","4":"262830.69999999999"},"purchase_reservation_volume_in_period":{"0":"0","1":"0","2":"44.792075000000001","3":"0.9468480000000001","4":"356.45813000000006"},"sale_amount_in_period":{"0":"19292","1":"1172","2":"2786066","3":"68196","4":"10090126"},"sale_quantity_in_period":{"0":"35","1":"14","2":"5330","3":"202","4":"20065"},"sale_weight_in_period":{"0":"420","1":"14.800000000000001","2":"62876.250000000002","3":"331","4":"236095.79999999957"},"sale_volume_in_period":{"0":"0.49822500000000002","1":"0.028288000000000002","2":"78.565640000000107","3":"1.114968","4":"308.77821999999841"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |      year |   supplier_id |   warehouse_no |   purchase_order_quantity_in_period |   purchase_inbound_quantity_in_period |   purchase_reservation_quantity_in_period |   sale_quantity_in_period |
|:------|----------:|--------------:|---------------:|------------------------------------:|--------------------------------------:|------------------------------------------:|--------------------------:|
| count | 4125      |      4125     |      4125      |                            4125     |                              4125     |                                  4125     |                  4125     |
| mean  | 2024.41   |       838.675 |        61.7845 |                             780.277 |                               745.648 |                                   739.243 |                   681.798 |
| std   |    0.5119 |       752.538 |        43.7605 |                            3749.72  |                              3559.67  |                                  3526.87  |                  2996.34  |
| min   | 2023      |        28     |         1      |                               0     |                                 0     |                                     0     |                     0     |
| 25%   | 2024      |       360     |        29      |                               0     |                                 0     |                                     0     |                     5     |
| 50%   | 2024      |       756     |        59      |                               0     |                                 0     |                                     0     |                    23     |
| 75%   | 2025      |       969     |        69      |                              72     |                                60     |                                    60     |                   197     |
| max   | 2025      |      2912     |       155      |                           76964     |                             76029     |                                 76029     |                 64624     |