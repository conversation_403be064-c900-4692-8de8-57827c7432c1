# app_service_area_warehouse_cost_wi
* comment: 仓储成本汇总
* last_data_modified_time: 2025-09-17 12:03:00

# schema:
CREATE TABLE summerfarm_tech.`app_service_area_warehouse_cost_wi` (
  `year` STRING COMMENT '年',
  `week_of_year` STRING COMMENT '周数',
  `monday` STRING COMMENT '周一',
  `sunday` STRING COMMENT '周天',
  `service_area` STRING COMMENT '服务区域',
  `total_storage_amt` DECIMAL(38,18) COMMENT '总仓储费',
  `self_storage_amt` DECIMAL(38,18) COMMENT '自营总仓储费',
  `heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶总仓储费',
  `warehouse_fixed_amt` DECIMAL(38,18) COMMENT '仓储固资折旧费',
  `trunk_fixed_amt` DECIMAL(38,18) COMMENT '干线固资折旧费',
  `total_out_sku_cnt` BIGINT COMMENT '总出库总件数',
  `self_out_sku_cnt` BIGINT COMMENT '自营出库总件数',
  `heytea_out_sku_cnt` BIGINT COMMENT '喜茶出库总件数',
  `product_loss_amt` DECIMAL(38,18) COMMENT '产品损耗费',
  `self_amt` DECIMAL(38,18) COMMENT '自提费用',
  `allocate_amt` DECIMAL(38,18) COMMENT '调拨费用',
  `nodelivery_amt` DECIMAL(38,18) COMMENT '非履约费用',
  `trunk_total_amt` DECIMAL(38,18) COMMENT '干线费',
  `self_trunk_amt` DECIMAL(38,18) COMMENT '自营干线费',
  `heytea_trunk_amt` DECIMAL(38,18) COMMENT '喜茶干线费',
  `self_trunk_km_cnt` DECIMAL(38,18) COMMENT '自营干线总公里数',
  `big_cust_total_amt` DECIMAL(38,18) COMMENT '大客户用车金额',
  `out_point_cnt` BIGINT COMMENT '外区点位数',
  `in_point_cnt` BIGINT COMMENT '内区点位数',
  `total_point_cnt` BIGINT COMMENT '总点位数',
  `heytea_point_cnt` BIGINT COMMENT '喜茶点位数',
  `self_point_cnt` BIGINT COMMENT '自营点位数',
  `heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送费',
  `self_delivery_amt` DECIMAL(38,18) COMMENT '自营配送费',
  `total_delivery_amt` DECIMAL(38,18) COMMENT '总配送费'
)
COMMENT '仓储成本汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025","5":"2025","6":"2025","7":"2025","8":"2025","9":"2025"},"week_of_year":{"0":"37","1":"37","2":"37","3":"37","4":"37","5":"37","6":"37","7":"37","8":"37","9":"37"},"monday":{"0":"20250908","1":"20250908","2":"20250908","3":"20250908","4":"20250908","5":"20250908","6":"20250908","7":"20250908","8":"20250908","9":"20250908"},"sunday":{"0":"20250914","1":"20250914","2":"20250914","3":"20250914","4":"20250914","5":"20250914","6":"20250914","7":"20250914","8":"20250914","9":"20250914"},"service_area":{"0":"None","1":"华东","2":"华中","3":"华北","4":"华南","5":"华西","6":"广西","7":"昆明","8":"福建","9":"贵阳"},"total_storage_amt":{"0":"0","1":"355230.74000000001","2":"85946.9805267","3":"6406.190000000001","4":"100268.66999999999","5":"30015.0544","6":"9674.450000000001","7":"3368.51","8":"19887.55","9":"5100.099999999999"},"self_storage_amt":{"0":"0","1":"276508.79000000001","2":"61667.8445267","3":"6406.190000000001","4":"100268.66999999999","5":"30015.0544","6":"9674.450000000001","7":"3368.51","8":"19887.55","9":"5100.099999999999"},"heytea_storage_amt":{"0":"0","1":"78721.95000000001","2":"24279.136","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"warehouse_fixed_amt":{"0":"8066.740000000001","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"trunk_fixed_amt":{"0":"0","1":"6040.26","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"total_out_sku_cnt":{"0":"178571","1":"8405","2":"35132","3":"6719","4":"53832","5":"24346","6":"3390","7":"0","8":"11519","9":"1028"},"self_out_sku_cnt":{"0":"141720","1":"5586","2":"32958","3":"6628","4":"49108","5":"22801","6":"3299","7":"0","8":"10502","9":"1021"},"heytea_out_sku_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"product_loss_amt":{"0":"22383.65","1":"0","2":"19.75","3":"1150.75","4":"559.23","5":"3513.11","6":"8.67","7":"0","8":"1433.56","9":"558.44"},"self_amt":{"0":"0","1":"12309.01","2":"5880","3":"763","4":"15963.1","5":"7200","6":"0","7":"0","8":"0","9":"0"},"allocate_amt":{"0":"0","1":"42950","2":"4810","3":"0","4":"15380","5":"1900","6":"0","7":"0","8":"0","9":"0"},"nodelivery_amt":{"0":"0","1":"55259.01","2":"10690","3":"763","4":"31343.1","5":"9100","6":"0","7":"0","8":"0","9":"0"},"trunk_total_amt":{"0":"0","1":"152483.07","2":"47910","3":"3800","4":"55450","5":"13440","6":"5250","7":"0","8":"18000","9":"0"},"self_trunk_amt":{"0":"0","1":"152483.07","2":"47910","3":"3800","4":"55450","5":"13440","6":"5250","7":"0","8":"18000","9":"0"},"heytea_trunk_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"self_trunk_km_cnt":{"0":"0","1":"7156.08000000000009","2":"2515.18999999999999","3":"193.48","4":"1182.96","5":"607.75","6":"830.8","7":"0","8":"1166.2200000000001","9":"0"},"big_cust_total_amt":{"0":"0","1":"900","2":"0","3":"0","4":"200","5":"0","6":"0","7":"0","8":"0","9":"0"},"out_point_cnt":{"0":"0","1":"726","2":"2776","3":"0","4":"391","5":"185","6":"0","7":"0","8":"72","9":"0"},"in_point_cnt":{"0":"0","1":"29014","2":"6120","3":"1499","4":"11334","5":"5343","6":"793","7":"228","8":"2477","9":"284"},"total_point_cnt":{"0":"0","1":"29740","2":"8896","3":"1499","4":"11725","5":"5528","6":"793","7":"228","8":"2549","9":"284"},"heytea_point_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"self_point_cnt":{"0":"0","1":"29740","2":"8896","3":"1499","4":"11725","5":"5528","6":"793","7":"228","8":"2549","9":"284"},"heytea_deliver_amt":{"0":"0","1":"7687","2":"1190","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"self_delivery_amt":{"0":"0","1":"634848.2","2":"202883.58","3":"22042","4":"223800.3","5":"111826.4","6":"14862.5","7":"4332","8":"50856.04","9":"6740"},"total_delivery_amt":{"0":"0","1":"642535.2","2":"204073.58","3":"22042","4":"223800.3","5":"111826.4","6":"14862.5","7":"4332","8":"50856.04","9":"6740"},"ds":{"0":"20250910","1":"20250910","2":"20250910","3":"20250910","4":"20250910","5":"20250910","6":"20250910","7":"20250910","8":"20250910","9":"20250910"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   total_out_sku_cnt |   self_out_sku_cnt |   heytea_out_sku_cnt |   out_point_cnt |   in_point_cnt |   total_point_cnt |   heytea_point_cnt |   self_point_cnt |
|:------|--------------------:|-------------------:|---------------------:|----------------:|---------------:|------------------:|-------------------:|-----------------:|
| count |               10    |              10    |                   10 |          10     |          10    |             10    |                 10 |            10    |
| mean  |            32294.2  |           27362.3  |                    0 |         415     |        5709.2  |           6124.2  |                  0 |          6124.2  |
| std   |            54205.7  |           43221.2  |                    0 |         863.024 |        8940.97 |           9226.1  |                  0 |          9226.1  |
| min   |                0    |               0    |                    0 |           0     |           0    |              0    |                  0 |             0    |
| 25%   |             4222.25 |            3870.75 |                    0 |           0     |         411.25 |            411.25 |                  0 |           411.25 |
| 50%   |             9962    |            8565    |                    0 |          36     |        1988    |           2024    |                  0 |          2024    |
| 75%   |            32435.5  |           30418.8  |                    0 |         339.5   |        5925.75 |           8054    |                  0 |          8054    |
| max   |           178571    |          141720    |                    0 |        2776     |       29014    |          29740    |                  0 |         29740    |