# app_saas_merchant_store_order_analysis_month_df
* comment: saas-门店订货分析-月
* last_data_modified_time: 2025-09-18 02:19:39

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_order_analysis_month_df` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `store_id` BIGINT COMMENT '门店id',
  `average_order_period` DECIMAL(38,18) COMMENT '平均订货周期',
  `average_order_period_last_period` DECIMAL(38,18) COMMENT '上周期平均订货周期',
  `average_order_period_upper_period` DECIMAL(38,18) COMMENT '平均订货周期环比',
  `order_amount` BIGINT COMMENT '订货数量',
  `order_amount_last_period` BIGINT COMMENT '上周期订货数量',
  `order_amount_upper_period` DECIMAL(38,18) COMMENT '订货数量环比',
  `order_price` DECIMAL(38,18) COMMENT '订货金额',
  `order_price_last_period` DECIMAL(38,18) COMMENT '上周期订货金额',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '订货金额环比',
  `last_order_time` STRING COMMENT '最后订货日期 yyyy-MM-dd',
  `last_order_amount` BIGINT COMMENT '最后订货数量',
  `last_order_price` DECIMAL(38,18) COMMENT '最后订货金额'
)
COMMENT 'saas-门店订货分析-月'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"6","4":"6"},"time_tag":{"0":"20220601","1":"20220601","2":"20220601","3":"20220701","4":"20220701"},"store_id":{"0":"1","1":"3","2":"5","3":"29","4":"33"},"average_order_period":{"0":"1","1":"1","2":"1","3":"1.25","4":"1.2"},"average_order_period_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"average_order_period_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_amount":{"0":"6","1":"2","2":"1","3":"175","4":"231"},"order_amount_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_amount_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_price":{"0":"174","1":"23.02","2":"0.01","3":"9317.9","4":"7791.44"},"order_price_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_price_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"last_order_time":{"0":"2022-06-19","1":"2022-06-19","2":"2022-06-02","3":"2022-07-31","4":"2022-07-30"},"last_order_amount":{"0":"5","1":"1","2":"1","3":"17","4":"90"},"last_order_price":{"0":"148","1":"23","2":"0.01","3":"854.3","4":"2877.98"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   store_id |   order_amount |   order_amount_last_period |   last_order_amount |
|:------|------------:|-----------:|---------------:|---------------------------:|--------------------:|
| count |  10000      |   10000    |      10000     |                 10000      |          10000      |
| mean  |      9.8072 |    1706.76 |        162.083 |                    79.7272 |             20.6057 |
| std   |      6.1503 |   13427.6  |       1358.33  |                   649.403  |            272.414  |
| min   |      2      |       1    |          1     |                     0      |              1      |
| 25%   |      4      |     461    |         14     |                     3      |              2      |
| 50%   |      8      |    1014    |         33     |                    24      |              4      |
| 75%   |     14      |    1694    |         68     |                    58      |              7      |
| max   |     44      |  348361    |      35161     |                 20115      |           7721      |