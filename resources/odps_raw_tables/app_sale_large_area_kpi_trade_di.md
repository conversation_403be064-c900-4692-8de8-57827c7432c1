# app_sale_large_area_kpi_trade_di
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:47:28

# schema:
CREATE TABLE summerfarm_tech.`app_sale_large_area_kpi_trade_di` (
  `date` STRING COMMENT '日期',
  `large_area_name` STRING COMMENT '运营服务大区',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `lose_cust_cnt` BIGINT COMMENT '交易流失客户数（90天内活跃用户近60天未下单客户数）',
  `lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"large_area_name":{"0":"成都大区","1":"福州大区","2":"重庆大区","3":"可可快递服务区","4":"南宁大区"},"order_origin_total_amt":{"0":"167578.7","1":"203879.46","2":"152910.43","3":"0","4":"97256.74"},"order_real_total_amt":{"0":"162900.62","1":"193729.05","2":"147166.1","3":"0","4":"92750.66"},"order_cust_cnt":{"0":"333","1":"358","2":"283","3":"0","4":"140"},"order_cust_arpu":{"0":"503.239339339339339339","1":"569.495698324022346369","2":"540.319540636042402827","3":"0","4":"694.691"},"order_cnt":{"0":"395","1":"428","2":"344","3":"0","4":"169"},"lose_cust_cnt":{"0":"454","1":"434","2":"295","3":"1","4":"183"},"lose_cust_ratio":{"0":"0.1312138728323699","1":"0.1163227016885554","2":"0.1188078936770036","3":"0.3333333333333333","4":"0.1342626559060895"},"delivery_origin_total_amt":{"0":"116943.73","1":"92145.3","2":"104129.9","3":"0","4":"27282.58"},"delivery_real_total_amt":{"0":"113507.76","1":"87601.319999999999999999","2":"100620.679999999999999999","3":"0","4":"26462.78"},"delivery_cust_cnt":{"0":"279","1":"234","2":"267","3":"0","4":"48"},"delivery_origin_profit":{"0":"15165.47","1":"14610.05","2":"16077.29","3":"0","4":"2249.64"},"delivery_real_profit":{"0":"11729.5","1":"10066.069999999999999999","2":"12568.069999999999999999","3":"0","4":"1429.84"},"delivery_after_profit":{"0":"4383.357720982035528186","1":"-31.10642052948024358","2":"3238.445190908257294061","3":"0","4":"-173.538135410351888768"},"delivery_days_avg":{"0":"1","1":"1","2":"1","3":"0","4":"1"},"delivery_point_cnt":{"0":"289","1":"244","2":"279","3":"0","4":"49"},"delivery_amt":{"0":"7346.142279017964471814","1":"10097.176420529480243579","2":"9329.624809091742705938","3":"0","4":"1603.378135410351888768"},"new_delivery_origin_total_amt":{"0":"1760.36","1":"2408.6","2":"4622.1","3":"0","4":"1826"},"new_delivery_real_total_amt":{"0":"1682.36","1":"2199.4","2":"4516.909999999999999999","3":"0","4":"1759"},"new_delivery_cust_cnt":{"0":"9","1":"10","2":"17","3":"0","4":"4"},"new_delivery_real_profit":{"0":"157.66","1":"277.09","2":"563.769999999999999999","3":"0","4":"106.71"},"old_delivery_origin_total_amt":{"0":"115183.37","1":"89736.7","2":"99507.8","3":"0","4":"25456.58"},"old_delivery_real_total_amt":{"0":"111825.4","1":"85401.919999999999999999","2":"96103.77","3":"0","4":"24703.78"},"old_delivery_cust_cnt":{"0":"270","1":"224","2":"250","3":"0","4":"44"},"old_delivery_real_profit":{"0":"11571.84","1":"9788.979999999999999999","2":"12004.3","3":"0","4":"1323.13"},"order_sku_cnt":{"0":"245","1":"243","2":"224","3":"0","4":"114"},"order_sku_weight":{"0":"10306.83","1":"10993.31999999999","2":"8460.069999999998","3":"0","4":"5216.770000000001"},"delivery_sku_cnt":{"0":"208","1":"193","2":"214","3":"0","4":"57"},"delivery_sku_weight":{"0":"5931.02","1":"5967.04","2":"7352.450000000001","3":"0","4":"1261.7"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   order_cust_cnt |   order_cnt |   lose_cust_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|-----------------:|------------:|----------------:|--------------------:|---------------------:|------------------------:|------------------------:|----------------:|-------------------:|
| count |           17     |      17     |            17   |              17     |               17     |                 17      |                  17     |          17     |             17     |
| mean  |          418.941 |     496.294 |           460   |             433.765 |              450.471 |                 20.5294 |                 413.235 |         210.941 |            206.471 |
| std   |          454.187 |     538.439 |           477.7 |             490.267 |              507.121 |                 25.3553 |                 465.405 |         163.573 |            172.523 |
| min   |            0     |       0     |             0   |               0     |                0     |                  0      |                   0     |           0     |              0     |
| 25%   |           46     |      54     |            71   |              38     |               38     |                  3      |                  35     |          50     |             41     |
| 50%   |          283     |     344     |           414   |             267     |              279     |                 10      |                 250     |         224     |            208     |
| 75%   |          562     |     689     |           615   |             615     |              674     |                 24      |                 593     |         311     |            361     |
| max   |         1536     |    1826     |          1696   |            1572     |             1631     |                 85      |                1487     |         509     |            543     |