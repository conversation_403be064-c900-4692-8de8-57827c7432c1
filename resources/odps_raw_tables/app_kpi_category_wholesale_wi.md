# app_kpi_category_wholesale_wi
* comment: 交易口径kpi指标日汇总
* last_data_modified_time: 2025-09-18 03:14:35

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_category_wholesale_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `sku_type` STRING COMMENT '商品类型; 自营/代仓',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `order_gmv_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `deliver_cust_cnt` BIGINT COMMENT '履约客户数',
  `deliver_cust_arpu` DECIMAL(38,18) COMMENT '履约ARPU(应付总金额/客户数)',
  `deliver_order_cnt` BIGINT COMMENT '履约订单数',
  `deliver_order_avg` DECIMAL(38,18) COMMENT '履约订单均价(应付总金额/订单数)',
  `deliver_cost_amt` DECIMAL(38,18) COMMENT '履约总成本',
  `deliver_gross_profit` DECIMAL(38,18) COMMENT '履约毛利润(应付总金额-总成本)',
  `deliver_gross_profit_rate` DECIMAL(38,18) COMMENT '履约毛利率'
)
COMMENT '交易口径kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025"},"week_of_year":{"0":"38","1":"38","2":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921"},"sku_type":{"0":"自营","1":"自营","2":"自营"},"category":{"0":"乳制品","1":"其他","2":"鲜果"},"order_gmv_amt":{"0":"1036921.4","1":"80845.95","2":"37257.2"},"deliver_gmv_amt":{"0":"506557.7","1":"75945.95","2":"0"},"deliver_cust_cnt":{"0":"8","1":"5","2":"0"},"deliver_cust_arpu":{"0":"63319.7125","1":"15189.19","2":"0"},"deliver_order_cnt":{"0":"30","1":"18","2":"0"},"deliver_order_avg":{"0":"16885.256666666666666667","1":"4219.219444444444444444","2":"0"},"deliver_cost_amt":{"0":"509011.86","1":"72479.19","2":"0"},"deliver_gross_profit":{"0":"-2454.16","1":"3466.76","2":"0"},"deliver_gross_profit_rate":{"0":"-0.00484477878828019","1":"0.045647727100655137","2":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   deliver_cust_cnt |   deliver_order_cnt |
|:------|---------------:|-------------------:|--------------------:|
| count |              3 |            3       |              3      |
| mean  |             38 |            4.33333 |             16      |
| std   |              0 |            4.04145 |             15.0997 |
| min   |             38 |            0       |              0      |
| 25%   |             38 |            2.5     |              9      |
| 50%   |             38 |            5       |             18      |
| 75%   |             38 |            6.5     |             24      |
| max   |             38 |            8       |             30      |