# app_sale_city_kpi_trade_di
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:29:17

# schema:
CREATE TABLE summerfarm_tech.`app_sale_city_kpi_trade_di` (
  `date` STRING COMMENT '日期',
  `administrative_city` STRING COMMENT '注册行政城市',
  `zone_name` STRING COMMENT '销售区域名称',
  `m1` STRING COMMENT '城市负责人（M1）',
  `m2` STRING COMMENT '区域负责人（M2）',
  `m3` STRING COMMENT '部门负责人（M3）',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `lose_cust_cnt` BIGINT COMMENT '交易流失客户数（90天内活跃用户近60天未下单客户数）',
  `lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `at_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约应付总金额',
  `at_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付总金额',
  `at_delivery_cust_cnt` BIGINT COMMENT '(乳品)安佳铁塔履约活跃客户数',
  `at_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付毛利润',
  `noat_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约应付总金额',
  `noat_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付总金额',
  `noat_delivery_cust_cnt` BIGINT COMMENT '(乳品)非安佳铁塔履约活跃客户数',
  `noat_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付毛利润',
  `timing_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额',
  `timing_delivery_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额',
  `timing_delivery_cust_cnt` BIGINT COMMENT '省心送履约活跃客户数',
  `timing_delivery_real_profit` DECIMAL(38,18) COMMENT '省心送履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"administrative_city":{"0":"None","1":"来宾市","2":"张家界市","3":"中山市","4":"临沧市"},"zone_name":{"0":"无","1":"无","2":"长沙","3":"大粤西","4":"昆明"},"m1":{"0":"无","1":"无","2":"陈锐石","3":"陈俊生","4":"蒋柳选"},"m2":{"0":"无","1":"无","2":"彭琨","3":"陈欲豪","4":"孙日达"},"m3":{"0":"无","1":"无","2":"吕建杰","3":"孙日达","4":"孙日达"},"order_origin_total_amt":{"0":"0","1":"0","2":"362.4","3":"49161.03","4":"1674"},"order_real_total_amt":{"0":"0","1":"0","2":"362.4","3":"47933.63","4":"1620"},"order_cust_cnt":{"0":"0","1":"0","2":"2","3":"101","4":"1"},"order_cust_arpu":{"0":"0","1":"0","2":"181.2","3":"486.742871287128712871","4":"1674"},"order_cnt":{"0":"0","1":"0","2":"2","3":"116","4":"1"},"lose_cust_cnt":{"0":"0","1":"0","2":"1","3":"125","4":"1"},"lose_cust_ratio":{"0":"0","1":"0","2":"0.25","3":"0.1132246376811594","4":"0.1666666666666667"},"delivery_origin_total_amt":{"0":"0","1":"0","2":"136","3":"43281.38","4":"1674"},"delivery_real_total_amt":{"0":"0","1":"0","2":"136","3":"41297.920000000000000001","4":"1620"},"delivery_cust_cnt":{"0":"0","1":"0","2":"1","3":"105","4":"1"},"delivery_origin_profit":{"0":"0","1":"0","2":"54.19","3":"6757.64","4":"111"},"delivery_real_profit":{"0":"0","1":"0","2":"54.19","3":"4774.180000000000000001","4":"57"},"delivery_after_profit":{"0":"-33.123423574327411277","1":"0","2":"54.19","3":"827.106716423620369262","4":"57"},"delivery_days_avg":{"0":"0","1":"0","2":"1","3":"1","4":"1"},"delivery_point_cnt":{"0":"0","1":"0","2":"1","3":"106","4":"1"},"delivery_amt":{"0":"33.123423574327411277","1":"0","2":"0","3":"3947.073283576379630739","4":"0"},"new_delivery_origin_total_amt":{"0":"0","1":"0","2":"0","3":"479","4":"0"},"new_delivery_real_total_amt":{"0":"0","1":"0","2":"0","3":"422.900000000000000001","4":"0"},"new_delivery_cust_cnt":{"0":"0","1":"0","2":"0","3":"2","4":"0"},"new_delivery_real_profit":{"0":"0","1":"0","2":"0","3":"67.600000000000000001","4":"0"},"old_delivery_origin_total_amt":{"0":"0","1":"0","2":"136","3":"42802.38","4":"1674"},"old_delivery_real_total_amt":{"0":"0","1":"0","2":"136","3":"40875.02","4":"1620"},"old_delivery_cust_cnt":{"0":"0","1":"0","2":"1","3":"103","4":"1"},"old_delivery_real_profit":{"0":"0","1":"0","2":"54.19","3":"4706.58","4":"57"},"at_delivery_origin_total_amt":{"0":"0","1":"0","2":"0","3":"12287","4":"1413"},"at_delivery_real_total_amt":{"0":"0","1":"0","2":"0","3":"12032.17","4":"1398"},"at_delivery_cust_cnt":{"0":"0","1":"0","2":"0","3":"16","4":"1"},"at_delivery_real_profit":{"0":"0","1":"0","2":"0","3":"-39.03","4":"0"},"noat_delivery_origin_total_amt":{"0":"0","1":"0","2":"0","3":"8581","4":"261"},"noat_delivery_real_total_amt":{"0":"0","1":"0","2":"0","3":"7917.7","4":"222"},"noat_delivery_cust_cnt":{"0":"0","1":"0","2":"0","3":"21","4":"1"},"noat_delivery_real_profit":{"0":"0","1":"0","2":"0","3":"1155.7","4":"57"},"timing_delivery_origin_total_amt":{"0":"0","1":"0","2":"0","3":"1884","4":"0"},"timing_delivery_real_total_amt":{"0":"0","1":"0","2":"0","3":"1800","4":"0"},"timing_delivery_cust_cnt":{"0":"0","1":"0","2":"0","3":"2","4":"0"},"timing_delivery_real_profit":{"0":"0","1":"0","2":"0","3":"-64","4":"0"},"order_sku_cnt":{"0":"0","1":"0","2":"7","3":"139","4":"2"},"order_sku_weight":{"0":"0","1":"0","2":"49.45","3":"2512.16","4":"77.09999999999999"},"delivery_sku_cnt":{"0":"0","1":"0","2":"2","3":"120","4":"2"},"delivery_sku_weight":{"0":"0","1":"0","2":"7.9","3":"3311.679999999999","4":"77.09999999999999"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   order_cust_cnt |   order_cnt |   lose_cust_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   at_delivery_cust_cnt |   noat_delivery_cust_cnt |   timing_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|-----------------:|------------:|----------------:|--------------------:|---------------------:|------------------------:|------------------------:|-----------------------:|-------------------------:|---------------------------:|----------------:|-------------------:|
| count |         188      |    188      |        188      |            188      |             188      |               188       |                188      |              188       |                 188      |                  188       |        188      |           188      |
| mean  |          37.883  |     44.8777 |         41.5479 |             39.2234 |              40.734  |                 1.85638 |                 37.367  |                5.68617 |                  10.3777 |                    1.39362 |         43.9894 |            44.7713 |
| std   |          76.9664 |     92.3361 |         72.4819 |             79.7769 |              84.4628 |                 4.03933 |                 76.1546 |               11.0759  |                  21.0162 |                    3.59059 |         67.2925 |            68.2801 |
| min   |           0      |      0      |          0      |              0      |               0      |                 0       |                  0      |                0       |                   0      |                    0       |          0      |             0      |
| 25%   |           0      |      0      |          0      |              0      |               0      |                 0       |                  0      |                0       |                   0      |                    0       |          0      |             0      |
| 50%   |           5      |      5      |          9      |              3.5    |               3.5    |                 0       |                  3.5    |                1       |                   1      |                    0       |          9      |             7.5    |
| 75%   |          40      |     49      |         52      |             40.75   |              41.75   |                 2       |                 38.25   |                6       |                  11      |                    1       |         60.5    |            65.5    |
| max   |         554      |    691      |        413      |            615      |             673      |                23       |                592      |               85       |                 158      |                   36       |        370      |           390      |