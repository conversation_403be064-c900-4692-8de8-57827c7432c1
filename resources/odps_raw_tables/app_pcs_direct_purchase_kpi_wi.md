# app_pcs_direct_purchase_kpi_wi
* comment: 直采kpi
* last_data_modified_time: 2025-09-18 03:21:56

# schema:
CREATE TABLE summerfarm_tech.`app_pcs_direct_purchase_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `direct_purchase_amt` DECIMAL(38,18) COMMENT '直采金额',
  `purchases_amt` DECIMAL(38,18) COMMENT '采购金额（非直采）',
  `cost_flow_amt` DECIMAL(38,18) COMMENT '成本浮动金额',
  `direct_delivery_origin_amt` DECIMAL(38,18) COMMENT '直采履约应付金额',
  `direct_delivery_real_amt` DECIMAL(38,18) COMMENT '直采履约实付金额',
  `direct_delivery_market_amt` DECIMAL(38,18) COMMENT '直采营销费用',
  `direct_delivery_cost_amt` DECIMAL(38,18) COMMENT '直采成本费用',
  `direct_init_amt` DECIMAL(38,18) COMMENT '直采期末库存金额',
  `direct_after_sale_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责售后金额',
  `direct_damage_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责货损金额'
)
COMMENT '直采kpi'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025"},"week_of_year":{"0":"38"},"monday":{"0":"20250915"},"sunday":{"0":"20250921"},"direct_purchase_amt":{"0":"390488.46"},"purchases_amt":{"0":"4484086.17"},"cost_flow_amt":{"0":"465.51153252578287814"},"direct_delivery_origin_amt":{"0":"436792.86"},"direct_delivery_real_amt":{"0":"423512.66000000000000001"},"direct_delivery_market_amt":{"0":"13280.19999999999999999"},"direct_delivery_cost_amt":{"0":"320593.42"},"direct_init_amt":{"0":"755209.56"},"direct_after_sale_pcs_amt":{"0":"762.92"},"direct_damage_pcs_amt":{"0":"0"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |
|:------|---------------:|
| count |              1 |
| mean  |             38 |
| std   |            nan |
| min   |             38 |
| 25%   |             38 |
| 50%   |             38 |
| 75%   |             38 |
| max   |             38 |