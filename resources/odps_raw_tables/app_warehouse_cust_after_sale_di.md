# app_warehouse_cust_after_sale_di
* comment: 库存仓+客户+sku售后数据汇总
* last_data_modified_time: 2025-09-18 03:00:21

# schema:
CREATE TABLE summerfarm_tech.`app_warehouse_cust_after_sale_di` (
  `date` STRING COMMENT '日期',
  `register_province` STRING COMMENT '客户注册省',
  `register_city` STRING COMMENT '客户注册市',
  `register_area` STRING COMMENT '客户注册区',
  `cust_class` STRING COMMENT '客户类型',
  `brand_alias` STRING COMMENT '品牌',
  `order_type` STRING COMMENT '订单类型：省心送or其他',
  `warehouse_no` BIGINT COMMENT '库存仓编号',
  `warehouse_name` STRING COMMENT '库存仓',
  `sku_id` STRING COMMENT 'sku编号',
  `sku_disc` STRING COMMENT '商品描述',
  `spu_name` STRING COMMENT '商品名称',
  `category_1` STRING COMMENT '一级类目',
  `category_4` STRING COMMENT '四级类目',
  `after_sale_sku_cnt` BIGINT COMMENT '已到货售后数量',
  `after_sale_cnt` BIGINT COMMENT '已到货售后次数',
  `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额',
  `after_sale_short_amt` DECIMAL(38,18) COMMENT '售后缺货金额',
  `after_sale_reissue_amt` DECIMAL(38,18) COMMENT '售后补发金额',
  `deliver_total_amt` DECIMAL(38,18) COMMENT '配送实付总金额',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额'
)
COMMENT '库存仓+客户+sku售后数据汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"register_province":{"0":"上海","1":"上海","2":"上海","3":"上海","4":"上海","5":"上海","6":"上海","7":"上海","8":"上海","9":"上海"},"register_city":{"0":"上海市","1":"上海市","2":"上海市","3":"上海市","4":"上海市","5":"上海市","6":"上海市","7":"上海市","8":"上海市","9":"上海市"},"register_area":{"0":"嘉定区","1":"嘉定区","2":"奉贤区","3":"奉贤区","4":"徐汇区","5":"普陀区","6":"普陀区","7":"松江区","8":"浦东新区","9":"浦东新区"},"cust_class":{"0":"普通（非品牌）","1":"普通（非品牌）","2":"普通（非品牌）","3":"普通（非品牌）","4":"普通（非品牌）","5":"大客户（非茶百道）","6":"大客户（非茶百道）","7":"普通（非品牌）","8":"大客户（非茶百道）","9":"普通（非品牌）"},"brand_alias":{"0":"无","1":"无","2":"无","3":"无","4":"无","5":"楼下酸奶","6":"楼下酸奶","7":"无","8":"楼下酸奶","9":"无"},"order_type":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"warehouse_no":{"0":"10","1":"10","2":"2","3":"10","4":"10","5":"10","6":"10","7":"10","8":"10","9":"10"},"warehouse_name":{"0":"嘉兴总仓","1":"嘉兴总仓","2":"上海总仓","3":"嘉兴总仓","4":"嘉兴总仓","5":"嘉兴总仓","6":"嘉兴总仓","7":"嘉兴总仓","8":"嘉兴总仓","9":"嘉兴总仓"},"sku_id":{"0":"16823457636","1":"17320871038","2":"N001S01R005","3":"528177060516","4":"528177060516","5":"16830404740","6":"5432522626","7":"533172712143","8":"5432522626","9":"533017061110"},"sku_disc":{"0":"净重4.8-5.5斤\/普通\/单果4-9g","1":"净重12-12.5斤\/普通\/单果400g+","2":"1L*12瓶","3":"净重38-40斤\/C级\/单果160±20g","4":"净重38-40斤\/C级\/单果160±20g","5":"净重9.5-10.5斤\/普通\/标准规格（楼下专用）","6":"净重12-12.5斤\/一级\/单果500g+楼下酸奶专用","7":"净重20-21斤\/普通\/单果1000克±200克\/9-12头","8":"净重12-12.5斤\/一级\/单果500g+楼下酸奶专用","9":"净重22.5-23.5斤\/普通\/单果1.8斤±（熟果）"},"spu_name":{"0":"晴王青提(阳光玫瑰)","1":"红凯特芒","2":"安佳淡奶油","3":"湖北夏橙（榨汁专用）","4":"湖北夏橙（榨汁专用）","5":"陕西小柿子(软)","6":"青凯特芒","7":"四川凯特芒果","8":"青凯特芒","9":"四川青凯特芒果"},"category_1":{"0":"鲜果","1":"鲜果","2":"乳制品","3":"鲜果","4":"鲜果","5":"鲜果","6":"鲜果","7":"鲜果","8":"鲜果","9":"鲜果"},"category_4":{"0":"提子","1":"芒果","2":"搅打型稀奶油","3":"橙","4":"橙","5":"柿子","6":"芒果","7":"芒果","8":"芒果","9":"芒果"},"after_sale_sku_cnt":{"0":"2400","1":"947","2":"8","3":"1","4":"1","5":"85","6":"864","7":"2345","8":"1333","9":"3595"},"after_sale_cnt":{"0":"1","1":"1","2":"1","3":"1","4":"1","5":"1","6":"1","7":"1","8":"1","9":"1"},"after_sale_amt":{"0":"24.11","1":"9.31","2":"314","3":"116","4":"116","5":"0.85","6":"9.21","7":"13.75","8":"14.21","9":"27.28"},"after_sale_short_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"after_sale_reissue_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"deliver_total_amt":{"0":"96.350000000000000001","1":"59","2":"471","3":"0","4":"0","5":"288","6":"64","7":"0","8":"320","9":"174.55"},"coupon_amt":{"0":"2.65","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"5.45"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   after_sale_sku_cnt |   after_sale_cnt |
|:------|---------------:|---------------------:|-----------------:|
| count |       173      |                173   |       173        |
| mean  |        45.5145 |               1287.3 |         1.06358  |
| std   |        41.59   |               2764.1 |         0.326188 |
| min   |         2      |                  0   |         1        |
| 25%   |        10      |                  2   |         1        |
| 50%   |        48      |                300   |         1        |
| 75%   |        69      |               1272   |         1        |
| max   |       155      |              23000   |         4        |