# app_gorse_offline_recommend_df
* comment: 推荐算法结果表
* last_data_modified_time: 2025-09-18 08:00:20

# schema:
CREATE TABLE summerfarm_tech.`app_gorse_offline_recommend_df` (
  `m_id` BIGINT COMMENT '用户编号',
  `items` STRING COMMENT '商品列表'
)
COMMENT '推荐算法结果表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"m_id":{"0":"2","1":"3","2":"10","3":"12","4":"13"},"items":{"0":"28206,G002S02R010,647471534,5450535868,Q001L01S001,611260163,17142611221,28482,647471767,N001S01R005,16823457065,17320871038,K001N01Z001,17142611557,N001S01R002","1":"647471767,28206,5456725335,611184462,16823457786,5417640046,16823457065,611184320,100803,17142611221,647471534,Q001L01S001,647471366,N001S01R002,G002S02R010,5450535868,17142611557,N001S01R005,28482,K001N01Z001,611260163,17320871038","2":"28482,17142611221,647471767,605874603137,L001S01R001,G002S02R010,605386061640,647471534,15300532428,16823457065,611184320,N001S01R002,5450535868,N001S01R005,5417640046,871270088857,Q001L01S001,611184462,17320871038,K001N01Z001,28206,2400337515","3":"K001N01Z001,N001S01R005,28206,17142611557,647471767,17320871038,16823457065,Q001L01S001,5450535868,N001S01R002,28482,17142611221,647471534","4":"17320871038,611184320,15300532428,N001S01R002,G002S02R010,17142611387,871270088857,K001N01Z001,28206,605386061640,Q001L01S001,17142611221,605874603137,28482,5450535868,L001S01R001,2400337515,16823457065,611184462,N001S01R005,647471767,647471534"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |     m_id |
|:------|---------:|
| count |  10000   |
| mean  |  63549.7 |
| std   |  35079   |
| min   |      2   |
| 25%   |  34146.2 |
| 50%   |  64257   |
| 75%   |  93138.2 |
| max   | 126755   |