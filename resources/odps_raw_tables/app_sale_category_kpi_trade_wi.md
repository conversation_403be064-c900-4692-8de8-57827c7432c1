# app_sale_category_kpi_trade_wi
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:47:55

# schema:
CREATE TABLE summerfarm_tech.`app_sale_category_kpi_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025"},"week_of_year":{"0":"38","1":"38","2":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921"},"category":{"0":"鲜果","1":"其他","2":"乳制品"},"order_origin_total_amt":{"0":"2860884.64","1":"1995062.99","2":"6468421.03"},"order_real_total_amt":{"0":"2746935.75","1":"1907403.57","2":"6273761.03"},"order_cust_cnt":{"0":"12188","1":"6538","2":"7153"},"order_cust_arpu":{"0":"234.729622579586478503","1":"305.148820740287549709","2":"904.294845519362505243"},"order_cnt":{"0":"16839","1":"7835","2":"8471"},"delivery_origin_total_amt":{"0":"2900379.3","1":"1932930.51","2":"6189561.25"},"delivery_real_total_amt":{"0":"2803611.960000000000000067","1":"1854467.422222222222222217","2":"6009805.691459096459096516"},"delivery_cust_cnt":{"0":"12582","1":"6734","2":"7263"},"delivery_origin_profit":{"0":"734080.23","1":"365977.66","2":"429778.76"},"delivery_real_profit":{"0":"637312.890000000000000067","1":"287514.572222222222222217","2":"250023.201459096459096516"},"delivery_after_profit":{"0":"196201.763713968945243581","1":"-7130.379682851702558079","2":"-135083.865082726232147891"},"delivery_days_avg":{"0":"1.209187728501033","1":"1.105435105435105","2":"1.098306484923585"},"delivery_point_cnt":{"0":"15774","1":"7621","2":"8168"},"delivery_amt":{"0":"441111.126286031054756486","1":"294644.951905073924780296","2":"385107.066541822691244407"},"new_delivery_origin_total_amt":{"0":"109976.21","1":"62776.51","2":"180838.62"},"new_delivery_real_total_amt":{"0":"105261.879999999999999999","1":"59318.709999999999999998","2":"174745.030000000000000005"},"new_delivery_cust_cnt":{"0":"556","1":"254","2":"268"},"new_delivery_real_profit":{"0":"20757.259999999999999999","1":"8336.209999999999999998","2":"8773.320000000000000005"},"old_delivery_origin_total_amt":{"0":"2790403.09","1":"1870154","2":"6008722.63"},"old_delivery_real_total_amt":{"0":"2698350.080000000000000068","1":"1795148.712222222222222219","2":"5835060.661459096459096511"},"old_delivery_cust_cnt":{"0":"12026","1":"6480","2":"6995"},"old_delivery_real_profit":{"0":"616555.630000000000000068","1":"279178.362222222222222219","2":"241249.881459096459096511"},"order_sku_cnt":{"0":"563","1":"517","2":"153"},"order_sku_weight":{"0":"242479.7599999999","1":"175717.89","2":"225699.3499999998"},"delivery_sku_cnt":{"0":"565","1":"523","2":"150"},"delivery_sku_weight":{"0":"247140.9399999999","1":"168872.11","2":"218129.9099999999"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   order_cust_cnt |   order_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|---------------:|-----------------:|------------:|--------------------:|---------------------:|------------------------:|------------------------:|----------------:|-------------------:|
| count |              3 |             3    |        3    |                3    |                 3    |                   3     |                    3    |           3     |              3     |
| mean  |             38 |          8626.33 |    11048.3  |             8859.67 |             10521    |                 359.333 |                 8500.33 |         411     |            412.667 |
| std   |              0 |          3099.78 |     5024.94 |             3234.47 |              4557.45 |                 170.462 |                 3064.16 |         224.615 |            228.443 |
| min   |             38 |          6538    |     7835    |             6734    |              7621    |                 254     |                 6480    |         153     |            150     |
| 25%   |             38 |          6845.5  |     8153    |             6998.5  |              7894.5  |                 261     |                 6737.5  |         335     |            336.5   |
| 50%   |             38 |          7153    |     8471    |             7263    |              8168    |                 268     |                 6995    |         517     |            523     |
| 75%   |             38 |          9670.5  |    12655    |             9922.5  |             11971    |                 412     |                 9510.5  |         540     |            544     |
| max   |             38 |         12188    |    16839    |            12582    |             15774    |                 556     |                12026    |         563     |            565     |