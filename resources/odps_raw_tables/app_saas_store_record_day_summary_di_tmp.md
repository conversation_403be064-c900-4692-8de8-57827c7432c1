# app_saas_store_record_day_summary_di_tmp
* comment: 出入库汇总天表（含自营仓）
* last_data_modified_time: 2025-08-26 16:57:16

# schema:
CREATE TABLE summerfarm_tech.`app_saas_store_record_day_summary_di_tmp` (
  `day_tag` STRING COMMENT '日期',
  `warehouse_no` BIGINT COMMENT '库存仓ID',
  `warehouse_name` STRING COMMENT '仓库名称',
  `warehouse_provider` STRING COMMENT '仓库服务商',
  `pd_id` BIGINT COMMENT '货品编码',
  `sku` STRING COMMENT 'sku编号',
  `saas_sku_id` BIGINT COMMENT 'saas skuId',
  `category_id` BIGINT COMMENT '类目id',
  `sku_tenant_id` BIGINT COMMENT 'sku租户id',
  `warehouse_tenant_id` BIGINT COMMENT '仓库租户id',
  `opening_quantity` BIGINT COMMENT '期初库存',
  `opening_amount` DECIMAL(38,18) COMMENT '期初金额',
  `ending_quantity` BIGINT COMMENT '期末库存',
  `ending_amount` DECIMAL(38,18) COMMENT '期末金额',
  `allocation_in_quantity` BIGINT COMMENT '调拨入库数量',
  `allocation_in_amount` DECIMAL(38,18) COMMENT '调拨入库金额',
  `purchase_in_quantity` BIGINT COMMENT '采购入库数量',
  `purchase_in_amount` DECIMAL(38,18) COMMENT '采购入库金额',
  `after_sale_in_quantity` BIGINT COMMENT '退货入库数量',
  `after_sale_in_amount` DECIMAL(38,18) COMMENT '退货入库金额',
  `stock_taking_in_quantity` BIGINT COMMENT '盘盈入库数量',
  `stock_taking_in_amount` DECIMAL(38,18) COMMENT '盘盈入库金额',
  `transfer_in_quantity` BIGINT COMMENT '转换入库数量',
  `transfer_in_amount` DECIMAL(38,18) COMMENT '转换入库金额',
  `allocation_abnormal_in_quantity` BIGINT COMMENT '调拨回库数量',
  `allocation_abnormal_in_amount` DECIMAL(38,18) COMMENT '调拨回库金额',
  `other_in_quantity` BIGINT COMMENT '其他入库数量',
  `other_in_amount` DECIMAL(38,18) COMMENT '其他入库金额',
  `in_quantity` BIGINT COMMENT '入库合计数量',
  `in_amount` DECIMAL(38,18) COMMENT '入库合计金额',
  `allocation_out_quantity` BIGINT COMMENT '调拨出库数量',
  `allocation_out_amount` DECIMAL(38,18) COMMENT '调拨出库金额',
  `sale_out_quantity` BIGINT COMMENT '销售出库数量',
  `sale_out_amount` DECIMAL(38,18) COMMENT '销售出库金额',
  `damage_out_quantity` BIGINT COMMENT '货损出库数量',
  `damage_out_amount` DECIMAL(38,18) COMMENT '货损出库金额',
  `stock_taking_out_quantity` BIGINT COMMENT '盘亏出库数量',
  `stock_taking_out_amount` DECIMAL(38,18) COMMENT '盘亏出库金额',
  `transfer_out_quantity` BIGINT COMMENT '转换出库数量',
  `transfer_out_amount` DECIMAL(38,18) COMMENT '转换出库金额',
  `purchase_back_out_quantity` BIGINT COMMENT '采购退货出库数量',
  `purchase_back_out_amount` DECIMAL(38,18) COMMENT '采购退货出库金额',
  `supply_again_out_quantity` BIGINT COMMENT '补货出库数量',
  `supply_again_out_amount` DECIMAL(38,18) COMMENT '补货出库金额',
  `own_self_out_quantity` BIGINT COMMENT '自提销售出库数量',
  `own_self_out_amount` DECIMAL(38,18) COMMENT '自提销售出库金额',
  `other_out_quantity` BIGINT COMMENT '其他出库数量',
  `other_out_amount` DECIMAL(38,18) COMMENT '其他出库金额',
  `out_quantity` BIGINT COMMENT '出库合计数量',
  `out_amount` DECIMAL(38,18) COMMENT '出库合计金额',
  `init_in_quantity` BIGINT COMMENT '期初入库数量',
  `init_in_amount` DECIMAL(38,18) COMMENT '期初入库金额',
  `lock_in_quantity` BIGINT COMMENT '缺货入库数量',
  `lock_in_amount` DECIMAL(38,18) COMMENT '缺货入库金额',
  `intercept_in_quantity` BIGINT COMMENT '拦截入库数量',
  `intercept_in_amount` DECIMAL(38,18) COMMENT '拦截入库金额',
  `out_more_in_quantity` BIGINT COMMENT '多出入库数量',
  `out_more_in_amount` DECIMAL(38,18) COMMENT '多出入库金额',
  `allocation_damage_out_quantity` BIGINT COMMENT '调拨货损出库数量',
  `allocation_damage_out_amount` DECIMAL(38,18) COMMENT '调拨货损出金额'
)
COMMENT '出入库汇总天表（含自营仓）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"day_tag":{"0":"20250825","1":"20250825","2":"20250825","3":"20250825","4":"20250825"},"warehouse_no":{"0":"10","1":"10","2":"10","3":"10","4":"10"},"warehouse_name":{"0":"嘉兴总仓","1":"嘉兴总仓","2":"嘉兴总仓","3":"嘉兴总仓","4":"嘉兴总仓"},"warehouse_provider":{"0":"杭州鲜沐科技有限公司","1":"杭州鲜沐科技有限公司","2":"杭州鲜沐科技有限公司","3":"杭州鲜沐科技有限公司","4":"杭州鲜沐科技有限公司"},"pd_id":{"0":"11706","1":"10819","2":"10818","3":"15144","4":"10833"},"sku":{"0":"1053545862347","1":"1112483401363","2":"1124080348002","3":"1124668017547","4":"1125711253174"},"saas_sku_id":{"0":"116253","1":"114958","2":"114957","3":"120657","4":"114949"},"category_id":{"0":"1053","1":"1112","2":"1124","3":"1124","4":"1125"},"sku_tenant_id":{"0":"85","1":"85","2":"85","3":"85","4":"85"},"warehouse_tenant_id":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"opening_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"opening_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ending_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ending_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"after_sale_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"after_sale_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_taking_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_taking_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"transfer_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"transfer_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_abnormal_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_abnormal_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"sale_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"sale_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"damage_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"damage_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_taking_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_taking_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"transfer_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"transfer_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_back_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_back_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"supply_again_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"supply_again_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"own_self_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"own_self_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"init_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"init_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"lock_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"lock_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"intercept_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"intercept_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"out_more_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"out_more_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_damage_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_damage_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250825","1":"20250825","2":"20250825","3":"20250825","4":"20250825"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |    pd_id |   saas_sku_id |   category_id |   sku_tenant_id |   warehouse_tenant_id |   opening_quantity |   ending_quantity |   allocation_in_quantity |   purchase_in_quantity |   after_sale_in_quantity |   stock_taking_in_quantity |   transfer_in_quantity |   allocation_abnormal_in_quantity |   other_in_quantity |   in_quantity |   allocation_out_quantity |   sale_out_quantity |   damage_out_quantity |   stock_taking_out_quantity |   transfer_out_quantity |   purchase_back_out_quantity |   supply_again_out_quantity |   own_self_out_quantity |   other_out_quantity |   out_quantity |   init_in_quantity |   lock_in_quantity |   intercept_in_quantity |   out_more_in_quantity |   allocation_damage_out_quantity |
|:------|---------------:|---------:|--------------:|--------------:|----------------:|----------------------:|-------------------:|------------------:|-------------------------:|-----------------------:|-------------------------:|---------------------------:|-----------------------:|----------------------------------:|--------------------:|--------------:|--------------------------:|--------------------:|----------------------:|----------------------------:|------------------------:|-----------------------------:|----------------------------:|------------------------:|---------------------:|---------------:|-------------------:|-------------------:|------------------------:|-----------------------:|---------------------------------:|
| count |      1134      |  1134    |       1134    |      1134     |            1134 |             1134      |          1134      |         1134      |              1134        |            1134        |                     1134 |                       1134 |                   1134 |                              1134 |                1134 |   1134        |                      1134 |         1134        |                  1134 |                        1134 |                    1134 |                         1134 |                        1134 |           1134          |                 1134 |    1134        |               1134 |               1134 |                    1134 |                   1134 |                             1134 |
| mean  |        50.8316 | 13016    |     117903    |       878.822 |              85 |                9      |            12.3827 |           12.4171 |                 0.031746 |               0.145503 |                        0 |                          0 |                      0 |                                 0 |                   0 |      0.177249 |                         0 |            0.13933  |                     0 |                           0 |                       0 |                            0 |                           0 |              0.00352734 |                    0 |       0.142857 |                  0 |                  0 |                       0 |                      0 |                                0 |
| std   |        47.1906 |  2260.13 |       2974.42 |       221.81  |               0 |               24.6685 |            58.8089 |           58.7523 |                 0.472414 |               3.34814  |                        0 |                          0 |                      0 |                                 0 |                   0 |      3.37994  |                         0 |            0.734947 |                     0 |                           0 |                       0 |                            0 |                           0 |              0.083955   |                    0 |       0.739061 |                  0 |                  0 |                       0 |                      0 |                                0 |
| min   |        10      | 10746    |     114885    |       589     |              85 |                1      |             0      |            0      |                 0        |               0        |                        0 |                          0 |                      0 |                                 0 |                   0 |      0        |                         0 |            0        |                     0 |                           0 |                       0 |                            0 |                           0 |              0          |                    0 |       0        |                  0 |                  0 |                       0 |                      0 |                                0 |
| 25%   |        10      | 10896.2  |     115108    |       631     |              85 |                1      |             0      |            0      |                 0        |               0        |                        0 |                          0 |                      0 |                                 0 |                   0 |      0        |                         0 |            0        |                     0 |                           0 |                       0 |                            0 |                           0 |              0          |                    0 |       0        |                  0 |                  0 |                       0 |                      0 |                                0 |
| 50%   |        38      | 12253.5  |     117006    |       860     |              85 |                1      |             0.5    |            1      |                 0        |               0        |                        0 |                          0 |                      0 |                                 0 |                   0 |      0        |                         0 |            0        |                     0 |                           0 |                       0 |                            0 |                           0 |              0          |                    0 |       0        |                  0 |                  0 |                       0 |                      0 |                                0 |
| 75%   |        69      | 15074.2  |     120580    |      1121     |              85 |                1      |            10      |           10      |                 0        |               0        |                        0 |                          0 |                      0 |                                 0 |                   0 |      0        |                         0 |            0        |                     0 |                           0 |                       0 |                            0 |                           0 |              0          |                    0 |       0        |                  0 |                  0 |                       0 |                      0 |                                0 |
| max   |       175      | 18274    |     125150    |      1131     |              85 |               85      |          1709      |         1709      |                10        |             100        |                        0 |                          0 |                      0 |                                 0 |                   0 |    100        |                         0 |           15        |                     0 |                           0 |                       0 |                            0 |                           0 |              2          |                    0 |      15        |                  0 |                  0 |                       0 |                      0 |                                0 |