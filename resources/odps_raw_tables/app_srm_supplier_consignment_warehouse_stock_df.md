# app_srm_supplier_consignment_warehouse_stock_df
* comment: 供应商代销入仓库存表
* last_data_modified_time: 2025-09-18 02:11:42

# schema:
CREATE TABLE summerfarm_tech.`app_srm_supplier_consignment_warehouse_stock_df` (
  `supplier_id` BIGINT COMMENT '供应商ID',
  `supplier_name` STRING COMMENT '供应商名称',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '仓库名称',
  `sku` STRING COMMENT 'sku编码',
  `spu_title` STRING COMMENT '商品名称',
  `sku_sub_type` BIGINT COMMENT '商品二级性质，1 自营-代销不入仓、2 自营-代销入仓',
  `stock_quantity` BIGINT COMMENT '供应商库存量',
  `risk_quantity` BIGINT COMMENT '临保风险量',
  `sales_14d` DECIMAL(38,18) COMMENT '近14天销量均值（包含小规格销量）',
  `date_flag` STRING COMMENT '同步时间标记(yyyyMMdd)'
)
COMMENT '供应商代销入仓库存表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"supplier_id":{"0":"nan","1":"nan","2":"nan","3":"nan","4":"nan","5":"nan","6":"nan","7":"nan","8":"nan","9":"nan"},"supplier_name":{"0":"None","1":"None","2":"None","3":"None","4":"None","5":"None","6":"None","7":"None","8":"None","9":"None"},"warehouse_no":{"0":"10","1":"10","2":"10","3":"10","4":"10","5":"10","6":"10","7":"10","8":"10","9":"10"},"warehouse_name":{"0":"嘉兴总仓","1":"嘉兴总仓","2":"嘉兴总仓","3":"嘉兴总仓","4":"嘉兴总仓","5":"嘉兴总仓","6":"嘉兴总仓","7":"嘉兴总仓","8":"嘉兴总仓","9":"嘉兴总仓"},"sku":{"0":"1078611204778","1":"528342844008","2":"533017061110","3":"551405648451","4":"559711785878","5":"580652512325","6":"581431488677","7":"607422512135","8":"613064286841","9":"638704848356"},"spu_title":{"0":"顶焙良品吐司面包专用粉","1":"云南爱媛果冻橙","2":"四川青凯特芒果","3":"山东玉菇甜瓜","4":"浙江本地番茄","5":"云南人参果","6":"德宝无盐黄油","7":"梅维堡全脂牛奶1L","8":"黄牛皮吐司面包袋","9":"雯雯吉祥手指饼干"},"sku_sub_type":{"0":"1","1":"1","2":"1","3":"1","4":"1","5":"1","6":"1","7":"1","8":"2","9":"2"},"stock_quantity":{"0":"2","1":"1","2":"1","3":"2","4":"1","5":"1","6":"2","7":"3","8":"1","9":"1"},"risk_quantity":{"0":"0","1":"1","2":"1","3":"2","4":"1","5":"1","6":"0","7":"0","8":"0","9":"0"},"sales_14d":{"0":"0.2142857142857143","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0.8571428571428571","8":"0","9":"0.6428571428571429"},"date_flag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   supplier_id |   warehouse_no |   sku_sub_type |   stock_quantity |   risk_quantity |
|:------|--------------:|---------------:|---------------:|-----------------:|----------------:|
| count |      2379     |      2413      |    2413        |        2413      |     2413        |
| mean  |      2352.53  |        73.6954 |       1.88023  |          39.8351 |        0.802735 |
| std   |       685.812 |        48.9884 |       0.324757 |         138.958  |        6.896    |
| min   |        38     |        10      |       1        |           1      |        0        |
| 25%   |      1950     |        24      |       2        |           4      |        0        |
| 50%   |      2186     |        69      |       2        |          10      |        0        |
| 75%   |      2943     |       121      |       2        |          28      |        0        |
| max   |      3425     |       155      |       2        |        3052      |      200        |