# app_saas_product_sales_overview_day_di
* comment: saas商品销售概况表(日维度)
* last_data_modified_time: 2025-09-18 02:35:42

# schema:
CREATE TABLE summerfarm_tech.`app_saas_product_sales_overview_day_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `category_id` BIGINT COMMENT '三级类目id',
  `store_type` BIGINT COMMENT '门店类型：0、直营店 1、加盟店 2、托管店',
  `store_id` BIGINT COMMENT '门店id',
  `store_name` STRING COMMENT '门店名',
  `province` STRING COMMENT '省',
  `city` STRING COMMENT '市',
  `pay_success_num` BIGINT COMMENT '支付成功商品件数',
  `pay_success_price` DECIMAL(38,18) COMMENT '支付成功金额',
  `refund_num` BIGINT COMMENT '退款件数',
  `refund_price` DECIMAL(38,18) COMMENT '退款金额',
  `warehouse_type` BIGINT COMMENT '归属类型 0自营品 1三方品',
  `delivery_type` BIGINT COMMENT '配送方式 0品牌方配送 1三方配送',
  `item_id` BIGINT COMMENT '商品编码',
  `title` STRING COMMENT '商品名称',
  `goods_type` BIGINT COMMENT '商品类型 0无货商品 1报价货品 2自营货品'
)
COMMENT 'saas商品销售概况表(日维度)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"7","1":"7","2":"7","3":"7","4":"7"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"category_id":{"0":"607.0","1":"607.0","2":"607.0","3":"607.0","4":"607.0"},"store_type":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"store_id":{"0":"390607","1":"390664","2":"402740","3":"409604","4":"421833"},"store_name":{"0":"WX浙江台州椒江宝龙店","1":"WX浙江嘉兴桐乡洲泉湘溪大道店","2":"WX温州平阳腾蛟店","3":"WX嘉兴秀洲马厍汇历史街区店","4":"WX浙江台州黄岩新前街店"},"province":{"0":"浙江","1":"浙江","2":"浙江","3":"浙江","4":"浙江"},"city":{"0":"台州市","1":"嘉兴市","2":"温州市","3":"嘉兴市","4":"台州市"},"pay_success_num":{"0":"3","1":"5","2":"2","3":"3","4":"5"},"pay_success_price":{"0":"720","1":"1200","2":"480","3":"720","4":"1200"},"refund_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"refund_price":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"warehouse_type":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"delivery_type":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"item_id":{"0":"32963","1":"32963","2":"32963","3":"32963","4":"32963"},"title":{"0":"生牛乳厚乳","1":"生牛乳厚乳","2":"生牛乳厚乳","3":"生牛乳厚乳","4":"生牛乳厚乳"},"goods_type":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   category_id |   store_type |   store_id |   pay_success_num |   refund_num |   warehouse_type |   item_id |   goods_type |
|:------|------------:|--------------:|-------------:|-----------:|------------------:|-------------:|-----------------:|----------:|-------------:|
| count |   2259      |      2010     |  2259        |       2259 |        2259       |    2259      |      2259        |    2259   |   2259       |
| mean  |     58.0646 |       660.986 |     0.689686 |     363531 |           2.37317 |      18.3267 |         1.02302  |   30917.9 |      1.30589 |
| std   |     38.3636 |       162.052 |     0.482405 |     205138 |           5.32714 |     201.189  |         0.492999 |   13955.8 |      0.658   |
| min   |      7      |       526     |     0        |        410 |           0       |       0      |         0        |     279   |      0       |
| 25%   |     14      |       536     |     0        |     359772 |           1       |       0      |         1        |   23353   |      1       |
| 50%   |     58      |       575     |     1        |     458217 |           1       |       0      |         1        |   35473   |      1       |
| 75%   |     95      |       780     |     1        |     519306 |           2       |       0      |         1        |   43100   |      2       |
| max   |    123      |      1127     |     2        |     543362 |         102       |    4368      |         2        |   44880   |      2       |