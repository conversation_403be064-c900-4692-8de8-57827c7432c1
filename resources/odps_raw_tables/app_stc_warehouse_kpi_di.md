# app_stc_warehouse_kpi_di
* comment: 仓配kpi库存数据
* last_data_modified_time: 2025-09-18 03:42:29

# schema:
CREATE TABLE summerfarm_tech.`app_stc_warehouse_kpi_di` (
  `date` STRING COMMENT '日期',
  `warehouse_no` BIGINT COMMENT '库存仓号',
  `warehouse_name` STRING COMMENT '库存仓',
  `check_sku_cnt` BIGINT COMMENT '抽检数量',
  `in_bound_sku_cnt` BIGINT COMMENT '入库数量',
  `back_order_cnt` BIGINT COMMENT '退货总单数',
  `finish_order_cnt` BIGINT COMMENT '已完成单数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责',
  `sale_amt` DECIMAL(38,18) COMMENT '销售金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额',
  `after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责',
  `after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责',
  `after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责',
  `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责',
  `after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付GMV',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
)
COMMENT '仓配kpi库存数据'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"warehouse_no":{"0":"24","1":"48","2":"155","3":"62","4":"64","5":"29","6":"59","7":"69","8":"125","9":"38"},"warehouse_name":{"0":"华西总仓","1":"长沙总仓","2":"武汉总仓","3":"苏州总仓","4":"青岛总仓","5":"重庆总仓","6":"南宁总仓","7":"东莞总仓","8":"南京总仓","9":"福州总仓"},"check_sku_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_bound_sku_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"back_order_cnt":{"0":"0","1":"2","2":"1","3":"0","4":"1","5":"1","6":"0","7":"9","8":"6","9":"5"},"finish_order_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"1","9":"1"},"damage_amt":{"0":"851.97","1":"86.7","2":"1140.07","3":"0","4":"0","5":"45.34","6":"0","7":"401.24","8":"239","9":"97"},"damage_amt_wah":{"0":"0","1":"11.7","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"sale_amt":{"0":"146461.75","1":"550005.74","2":"334643.98","3":"54088.45","4":"69552.95","5":"61735.58","6":"92909.96","7":"636221.86","8":"215496.65","9":"175488.05"},"after_sale_amt":{"0":"1323","1":"2291.5","2":"763.97","3":"0","4":"583.51","5":"33.74","6":"320","7":"2750.28","8":"591.51","9":"851.01"},"after_sale_amt_wah":{"0":"22.93","1":"17.92","2":"0","3":"0","4":"7.5","5":"0","6":"0","7":"888.29","8":"107.76","9":"0"},"after_sale_amt_pur":{"0":"75.6","1":"178.66","2":"35.57","3":"0","4":"154.99","5":"0","6":"0","7":"119.48","8":"102.91","9":"94.51"},"after_sale_amt_che":{"0":"1018.2","1":"2012.63","2":"624.47","3":"0","4":"215.47","5":"33.74","6":"43.79","7":"1543.9","8":"210.07","9":"743.46"},"after_sale_amt_pur_che":{"0":"1093.8","1":"2191.29","2":"660.04","3":"0","4":"370.46","5":"33.74","6":"43.79","7":"1663.38","8":"312.98","9":"837.97"},"after_sale_amt_oth":{"0":"206.27","1":"82.29","2":"103.93","3":"0","4":"205.55","5":"0","6":"276.21","7":"198.61","8":"170.77","9":"13.04"},"delivery_total_amt":{"0":"161635.259999999999997","1":"517984.949999999999996672","2":"375797.349999999999979998","3":"55676.5899999999999936","4":"77062.790000000000001012","5":"68146.319999999999999999","6":"27143.38","7":"746773.689999999999990398","8":"289659.010000000000000786","9":"92331.269999999999998999"},"coupon_amt":{"0":"1952.319999999999997","1":"5649.91333333333333","2":"4092.41999999999998","3":"502.1399999999999936","4":"857.590000000000001","5":"742.81","6":"246.81","7":"8265.2111111111111015","8":"2870.0914285714285722","9":"1352.819999999999999"},"origin_total_amt":{"0":"190792.79","1":"568005.79","2":"386237.82","3":"56309","4":"79250.05","5":"73310.52","6":"28968.58","7":"800415.97","8":"263116.93","9":"95599.43"},"real_total_amt":{"0":"186156.23","1":"547673.986666666666666672","2":"372156.149999999999999998","3":"55174.45","4":"76205.200000000000000012","5":"70768.509999999999999999","6":"28148.78","7":"769956.518888888888888898","8":"253509.238571428571428586","9":"90978.449999999999999999"},"storage_amt":{"0":"3039.981112799973278707","1":"12749.081781809634350452","2":"9456.617546374477532922","3":"262.227633142147473052","4":"1366.865711031777224405","5":"1606.718138922529962368","6":"639.843902161874746227","7":"20157.453331143193849002","8":"5799.809806289605679804","9":"2101.24456983559186296"},"arterial_roads_amt":{"0":"0","1":"16883.749605200353823518","2":"15534.705010616315276003","3":"154.201314057955615489","4":"804.954643135063892307","5":"0","6":"376.323280545293421053","7":"15656.291023962635519634","8":"7760.740510758539573953","9":"2249.119992611929300862"},"deliver_amt":{"0":"10682.028690704946723544","1":"62244.256636400608718628","2":"35328.160731738184200895","3":"1149.807088765442828043","4":"3097.05332381585070301","5":"3980.283668901367300012","6":"1102.37950886254306245","7":"42749.456560337740363835","8":"22445.844327537704395384","9":"5965.314690515559111423"},"self_picked_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"other_amt":{"0":"0","1":"2395.498047089663087793","2":"1198.393841066858158148","3":"68.039519951608999004","4":"151.080044940815608803","5":"0","6":"69.548637909335237868","7":"0","8":"877.043675180302980325","9":"377.495356476802790363"},"allocation_amt":{"0":"2603.550042678492547323","1":"1833.154178788868611906","2":"1261.516427216530062125","3":"9.145096767150880412","4":"750.789013440663434371","5":"1013.584983087495861399","6":"368.928774684706836867","7":"3523.568639385362892395","8":"144.799486596976111914","9":"1239.128765255896807056"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   check_sku_cnt |   in_bound_sku_cnt |   back_order_cnt |   finish_order_cnt |
|:------|---------------:|----------------:|-------------------:|-----------------:|-------------------:|
| count |        17      |              17 |                 17 |         17       |          17        |
| mean  |        70.0588 |               0 |                  0 |          2.64706 |           0.176471 |
| std   |        46.4926 |               0 |                  0 |          4.06111 |           0.392953 |
| min   |         2      |               0 |                  0 |          0       |           0        |
| 25%   |        38      |               0 |                  0 |          0       |           0        |
| 50%   |        62      |               0 |                  0 |          1       |           0        |
| 75%   |       117      |               0 |                  0 |          2       |           0        |
| max   |       155      |               0 |                  0 |         15       |           1        |