# app_kpi_operate_large_area_delivery_mi
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:42:26

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_large_area_delivery_mi` (
  `month` STRING COMMENT '月份',
  `large_area_name` STRING COMMENT '运营服务大区',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"large_area_name":{"0":"上海大区","1":"南宁大区","2":"广东一点点快递区域","3":"成都大区","4":"昆明大区"},"origin_total_amt":{"0":"5396817.66","1":"1291275.8","2":"1884","3":"2471383.45","4":"814068.31"},"real_total_amt":{"0":"5252554.912857142857142879","1":"1245385.20999999999999999","2":"1884","3":"2406826.089999999999999987","4":"783470.450000000000000009"},"marketing_amt":{"0":"144262.747142857142857121","1":"45890.59000000000000001","2":"0","3":"64557.360000000000000013","4":"30597.859999999999999991"},"cost_amt":{"0":"4549815.55","1":"1182990.87","2":"1858.68","3":"2131741.99","4":"749001.47"},"origin_gross":{"0":"847002.11","1":"108284.93","2":"25.32","3":"339641.46","4":"65066.84"},"real_gross":{"0":"702739.362857142857142879","1":"62394.33999999999999999","2":"25.32","3":"275084.099999999999999987","4":"34468.980000000000000009"},"origin_gross_margin":{"0":"0.156944733611770756","1":"0.083858870428765102","2":"0.013439490445859873","3":"0.137429689431642022","4":"0.079927985404566356"},"real_gross_margin":{"0":"0.133790007818287747","1":"0.050100434386883397","2":"0.013439490445859873","3":"0.11429330151560722","4":"0.043995252150224683"},"cust_cnt":{"0":"2697","1":"807","2":"2","3":"2168","4":"313"},"point_cnt":{"0":"10949","1":"1784","2":"2","3":"6019","4":"635"},"origin_pre_cust_price":{"0":"2001.044738598442714127","1":"1600.093928128872366791","2":"942","3":"1139.937015682656826568","4":"2600.857220447284345048"},"real_pre_cust_price":{"0":"1947.554658085703691933","1":"1543.228265179677819083","2":"942","3":"1110.159635608856088561","4":"2503.100479233226837061"},"timing_origin_amt":{"0":"368987","1":"65306","2":"0","3":"112788","4":"32873"},"timing_real_amt":{"0":"344184.902857142857142851","1":"61242","2":"0","3":"104672.26","4":"28871.7"},"consign_origin_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_marketing_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_origin_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_cust_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"storage_amt":{"0":"152559.23670647472552997","1":"27141.301249167331771304","2":"0","3":"33739.52943530600208304","4":"12686.110005600433562113"},"arterial_roads_amt":{"0":"19447.962888503908228787","1":"15679.355951014190064919","2":"0","3":"0","4":"0"},"self_picked_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_amt":{"0":"2608.912995418023551712","1":"15649.452904624695461033","2":"0","3":"32847.735361364883000606","4":"30998.393303213373855556"},"other_amt":{"0":"818.412174970573721052","1":"2950.157884740596204033","2":"0","3":"0","4":"3289.923817558023175812"},"deliver_amt":{"0":"317645.270293362046423914","1":"46067.339464874057416484","2":"0","3":"136947.393175055051065153","4":"21582.37240092493112535"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |      17    |       17    |                 17 |
| mean  |    2631.71 |     7803.12 |                  0 |
| std   |    2840.28 |     8599.19 |                  0 |
| min   |       2    |        2    |                  0 |
| 25%   |     326    |      698    |                  0 |
| 50%   |    2168    |     5716    |                  0 |
| 75%   |    3677    |    10949    |                  0 |
| max   |    9598    |    28131    |                  0 |