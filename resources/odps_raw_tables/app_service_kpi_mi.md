# app_service_kpi_mi
* comment: 客服KPI
* last_data_modified_time: 2025-09-18 03:17:07

# schema:
CREATE TABLE summerfarm_tech.`app_service_kpi_mi` (
  `month` STRING COMMENT '月份',
  `cust_team` STRING COMMENT '客户类型：全量客户，平台客户',
  `channel_type` STRING COMMENT '渠道类型: 鲜沐，SAAS',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `after_sale_received_quality_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（质量问题）',
  `after_sale_received_warehouse_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（仓配问题）',
  `after_sale_received_other_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（其他问题）',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `after_sale_received_ratio` DECIMAL(38,18) COMMENT '已到货售后率',
  `delivery_evaluation_low_cnt` BIGINT COMMENT '司机评价差评数(3星以下)',
  `delivery_evaluation_cnt` BIGINT COMMENT '司机评价总数'
)
COMMENT '客服KPI'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509"},"cust_team":{"0":"平台客户","1":"平台客户","2":"全量客户","3":"全量客户"},"channel_type":{"0":"鲜沐","1":"SAAS","2":"鲜沐","3":"SAAS"},"after_sale_received_amt":{"0":"313252.34","1":"9130.28","2":"319166.57","3":"28726.53"},"after_sale_received_quality_amt":{"0":"231463.86","1":"8471.74","2":"235807.35","3":"25146.43"},"after_sale_received_warehouse_amt":{"0":"27688.39","1":"0","2":"28200.13","3":"0"},"after_sale_received_other_amt":{"0":"39673.98","1":"658.54","2":"40732.98","3":"3580.1"},"delivery_origin_total_amt":{"0":"62599173.09","1":"1336627.15","2":"63228287.89","3":"1828632.14"},"after_sale_received_ratio":{"0":"0.005004097091660161","1":"0.006830835360481792","2":"0.00504784457480903","3":"0.01570929952046014"},"delivery_evaluation_low_cnt":{"0":"112","1":"0","2":"113","3":"0"},"delivery_evaluation_cnt":{"0":"4610","1":"0","2":"4612","3":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   delivery_evaluation_low_cnt |   delivery_evaluation_cnt |
|:------|------------------------------:|--------------------------:|
| count |                        4      |                      4    |
| mean  |                       56.25   |                   2305.5  |
| std   |                       64.9532 |                   2662.16 |
| min   |                        0      |                      0    |
| 25%   |                        0      |                      0    |
| 50%   |                       56      |                   2305    |
| 75%   |                      112.25   |                   4610.5  |
| max   |                      113      |                   4612    |