# app_purchase_category_kpi_wi
* comment: 采购kpi
* last_data_modified_time: 2025-09-18 03:12:33

# schema:
CREATE TABLE summerfarm_tech.`app_purchase_category_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `category` STRING COMMENT '鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本',
  `store_cost_amt` DECIMAL(38,18) COMMENT '期末库存成本',
  `sale_amt` DECIMAL(38,18) COMMENT '销售出库成本',
  `on_sale_sku_cnt` BIGINT COMMENT '有销售及自提出库的sku数',
  `init_sku_cnt` BIGINT COMMENT '有期初库存的sku数',
  `sale_out_time` DECIMAL(38,18) COMMENT '售罄时长',
  `on_sale_time` DECIMAL(38,18) COMMENT '上架时长',
  `check_sku_cnt` BIGINT COMMENT '抽检数量',
  `qualified_cnt` BIGINT COMMENT '合格数量',
  `after_sale_amt` DECIMAL(38,18) COMMENT '采购责售后金额'
)
COMMENT '采购kpi'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025"},"week_of_year":{"0":"38","1":"38","2":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921"},"category":{"0":"乳制品","1":"其他","2":"鲜果"},"origin_total_amt":{"0":"6540625.08","1":"2518842.24","2":"3132309.45"},"real_total_amt":{"0":"6354161.691459096459096516","1":"2429229.066349206349206333","2":"3032501.540000000000000067"},"cost_amt":{"0":"5949234.53","1":"1879815.25","2":"2344764.94"},"store_cost_amt":{"0":"118621664.75","1":"49635611.67","2":"5556531.06"},"sale_amt":{"0":"6367886.91","1":"1894054.42","2":"2266631.61"},"on_sale_sku_cnt":{"0":"312","1":"1825","2":"668"},"init_sku_cnt":{"0":"232","1":"1071","2":"617"},"sale_out_time":{"0":"40545.911944444444418913","1":"211990.222222222222083057","2":"45014.382222222222189422"},"on_sale_time":{"0":"158028.448333333333306954","1":"802639.113333333333325052","2":"151248.485833333333325456"},"check_sku_cnt":{"0":"0","1":"0","2":"0"},"qualified_cnt":{"0":"0","1":"0","2":"0"},"after_sale_amt":{"0":"0","1":"0","2":"2557.59"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   on_sale_sku_cnt |   init_sku_cnt |   check_sku_cnt |   qualified_cnt |
|:------|---------------:|------------------:|---------------:|----------------:|----------------:|
| count |              3 |             3     |          3     |               3 |               3 |
| mean  |             38 |           935     |        640     |               0 |               0 |
| std   |              0 |           791.049 |        419.973 |               0 |               0 |
| min   |             38 |           312     |        232     |               0 |               0 |
| 25%   |             38 |           490     |        424.5   |               0 |               0 |
| 50%   |             38 |           668     |        617     |               0 |               0 |
| 75%   |             38 |          1246.5   |        844     |               0 |               0 |
| max   |             38 |          1825     |       1071     |               0 |               0 |