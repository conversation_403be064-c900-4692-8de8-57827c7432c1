# app_pcs_batch_sku_in_bound_di
* comment: 采购数据汇总
* last_data_modified_time: 2025-09-18 02:07:50

# schema:
CREATE TABLE summerfarm_tech.`app_pcs_batch_sku_in_bound_di` (
  `purchase_date` DATETIME COMMENT '采购时间',
  `batch_no` STRING COMMENT '采购批次',
  `sku_id` STRING COMMENT '采购sku编号',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT 'sku规格',
  `sku_type` STRING COMMENT 'sku类型，自营，代仓，代售',
  `category1` STRING COMMENT '一级类目',
  `category2` STRING COMMENT '二级类目',
  `category3` STRING COMMENT '三级类目',
  `category4` STRING COMMENT '四级类目',
  `purchaser` STRING COMMENT '采购人',
  `in_store_warehouse_no` BIGINT COMMENT '入库仓号',
  `in_store_warehouse_name` STRING COMMENT '入库仓',
  `in_store_state` STRING COMMENT '待入库，已入库，部分入库',
  `supplier` STRING COMMENT '供应商',
  `purchase_sku_cnt` BIGINT COMMENT '采购数量',
  `purchase_total_amt` DECIMAL(38,18) COMMENT '采购总价',
  `purchase_unit_cost` DECIMAL(38,18) COMMENT '采购单价',
  `in_store_sku_cnt` BIGINT COMMENT '入库数量',
  `no_store_sku_cnt` BIGINT COMMENT '未入库数量',
  `back_sku_cnt` BIGINT COMMENT '退货数量',
  `back_order_sku_cnt` BIGINT COMMENT '退订数量',
  `in_store_sku_cnt_t_1` DECIMAL(38,18) COMMENT 't-1入库成本',
  `in_store_sku_cnt_t_2` DECIMAL(38,18) COMMENT 't-2入库成本',
  `in_store_sku_cnt_t_3` DECIMAL(38,18) COMMENT 't-3入库成本',
  `in_store_sku_cnt_t_4` DECIMAL(38,18) COMMENT 't-4入库成本',
  `in_store_sku_cnt_t_5` DECIMAL(38,18) COMMENT 't-5入库成本',
  `in_store_sku_cnt_t_6` DECIMAL(38,18) COMMENT 't-6入库成本',
  `in_store_sku_cnt_t_7` DECIMAL(38,18) COMMENT 't-7入库成本'
)
COMMENT '采购数据汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"purchase_date":{"0":"2025-09-17 11:25:16","1":"2025-09-17 16:02:05","2":"2025-09-17 16:02:05","3":"2025-09-17 16:02:05","4":"2025-09-17 09:53:57"},"batch_no":{"0":"202508080601904069","1":"20250904112217141","2":"20250904112217141","3":"20250904112217141","4":"202509100485317144"},"sku_id":{"0":"464633265","1":"17474853250","2":"17447307136","3":"17464663857","4":"654407504358"},"spu_name":{"0":"艾恩摩尔35%淡奶油","1":"雀巢香草味冰淇淋","2":"雀巢绿茶味冰淇淋","3":"雀巢草莓味冰淇淋","4":"沐清友茉莉雪芽（绿茶）"},"sku_disc":{"0":"1L*12瓶","1":"3.5KG*1桶\/含奶量92%(蓝色盖子)","2":"3.5KG*1桶\/含奶量92%","3":"3.5KG*1桶\/含奶量92%","4":"300g*1袋"},"sku_type":{"0":"自营","1":"自营","2":"自营","3":"自营","4":"自营"},"category1":{"0":"乳制品","1":"其他","2":"其他","3":"其他","4":"其他"},"category2":{"0":"乳制品","1":"饮品原料","2":"饮品原料","3":"饮品原料","4":"茶制品"},"category3":{"0":"稀奶油","1":"冷冻饮品","2":"冷冻饮品","3":"冷冻饮品","4":"茶叶"},"category4":{"0":"搅打型稀奶油","1":"冰淇淋","2":"冰淇淋","3":"冰淇淋","4":"绿茶"},"purchaser":{"0":"万彩云","1":"吴雅雯","2":"吴雅雯","3":"吴雅雯","4":"冯世杰"},"in_store_warehouse_no":{"0":"24","1":"117","2":"117","3":"117","4":"69"},"in_store_warehouse_name":{"0":"华西总仓","1":"东莞冷冻总仓","2":"东莞冷冻总仓","3":"东莞冷冻总仓","4":"东莞总仓"},"in_store_state":{"0":"已入库","1":"待入库","2":"待入库","3":"待入库","4":"待入库"},"supplier":{"0":"成都鑫昶实贸易有限公司","1":"天津雀巢有限公司","2":"天津雀巢有限公司","3":"天津雀巢有限公司","4":"福建省盛世大翔茶业有限公司"},"purchase_sku_cnt":{"0":"50","1":"300","2":"40","3":"24","4":"60"},"purchase_total_amt":{"0":"21000","1":"37290","2":"4972","3":"2983.2","4":"2400"},"purchase_unit_cost":{"0":"420","1":"124.3","2":"124.3","3":"124.3","4":"40"},"in_store_sku_cnt":{"0":"50","1":"0","2":"0","3":"0","4":"0"},"no_store_sku_cnt":{"0":"0","1":"300","2":"40","3":"24","4":"60"},"back_sku_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"back_order_sku_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_store_sku_cnt_t_1":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_store_sku_cnt_t_2":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_store_sku_cnt_t_3":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_store_sku_cnt_t_4":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_store_sku_cnt_t_5":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_store_sku_cnt_t_6":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_store_sku_cnt_t_7":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | purchase_date                 |   in_store_warehouse_no |   purchase_sku_cnt |   in_store_sku_cnt |   no_store_sku_cnt |   back_sku_cnt |   back_order_sku_cnt |
|:------|:------------------------------|------------------------:|-------------------:|-------------------:|-------------------:|---------------:|---------------------:|
| count | 683                           |                683      |           683      |           683      |           683      |    683         |          683         |
| mean  | 2025-09-17 12:54:25.237189120 |                 56.4348 |            63.7277 |            25.2504 |            38.4773 |      0.0614934 |            0.0292826 |
| min   | 2025-09-17 00:07:47           |                  2      |             1      |             0      |             0      |      0         |            0         |
| 25%   | 2025-09-17 09:29:34           |                 10      |            10      |             0      |             0      |      0         |            0         |
| 50%   | 2025-09-17 12:16:01           |                 48      |            21      |            10      |             0      |      0         |            0         |
| 75%   | 2025-09-17 15:47:33.500000    |                 69      |            50      |            30      |            10      |      0         |            0         |
| max   | 2025-09-17 21:53:01           |                155      |          2500      |           450      |          2500      |     25         |           15         |
| std   | nan                           |                 43.1872 |           178.801  |            44.9842 |           176.25   |      1.0199    |            0.60474   |