# app_sku_area_cust_category_gross_margin_di
* comment: 毛利数据sku维日表
* last_data_modified_time: 2025-09-18 03:35:37

# schema:
CREATE TABLE summerfarm_tech.`app_sku_area_cust_category_gross_margin_di` (
  `date` STRING COMMENT '日期',
  `large_area_name` STRING COMMENT '服务大区',
  `warehouse_name` STRING COMMENT '库存仓名',
  `cust_class` STRING COMMENT '客户大类:大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
  `sku_id` STRING COMMENT 'sku编号',
  `sku_type` STRING COMMENT '自营 ， 代仓',
  `spu_name` STRING COMMENT '商品名称',
  `category_1` STRING COMMENT '一级类目',
  `category_2` STRING COMMENT '二级类目',
  `category_3` STRING COMMENT '三级类目',
  `category_4` STRING COMMENT '四级类目',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '总成本',
  `gross_margin` DECIMAL(38,18) COMMENT '毛利润',
  `sku_cnt` BIGINT COMMENT '配送数量',
  `origin_amt_befored` DECIMAL(38,18) COMMENT '昨日应付',
  `real_amt_befored` DECIMAL(38,18) COMMENT '昨日实付',
  `cost_amt_befored` DECIMAL(38,18) COMMENT '昨日总成本',
  `gross_margin_befored` DECIMAL(38,18) COMMENT '昨日毛利润',
  `sku_cnt_befored` DECIMAL(38,18) COMMENT '昨日配送数量',
  `origin_amt_beforew` DECIMAL(38,18) COMMENT '上周应付',
  `real_amt_beforew` DECIMAL(38,18) COMMENT '上周实付',
  `cost_amt_beforew` DECIMAL(38,18) COMMENT '上周总成本',
  `gross_margin_beforew` DECIMAL(38,18) COMMENT '上周毛利润',
  `sku_cnt_beforew` DECIMAL(38,18) COMMENT '上周配送数量',
  `sku_disc` STRING COMMENT '描述'
)
COMMENT '毛利数据sku维日表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"large_area_name":{"0":"广州大区","1":"重庆大区","2":"广州大区","3":"成都大区","4":"杭州大区","5":"广州大区","6":"上海大区","7":"上海大区","8":"广州大区","9":"成都大区"},"warehouse_name":{"0":"东莞总仓","1":"重庆总仓","2":"东莞总仓","3":"华西总仓","4":"嘉兴总仓","5":"东莞总仓","6":"上海总仓","7":"上海总仓","8":"东莞总仓","9":"华西总仓"},"cust_class":{"0":"大客户","1":"大客户","2":"大客户","3":"大客户","4":"大客户","5":"大客户","6":"大客户","7":"大客户","8":"大客户","9":"大客户"},"sku_id":{"0":"N001G01R002","1":"Q001L01S001","2":"607164503701","3":"814153876","4":"608083408143","5":"H001Y01M01","6":"N001S01R005","7":"N001S01R002","8":"N001S01R005","9":"N001S01R005"},"sku_type":{"0":"自营","1":"自营","2":"自营","3":"自营","4":"自营","5":"自营","6":"自营","7":"自营","8":"自营","9":"自营"},"spu_name":{"0":"安佳奶油奶酪1KG","1":"琪雷萨马斯卡彭","2":"Protag纯牛奶","3":"雀巢纯牛奶","4":"Protag冷藏鲜牛奶","5":"鹰唛炼乳","6":"安佳淡奶油","7":"爱乐薇(铁塔)淡奶油","8":"安佳淡奶油","9":"安佳淡奶油"},"category_1":{"0":"乳制品","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品","5":"乳制品","6":"乳制品","7":"乳制品","8":"乳制品","9":"乳制品"},"category_2":{"0":"乳制品","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品","5":"乳制品","6":"乳制品","7":"乳制品","8":"乳制品","9":"乳制品"},"category_3":{"0":"奶酪丨芝士","1":"奶酪丨芝士","2":"液体乳","3":"液体乳","4":"液体乳","5":"炼乳","6":"稀奶油","7":"稀奶油","8":"稀奶油","9":"稀奶油"},"category_4":{"0":"奶油奶酪","1":"马斯卡彭","2":"常温牛奶","3":"常温牛奶","4":"鲜牛奶","5":"罐装炼乳","6":"搅打型稀奶油","7":"搅打型稀奶油","8":"搅打型稀奶油","9":"搅打型稀奶油"},"origin_total_amt":{"0":"56","1":"500","2":"75","3":"129","4":"200","5":"540","6":"14130","7":"535","8":"21275","9":"471"},"real_total_amt":{"0":"56","1":"495.77","2":"75","3":"129","4":"200","5":"540","6":"14130","7":"535","8":"21275","9":"471"},"cost_amt":{"0":"49.17","1":"432","2":"59","3":"97","4":"172.36","5":"495","6":"13980","7":"508","8":"20970","9":"466"},"gross_margin":{"0":"6.83","1":"68","2":"16","3":"32","4":"27.64","5":"45","6":"150","7":"27","8":"305","9":"5"},"sku_cnt":{"0":"1","1":"2","2":"1","3":"1","4":"2","5":"1","6":"30","7":"1","8":"45","9":"1"},"origin_amt_befored":{"0":"168","1":"0","2":"150","3":"0","4":"0","5":"0","6":"1413","7":"0","8":"21666","9":"0"},"real_amt_befored":{"0":"168","1":"0","2":"150","3":"0","4":"0","5":"0","6":"1413","7":"0","8":"21666","9":"0"},"cost_amt_befored":{"0":"147.51","1":"0","2":"118","3":"0","4":"0","5":"0","6":"1398","7":"0","8":"21436","9":"0"},"gross_margin_befored":{"0":"20.49","1":"0","2":"32","3":"0","4":"0","5":"0","6":"15","7":"0","8":"230","9":"0"},"sku_cnt_befored":{"0":"3","1":"0","2":"2","3":"0","4":"0","5":"0","6":"3","7":"0","8":"46","9":"0"},"origin_amt_beforew":{"0":"114","1":"0","2":"75","3":"0","4":"0","5":"0","6":"2355","7":"0","8":"18409","9":"471"},"real_amt_beforew":{"0":"114","1":"0","2":"75","3":"0","4":"0","5":"0","6":"2355","7":"0","8":"18409","9":"471"},"cost_amt_beforew":{"0":"98.34","1":"0","2":"59","3":"0","4":"0","5":"0","6":"2330","7":"0","8":"18171.62","9":"466"},"gross_margin_beforew":{"0":"15.66","1":"0","2":"16","3":"0","4":"0","5":"0","6":"25","7":"0","8":"237.38","9":"5"},"sku_cnt_beforew":{"0":"2","1":"0","2":"1","3":"0","4":"0","5":"0","6":"5","7":"0","8":"39","9":"1"},"sku_disc":{"0":"1KG*1块","1":"500g*6盒","2":"1L*12盒\/纯牛奶","3":"1L*12盒","4":"1KG*12瓶","5":"350g*48罐","6":"1L*12瓶","7":"1L*12盒","8":"1L*12瓶","9":"1L*12瓶"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |    sku_cnt |
|:------|-----------:|
| count | 3910       |
| mean  |    9.41969 |
| std   |   30.7658  |
| min   |    0       |
| 25%   |    1       |
| 50%   |    3       |
| 75%   |    8       |
| max   | 1303       |