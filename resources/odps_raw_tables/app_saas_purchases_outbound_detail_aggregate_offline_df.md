# app_saas_purchases_outbound_detail_aggregate_offline_df
* comment: 采购退货出库单 离线表
* last_data_modified_time: 2025-09-18 02:01:46

# schema:
CREATE TABLE summerfarm_tech.`app_saas_purchases_outbound_detail_aggregate_offline_df` (
  `outbound_time` DATETIME COMMENT '出库时间',
  `batch_no` STRING COMMENT '采购批次',
  `refund_batch_no` STRING COMMENT '退款单编号',
  `outbound_stock` BIGINT COMMENT '出库数量',
  `outbound_price` DECIMAL(38,18) COMMENT '出库金额',
  `purchases_stock` BIGINT COMMENT '采购数量',
  `purchases_price` DECIMAL(38,18) COMMENT '采购金额',
  `sku_no` STRING COMMENT 'sku编号',
  `sku_name` STRING COMMENT 'sku名称',
  `specification` STRING COMMENT '规格',
  `packaging` STRING COMMENT '规格单位',
  `saas_sku_no` STRING COMMENT 'saas-sku编号',
  `saas_sku_name` STRING COMMENT 'saas-sku名称',
  `saas_specification` STRING COMMENT 'saas-规格',
  `saas_packaging` STRING COMMENT 'saas-规格单位',
  `outbound_create_user_id` BIGINT COMMENT '出库发起人',
  `outbound_create_user_name` STRING COMMENT '出库发起人名称',
  `outbound_create_user_phone` STRING COMMENT '出库发起人电话',
  `inbound_create_user_id` BIGINT COMMENT '采购人',
  `inbound_create_user_name` STRING COMMENT '采购人名称',
  `inbound_create_user_phone` STRING COMMENT '采购人电话',
  `warehouse_id` BIGINT COMMENT '库存仓ID',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `tenant_id` BIGINT COMMENT '租户ID',
  `tenant_name` STRING COMMENT '租户名称',
  `supplier_id` BIGINT COMMENT '供应商ID',
  `supplier_name` STRING COMMENT '供应商名称',
  `supplier_type` BIGINT COMMENT '供应商类型'
)
COMMENT '采购退货出库单 离线表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"outbound_time":{"0":"2023-07-02 09:47:38","1":"2024-09-25 06:58:55","2":"2023-06-24 12:43:51","3":"2025-09-05 13:33:32","4":"2024-08-08 11:41:42"},"batch_no":{"0":"20230112178790149","1":"20240923115884248","2":"20230624218935063","3":"20250827115803158","4":"2024010100058072"},"refund_batch_no":{"0":"0202301124373413","1":"0202409242183421","2":"0202306244607561","3":"0202509058080564","4":"0202408075336078"},"outbound_stock":{"0":"1","1":"10","2":"50","3":"411","4":"7"},"outbound_price":{"0":"5.45","1":"4200","2":"21550","3":"177141","4":"2317"},"purchases_stock":{"0":"15","1":"300","2":"50","3":"500","4":"100"},"purchases_price":{"0":"81.75","1":"126000","2":"21550","3":"215500","4":"33100"},"sku_no":{"0":"1038882864152","1":"666137337665","2":"586763224618","3":"586763224618","4":"801136364764"},"sku_name":{"0":"测试商品","1":"果然爆西米龙珠-斯味洛","2":"斯味洛专用乳粉-斯味洛","3":"斯味洛专用乳粉-斯味洛","4":"品青黑糖浆-斯味洛"},"specification":{"0":"0_1箱*2斤包","1":"1KG*20包","2":"1KG*20包","3":"1KG*20包","4":"1.2KG*15瓶"},"packaging":{"0":"包","1":"箱","2":"箱","3":"箱","4":"箱"},"saas_sku_no":{"0":"100922","1":"105501","2":"105503","3":"105503","4":"105504"},"saas_sku_name":{"0":"测试商品","1":"果然爆西米龙珠-斯味洛","2":"斯味洛专用乳粉-斯味洛","3":"斯味洛专用乳粉-斯味洛","4":"品青黑糖浆-斯味洛"},"saas_specification":{"0":"1箱*2斤","1":"1KG*20包","2":"1KG*20包","3":"1KG*20包","4":"1.2KG*15瓶"},"saas_packaging":{"0":"包","1":"箱","2":"箱","3":"箱","4":"箱"},"outbound_create_user_id":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"outbound_create_user_name":{"0":"张小虎","1":"唐荣兵","2":"斯味洛订货商城管理员","3":"新哥","4":"曾丽芳"},"outbound_create_user_phone":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"inbound_create_user_id":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"inbound_create_user_name":{"0":"1","1":"新哥","2":"斯味洛订货商城管理员","3":"新哥","4":"斯味洛订货商城管理员"},"inbound_create_user_phone":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"warehouse_id":{"0":"46","1":"69","2":"95","3":"96","4":"69"},"warehouse_name":{"0":"重构测试仓","1":"东莞总仓","2":"广州总部仓","3":"虚拟仓库","4":"东莞总仓"},"tenant_id":{"0":"2","1":"32","2":"32","3":"32","4":"32"},"tenant_name":{"0":"鲜沐商城","1":"斯味洛订货商城","2":"斯味洛订货商城","3":"斯味洛订货商城","4":"斯味洛订货商城"},"supplier_id":{"0":"1824","1":"1994","2":"1994","3":"1994","4":"1994"},"supplier_name":{"0":"大鹏测试个人","1":"总仓供应商","2":"总仓供应商","3":"总仓供应商","4":"总仓供应商"},"supplier_type":{"0":"1","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | outbound_time                 |   outbound_stock |   purchases_stock |   warehouse_id |   tenant_id |   supplier_id |   supplier_type |
|:------|:------------------------------|-----------------:|------------------:|---------------:|------------:|--------------:|----------------:|
| count | 293                           |         293      |           293     |       293      |    293      |       293     |      293        |
| mean  | 2024-09-28 03:20:19.126279936 |          35.8737 |           101.648 |        61.3276 |     46.2765 |      2336.59  |        0.573379 |
| min   | 2023-01-12 20:39:09           |           1      |             1     |         1      |      2      |      1823     |        0        |
| 25%   | 2024-04-21 09:43:29           |           2      |            15     |        10      |     32      |      1994     |        0        |
| 50%   | 2024-11-13 09:38:28           |          10      |            50     |        69      |     32      |      2049     |        0        |
| 75%   | 2025-05-27 05:32:44           |          30      |           100     |        96      |     75      |      2734     |        1        |
| max   | 2025-09-16 11:45:11           |         500      |          1300     |       175      |    116      |      3342     |        2        |
| std   | nan                           |          84.4559 |           163.729 |        44.4091 |     30.3932 |       458.322 |        0.826928 |