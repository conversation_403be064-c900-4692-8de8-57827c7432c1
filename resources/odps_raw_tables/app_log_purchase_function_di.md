# app_log_purchase_function_di
* comment: 采购助手页面流量数据
* last_data_modified_time: 2025-09-18 02:30:54

# schema:
CREATE TABLE summerfarm_tech.`app_log_purchase_function_di` (
  `date` STRING COMMENT 'FIELD',
  `type` STRING COMMENT '实验分组：V3 、V4、对照组',
  `cust_cnt` BIGINT COMMENT '客户数',
  `view_uv` BIGINT COMMENT '榜单曝光uv',
  `click_uv` BIGINT COMMENT '榜单商品点击uv',
  `click_buy_uv` BIGINT COMMENT '榜单唤起购买uv',
  `frequently_shop_uv` BIGINT COMMENT '常购商品曝光uv',
  `click_jump_uv` BIGINT COMMENT '商品点击跳转uv',
  `buy_uv` BIGINT COMMENT '常购商品唤起购买uv',
  `add_buy_uv` BIGINT COMMENT '加购点击',
  `click_select_uv` BIGINT COMMENT '点击去选购UV',
  `remove_buy_uv` BIGINT COMMENT '移除常购商品'
)
COMMENT '采购助手页面流量数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917"},"type":{"0":"V4"},"cust_cnt":{"0":"1613"},"view_uv":{"0":"334"},"click_uv":{"0":"19"},"click_buy_uv":{"0":"7"},"frequently_shop_uv":{"0":"1168"},"click_jump_uv":{"0":"216"},"buy_uv":{"0":"411"},"add_buy_uv":{"0":"340"},"click_select_uv":{"0":"24"},"remove_buy_uv":{"0":"19"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   view_uv |   click_uv |   click_buy_uv |   frequently_shop_uv |   click_jump_uv |   buy_uv |   add_buy_uv |   click_select_uv |   remove_buy_uv |
|:------|-----------:|----------:|-----------:|---------------:|---------------------:|----------------:|---------:|-------------:|------------------:|----------------:|
| count |          1 |         1 |          1 |              1 |                    1 |               1 |        1 |            1 |                 1 |               1 |
| mean  |       1613 |       334 |         19 |              7 |                 1168 |             216 |      411 |          340 |                24 |              19 |
| std   |        nan |       nan |        nan |            nan |                  nan |             nan |      nan |          nan |               nan |             nan |
| min   |       1613 |       334 |         19 |              7 |                 1168 |             216 |      411 |          340 |                24 |              19 |
| 25%   |       1613 |       334 |         19 |              7 |                 1168 |             216 |      411 |          340 |                24 |              19 |
| 50%   |       1613 |       334 |         19 |              7 |                 1168 |             216 |      411 |          340 |                24 |              19 |
| 75%   |       1613 |       334 |         19 |              7 |                 1168 |             216 |      411 |          340 |                24 |              19 |
| max   |       1613 |       334 |         19 |              7 |                 1168 |             216 |      411 |          340 |                24 |              19 |