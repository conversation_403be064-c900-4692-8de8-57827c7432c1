# app_self_cust_delivery_kpi_mi
* comment: 履约口径kpi指标日汇总(自营)
* last_data_modified_time: 2025-09-18 03:42:46

# schema:
CREATE TABLE summerfarm_tech.`app_self_cust_delivery_kpi_mi` (
  `month` STRING COMMENT '日期',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `day_point_cnt` BIGINT COMMENT '日均点位',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `cust_runoff` DECIMAL(38,18) COMMENT '客户流失率'
)
COMMENT '履约口径kpi指标日汇总(自营)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509"},"cust_team":{"0":"Mars大客户","1":"平台客户","2":"集团大客户（茶百道）"},"origin_total_amt":{"0":"1037586.37","1":"63935800.24","2":"83533.42"},"real_total_amt":{"0":"1036880.200000000000000002","1":"61932315.833454545454546037","2":"80776.200000000000000004"},"cost_amt":{"0":"825000.17","1":"55170941.19","2":"68209.55"},"timing_origin_total_amt":{"0":"0","1":"3507383","2":"0"},"timing_real_total_amt":{"0":"0","1":"3272386.263454545454545426","2":"0"},"cust_cnt":{"0":"697","1":"44738","2":"150"},"order_cnt":{"0":"3029","1":"150992","2":"390"},"point_cnt":{"0":"709","1":"48567","2":"169"},"day_point_cnt":{"0":"2913","1":"132653","2":"326"},"sku_cnt":{"0":"13360","1":"591485","2":"1511"},"delivery_amt":{"0":"4370","1":"129151.14","2":"270"},"after_sale_received_amt":{"0":"20463.18","1":"141916.22","2":"217.46"},"storage_amt":{"0":"14255.932241478569505269","1":"1310935.925994524446427216","2":"2734.284468806892022222"},"arterial_roads_amt":{"0":"5149.617236074202950387","1":"1033932.143884318668414899","2":"3357.408115756722646477"},"deliver_amt":{"0":"45314.68982634950209634","1":"3927634.020492540908854771","2":"8890.693027454461402556"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"other_amt":{"0":"1264.414918132442033595","1":"164331.990882696605951611","2":"524.81203914201970388"},"allocation_amt":{"0":"5673.151882911984585449","1":"252743.463843885655930516","2":"251.353507312339272127"},"cust_runoff":{"0":"0.1942740286298568","1":"0.1131419616781822","2":"0.1836228287841191"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   point_cnt |   day_point_cnt |   sku_cnt |
|:------|-----------:|------------:|------------:|----------------:|----------:|
| count |        3   |         3   |         3   |             3   |       3   |
| mean  |    15195   |     51470.3 |     16481.7 |         45297.3 |  202119   |
| std   |    25586.5 |     86198.4 |     27788   |         75663.3 |  337253   |
| min   |      150   |       390   |       169   |           326   |    1511   |
| 25%   |      423.5 |      1709.5 |       439   |          1619.5 |    7435.5 |
| 50%   |      697   |      3029   |       709   |          2913   |   13360   |
| 75%   |    22717.5 |     77010.5 |     24638   |         67783   |  302422   |
| max   |    44738   |    150992   |     48567   |        132653   |  591485   |