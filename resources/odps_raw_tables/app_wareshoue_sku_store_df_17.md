# app_wareshoue_sku_store_df_17
* comment: 当日17时自营水果库存数据
* last_data_modified_time: 2025-09-17 17:06:53

# schema:
CREATE TABLE summerfarm_tech.`app_wareshoue_sku_store_df_17` (
  `warehouse_no` BIGINT COMMENT '库存仓号',
  `warehouse_name` STRING COMMENT '库存仓',
  `sku_id` STRING COMMENT 'sku编码',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品描述',
  `store_quantity` BIGINT COMMENT '仓库库存',
  `ues_quantity` BIGINT COMMENT '可用库存',
  `road_quantity` BIGINT COMMENT '在途库存',
  `sale_snt` BIGINT COMMENT '当日销量',
  `sale_amt` DECIMAL(38,18) COMMENT '当日应付GMV'
)
COMMENT '当日17时自营水果库存数据'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"warehouse_no":{"0":"2","1":"10","2":"10","3":"10","4":"10","5":"10","6":"10","7":"10","8":"10","9":"10"},"warehouse_name":{"0":"上海总仓","1":"嘉兴总仓","2":"嘉兴总仓","3":"嘉兴总仓","4":"嘉兴总仓","5":"嘉兴总仓","6":"嘉兴总仓","7":"嘉兴总仓","8":"嘉兴总仓","9":"嘉兴总仓"},"sku_id":{"0":"16788463142","1":"102530","2":"103162","3":"103764","4":"105020","5":"105771","6":"105818","7":"14042","8":"14407205781","9":"145450"},"spu_name":{"0":"广东粗皮香水柠檬","1":"海南小金桔","2":"无籽青柠檬","3":"无籽青柠檬","4":"四川尤力克黄柠檬","5":"四川尤力克黄柠檬","6":"四川尤力克黄柠檬","7":"国产红心火龙果","8":"盆栽薄荷","9":"越南白心火龙果"},"sku_disc":{"0":"5斤*1包\/普通\/单果70-200g","1":"2斤*1包\/一级\/标准规格","2":"5斤*1箱\/其他\/快递\/茉莉奶白专用","3":"8斤*1箱\/其他\/茉莉奶白城配专用","4":"3斤*1包\/三级\/单果75g+","5":"50斤*1箱\/二级\/单果110-150g(净重50斤\/箱)","6":"3斤*1包\/一级\/单果170-200g","7":"30斤*1箱\/普通\/单果约300-400g","8":"1颗*1桶","9":"毛重36-38斤\/普通\/单果500g+(净重31-32斤)"},"store_quantity":{"0":"7","1":"66","2":"93","3":"50","4":"165","5":"32","6":"67","7":"58","8":"11","9":"33"},"ues_quantity":{"0":"0","1":"-7","2":"93","3":"48","4":"143","5":"32","6":"56","7":"48","8":"6","9":"33"},"road_quantity":{"0":"0","1":"249","2":"0","3":"0","4":"0","5":"37","6":"0","7":"0","8":"0","9":"0"},"sale_snt":{"0":"0","1":"65","2":"0","3":"0","4":"22","5":"0","6":"11","7":"10","8":"5","9":"0"},"sale_amt":{"0":"0","1":"9993.5","2":"0","3":"0","4":"3622.3","5":"0","6":"2119","7":"2092","8":"1507.5","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   store_quantity |   ues_quantity |   road_quantity |   sale_snt |
|:------|---------------:|-----------------:|---------------:|----------------:|-----------:|
| count |      1263      |        1263      |      1263      |      1263       | 1263       |
| mean  |        63.0024 |          33.9794 |        26.0792 |         8.02771 |    6.32937 |
| std   |        46.0701 |          65.9304 |        50.8098 |        34.7989  |   27.1977  |
| min   |         2      |           0      |       -34      |         0       |    0       |
| 25%   |        24      |           5      |         3      |         0       |    0       |
| 50%   |        59      |          14      |        11      |         0       |    1       |
| 75%   |        69      |          35      |        28      |         0       |    5       |
| max   |       155      |         958      |       615      |       703       |  802       |