# app_stc_damage_detail_report_df
* comment: saas货损明细
* last_data_modified_time: 2025-09-18 02:18:11

# schema:
CREATE TABLE summerfarm_tech.`app_stc_damage_detail_report_df` (
  `outbound_date` DATETIME COMMENT '出库日期',
  `damage_no` STRING COMMENT '批次',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '仓库',
  `sku_id` BIGINT COMMENT 'sku',
  `spu_id` BIGINT COMMENT 'pd_id',
  `xianmu_sku` STRING COMMENT '鲜沐sku',
  `xianmu_spu_id` BIGINT COMMENT '鲜沐pd_id',
  `name` STRING COMMENT '商品名称',
  `specification` STRING COMMENT '规格',
  `unit` STRING COMMENT '规格单位',
  `purchaser` STRING COMMENT '采购人',
  `damage_type` STRING COMMENT '货损类型',
  `credentials` STRING COMMENT '货损凭证',
  `damage_quantity` BIGINT COMMENT '货损数量',
  `damage_amount` DECIMAL(38,18) COMMENT '货损金额',
  `tenant_id` BIGINT COMMENT '租户Id',
  `category_id` BIGINT COMMENT '一级类目',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `warehouse_service_provider` STRING COMMENT '仓库服务商',
  `price` DECIMAL(38,18) COMMENT '采购单价'
)
COMMENT 'saas货损明细'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"outbound_date":{"0":"2022-03-09 16:19:46","1":"2022-04-14 13:59:34","2":"2022-08-16 14:09:04","3":"2023-09-18 12:09:04","4":"2023-09-18 12:09:49","5":"2023-09-20 13:54:09","6":"2023-09-21 18:41:04","7":"2023-09-21 18:46:04","8":"2023-09-22 15:47:52","9":"2023-09-25 09:57:47"},"damage_no":{"0":"2022030692202038","1":"2021111792291176","2":"2021120292201166","3":"20230610218951068","4":"20230610218951068","5":"None","6":"0120230906751872","7":"202308251079135087","8":"202309051079122116","9":"None"},"warehouse_no":{"0":"48","1":"2","2":"2","3":"96","4":"96","5":"10","6":"117","7":"117","8":"117","9":"10"},"warehouse_name":{"0":"长沙总仓","1":"上海总仓","2":"上海总仓","3":"虚拟仓库","4":"虚拟仓库","5":"嘉兴总仓","6":"东莞冷冻总仓","7":"东莞冷冻总仓","8":"东莞冷冻总仓","9":"嘉兴总仓"},"sku_id":{"0":"101007","1":"108395","2":"108509","3":"102868","4":"102868","5":"100064","6":"100375","7":"102163","8":"101035","9":"100101"},"spu_id":{"0":"100886","1":"105397","2":"105502","3":"101865","4":"101865","5":"100045","6":"101377","7":"101392","8":"100869","9":"100832"},"xianmu_sku":{"0":"555830544572","1":"1742613766","2":"9438680568","3":"663548804016","4":"663548804016","5":"631404023472","6":"892428215046","7":"1038737513678","8":"782670410617","9":"830434202726"},"xianmu_spu_id":{"0":"3216","1":"1318","2":"1936","3":"5666","4":"5666","5":"4057","6":"3175","7":"5701","8":"4037","9":"4085"},"name":{"0":"自采丹东草莓","1":"可可百利薄脆碎饼干","2":"尖口四层纸吸管","3":"香大琥珀珍珠-斯味洛","4":"香大琥珀珍珠-斯味洛","5":"520ml中塑杯","6":"果木大鸡排","7":"奶酥牛角包-本来不该有","8":"冷冻牛油果泥 本来不该有咖啡","9":"玫瑰花酱（人在茶在）"},"specification":{"0":"毛重4.6-6.5斤\/一级\/净重4斤20g-40g","1":"2.5KG*1盒","2":"100只*1包","3":"1KG*20包","4":"1KG*20包","5":"500个","6":"1KG*5包","7":"70G*30个","8":"250g*24包","9":"1KG*1罐"},"unit":{"0":"筐","1":"盒","2":"包","3":"箱","4":"箱","5":"箱","6":"组","7":"箱","8":"箱","9":"罐"},"purchaser":{"0":"杨润韬","1":"杨润韬","2":"杨润韬","3":"斯味洛订货商城管理员","4":"斯味洛订货商城管理员","5":"None","6":"None","7":"董瑞赞","8":"董瑞赞","9":"None"},"damage_type":{"0":"变质货损","1":"过期货损","2":"其他","3":"None","4":"None","5":"运营部-滞销过期","6":"仓-破损","7":"仓-破损","8":"仓-其它","9":"运营部-滞销过期"},"credentials":{"0":"car\/2bibt7lpw4ljaixf9.jpg","1":"reasonImages\/wrx3hmuirooyigqjb.png","2":"艾伦司机买赔","3":"None","4":"None","5":"reasonImages\/k2zwu9du4lrbwolg.jpg","6":"reasonImages\/pu9glhj0lht1lkoi.png","7":"reasonImages\/rn6wc2mlcyqt1ryqd.png","8":"仓库买赔","9":"reasonImages\/m7fad1i38rsy8nzs1.jpg"},"damage_quantity":{"0":"1","1":"1","2":"2","3":"300","4":"58","5":"1","6":"2","7":"1","8":"4","9":"10"},"damage_amount":{"0":"79","1":"220","2":"20.36","3":"57000","4":"11020","5":"None","6":"None","7":"130","8":"1640","9":"None"},"tenant_id":{"0":"11","1":"53","2":"53","3":"32","4":"32","5":"6","6":"7","7":"4","8":"4","9":"6"},"category_id":{"0":"555","1":"635","2":"626","3":"663","4":"663","5":"631","6":"892","7":"699","8":"782","9":"830"},"time_tag":{"0":"20220309","1":"20220414","2":"20220816","3":"20230918","4":"20230918","5":"20230920","6":"20230921","7":"20230921","8":"20230922","9":"20230925"},"warehouse_service_provider":{"0":"杭州鲜沐科技有限公司","1":"杭州鲜沐科技有限公司","2":"杭州鲜沐科技有限公司","3":"广州肆捌城餐饮管理有限公司","4":"广州肆捌城餐饮管理有限公司","5":"杭州鲜沐科技有限公司","6":"杭州鲜沐科技有限公司","7":"杭州鲜沐科技有限公司","8":"杭州鲜沐科技有限公司","9":"杭州鲜沐科技有限公司"},"price":{"0":"79","1":"220","2":"10.18","3":"190","4":"190","5":"None","6":"None","7":"130","8":"410","9":"None"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | outbound_date                 |   warehouse_no |    sku_id |    spu_id |   xianmu_spu_id |   damage_quantity |   tenant_id |   category_id |
|:------|:------------------------------|---------------:|----------:|----------:|----------------:|------------------:|------------:|--------------:|
| count | 957                           |       957      |    957    |    957    |          957    |          957      |    957      |       957     |
| mean  | 2024-07-25 10:57:10.112852736 |        58.4504 | 109435    | 105282    |         8534.77 |           45.7691 |     42.7544 |       807.009 |
| min   | 2022-03-09 16:19:46           |         1      | 100064    |   4178    |         1318    |            1      |      2      |       526     |
| 25%   | 2023-12-26 17:19:49           |        10      | 102163    | 101392    |         5089    |            1      |      7      |       638     |
| 50%   | 2024-09-10 13:51:29           |        62      | 108509    | 105417    |         7192    |            3      |     32      |       783     |
| 75%   | 2025-03-25 13:49:18           |        96      | 115102    | 109016    |        10890    |           20      |     85      |       992     |
| max   | 2025-09-17 11:10:55           |       175      | 124510    | 115613    |        17940    |         4199      |    116      |      1131     |
| std   | nan                           |        49.3092 |   7377.92 |   8587.27 |         3854.24 |          217.395  |     34.2185 |       183.149 |