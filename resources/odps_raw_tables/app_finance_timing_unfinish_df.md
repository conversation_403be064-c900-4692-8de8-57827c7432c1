# app_finance_timing_unfinish_df
* comment: 财务口径省心送余额表
* last_data_modified_time: 2025-09-18 02:48:31

# schema:
CREATE TABLE summerfarm_tech.`app_finance_timing_unfinish_df` (
  `order_no` STRING COMMENT '订单编号',
  `order_time` DATETIME COMMENT '下单时间',
  `cust_id` BIGINT COMMENT '客户ID',
  `cust_name` STRING COMMENT '客户名',
  `cust_team` STRING COMMENT '客户团队类型:平台客户、大客户',
  `sku_id` STRING COMMENT 'SKU',
  `spu_name` STRING COMMENT '商品名',
  `category1` STRING COMMENT '商品一级类目',
  `sku_cnt` BIGINT COMMENT '订单内商品件数',
  `origin_total_amt` DECIMAL(38,18) COMMENT '订单应付金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '订单实付金额',
  `unfinish_sku_cnt` BIGINT COMMENT '未配送商品件数',
  `unfinish_amt` DECIMAL(38,18) COMMENT '未配送金额',
  `date_flag` STRING COMMENT '日期标识',
  `order_date` STRING COMMENT '下单日期'
)
COMMENT '财务口径省心送余额表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"order_no":{"0":"02165414043508008","1":"02165434990371314","2":"02165500016336407","3":"02166668499483247","4":"02167022746129246"},"order_time":{"0":1655626928000,"1":1655655998000,"2":1655625146000,"3":1666713795000,"4":1670256261000},"cust_id":{"0":149054,"1":193593,"2":188356,"3":306006,"4":219967},"cust_name":{"0":"麦乔手作","1":"羊羊蛋糕","2":"达令手作烘焙","3":"杏意蛋糕馆","4":"HZ甜品吾悦店"},"cust_team":{"0":"平台客户","1":"平台客户","2":"平台客户","3":"平台客户","4":"平台客户"},"sku_id":{"0":"N001S01R005","1":"782442074225","2":"872543715","3":"N001S01R005","4":"N001S01R005"},"spu_name":{"0":"安佳淡奶油","1":"安德鲁果溶(阿方索芒果)","2":"黑白全脂牛奶","3":"安佳淡奶油","4":"安佳淡奶油"},"category1":{"0":"乳制品","1":"其他","2":"乳制品","3":"乳制品","4":"乳制品"},"sku_cnt":{"0":5,"1":80,"2":5,"3":10,"4":10},"origin_total_amt":{"0":2275.0,"1":5360.0,"2":560.0,"3":4630.0,"4":4560.0},"real_total_amt":{"0":2125.0,"1":4400.0,"2":520.0,"3":4630.0,"4":4505.0},"unfinish_sku_cnt":{"0":1,"1":16,"2":5,"3":4,"4":2},"unfinish_amt":{"0":425.0,"1":880.0,"2":520.0,"3":1852.0,"4":901.0},"date_flag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"order_date":{"0":"20220619","1":"20220619","2":"20220619","3":"20221025","4":"20221205"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | order_time                    |   cust_id |   sku_cnt |   unfinish_sku_cnt |
|:------|:------------------------------|----------:|----------:|-------------------:|
| count | 3202                          |      3202 | 3202      |         3202       |
| mean  | 2025-07-20 00:11:20.508432128 |    277667 |   13.534  |            8.04685 |
| min   | 2020-02-27 13:55:25           |       132 |    1      |            1       |
| 25%   | 2025-08-05 21:51:49.750000128 |    144909 |    5      |            3       |
| 50%   | 2025-08-27 13:15:15.500000    |    285264 |   10      |            5       |
| 75%   | 2025-09-09 21:23:57.500000    |    419497 |   12      |            9       |
| max   | 2025-09-17 23:43:34           |    574012 |  700      |          200       |
| std   | nan                           |    163885 |   22.3266 |           11.4069  |