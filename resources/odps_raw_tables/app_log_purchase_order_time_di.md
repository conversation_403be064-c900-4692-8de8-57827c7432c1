# app_log_purchase_order_time_di
* comment: 下单时长效率
* last_data_modified_time: 2025-09-18 02:32:00

# schema:
CREATE TABLE summerfarm_tech.`app_log_purchase_order_time_di` (
  `date` STRING COMMENT '日期',
  `is_purchase` STRING COMMENT '根据下单前行为划分：下单前15分钟点击采购助手的商品，含常购商品和榜单推荐商品',
  `type` STRING COMMENT 'V3、V4、对照组',
  `order_cnt` BIGINT COMMENT '下单数',
  `order_interval_minute` DECIMAL(38,18) COMMENT '下单间隔时长均值_分钟',
  `order_interval_minute_75` DECIMAL(38,18) COMMENT '下单间隔时长75分位_分钟',
  `order_interval_minute_mid` DECIMAL(38,18) COMMENT '下单间隔时长中位值_分钟',
  `order_interval_minute_25` DECIMAL(38,18) COMMENT '下单间隔时长25分位_分钟'
)
COMMENT '下单时长效率'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917"},"is_purchase":{"0":"a未进入","1":"b进入","2":"a未进入"},"type":{"0":"V4","1":"V4","2":"对照组"},"order_cnt":{"0":"8222","1":"572","2":"333"},"order_interval_minute":{"0":"3.712408759124088","1":"4.851398601398602","2":"0"},"order_interval_minute_75":{"0":"5.083333333333333","1":"7.3","2":"0.2833333333333333"},"order_interval_minute_mid":{"0":"2.016666666666667","1":"3.083333333333333","2":"0.2666666666666667"},"order_interval_minute_25":{"0":"0.85","1":"1.229166666666667","2":"0.1666666666666667"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   order_cnt |
|:------|------------:|
| count |        3    |
| mean  |     3042.33 |
| std   |     4487.31 |
| min   |      333    |
| 25%   |      452.5  |
| 50%   |      572    |
| 75%   |     4397    |
| max   |     8222    |