# app_stc_sku_temporary_damage_delivery_di
* comment: 临保品仓库数据信息表
* last_data_modified_time: 2025-09-18 03:05:23

# schema:
CREATE TABLE summerfarm_tech.`app_stc_sku_temporary_damage_delivery_di` (
  `date` STRING COMMENT '日期',
  `sku_id` STRING COMMENT 'sku编号',
  `spu_name` STRING COMMENT '商品名称',
  `temporary_store_cnt` BIGINT COMMENT '转入批次临保库存数量',
  `temporary_store_amt` DECIMAL(38,18) COMMENT '转入批次临保库存金额',
  `temporary_sale_cnt` BIGINT COMMENT 'SKU性质为临保的当日销售数量',
  `temporary_sale_amt` DECIMAL(38,18) COMMENT 'SKU性质为临保的当日销售金额',
  `temporary_damage_cnt` BIGINT COMMENT 'SKU性质为临保的当日货损数量',
  `temporary_damage_amt` DECIMAL(38,18) COMMENT 'SKU性质为临保的当日货损金额',
  `temporary_deliver_amt` DECIMAL(38,18) COMMENT 'SKU性质为临保的当日配送GMV（除批发）',
  `deliver_total_amt` DECIMAL(38,18) COMMENT '整体当日配送GMV（除批发）'
)
COMMENT '临保品仓库数据信息表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"sku_id":{"0":"1571800865","1":"2400337482","2":"318034","3":"402107515","4":"422371038","5":"422371062","6":"464633254","7":"605386061242","8":"605455167181","9":"608802002341"},"spu_name":{"0":"宝茸果茸系列","1":"爱真稀奶油38%","2":"琪雷萨马斯卡彭","3":"肯迪雅淡奶油","4":"爱真稀奶油35%","5":"爱真稀奶油35%","6":"艾恩摩尔35%淡奶油","7":"爱真牧场稀奶油","8":"新悦纯牧超高温灭菌搅打稀奶油","9":"优诺高品质冷藏牛乳"},"temporary_store_cnt":{"0":"0","1":"0","2":"1","3":"0","4":"0","5":"0","6":"0","7":"21","8":"2","9":"1"},"temporary_store_amt":{"0":"0","1":"0","2":"37","3":"0","4":"0","5":"0","6":"0","7":"836.43","8":"70","9":"116.18"},"temporary_sale_cnt":{"0":"1","1":"8","2":"0","3":"1","4":"1","5":"0","6":"1","7":"0","8":"0","9":"0"},"temporary_sale_amt":{"0":"85","1":"332","2":"0","3":"42.18","4":"443","5":"0","6":"36","7":"0","8":"0","9":"0"},"temporary_damage_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"temporary_damage_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"temporary_deliver_amt":{"0":"38.38","1":"298.98","2":"0","3":"0","4":"1920.6","5":"193.05","6":"34.44","7":"0","8":"0","9":"0"},"deliver_total_amt":{"0":"2773622.511904761904761943","1":"2773622.511904761904761943","2":"2773622.511904761904761943","3":"2773622.511904761904761943","4":"2773622.511904761904761943","5":"2773622.511904761904761943","6":"2773622.511904761904761943","7":"2773622.511904761904761943","8":"2773622.511904761904761943","9":"2773622.511904761904761943"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   temporary_store_cnt |   temporary_sale_cnt |   temporary_damage_cnt |
|:------|----------------------:|---------------------:|-----------------------:|
| count |              16       |             16       |               16       |
| mean  |               2.8125  |              1.1875  |                1       |
| std   |               6.93031 |              2.34432 |                2.70801 |
| min   |               0       |              0       |                0       |
| 25%   |               0       |              0       |                0       |
| 50%   |               0       |              0       |                0       |
| 75%   |               1       |              1       |                0       |
| max   |              21       |              8       |               10       |