# app_saas_merchant_store_item_order_analysis_month_df
* comment: saas-门店商品订货分析-月
* last_data_modified_time: 2025-09-18 02:19:55

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_item_order_analysis_month_df` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd 月初日期',
  `item_id` BIGINT COMMENT '商品id',
  `store_id` BIGINT COMMENT '门店id',
  `average_order_period` DECIMAL(38,18) COMMENT '平均订货周期',
  `average_order_period_last_period` DECIMAL(38,18) COMMENT '上周期平均订货周期',
  `average_order_period_upper_period` DECIMAL(38,18) COMMENT '平均订货周期环比',
  `order_amount` BIGINT COMMENT '订货数量',
  `order_amount_last_period` BIGINT COMMENT '上周期订货数量',
  `order_amount_upper_period` DECIMAL(38,18) COMMENT '订货数量环比',
  `order_price` DECIMAL(38,18) COMMENT '订货金额',
  `order_price_last_period` DECIMAL(38,18) COMMENT '上周期订货金额',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '订货金额环比',
  `last_order_time` STRING COMMENT '最后订货日期 yyyy-MM-dd',
  `last_order_amount` BIGINT COMMENT '最后订货数量',
  `last_order_price` DECIMAL(38,18) COMMENT '最后订货金额'
)
COMMENT 'saas-门店商品订货分析-月'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"time_tag":{"0":"20220601","1":"20220601","2":"20220601","3":"20220601","4":"20220601"},"item_id":{"0":"1","1":"3","2":"9","3":"16","4":"17"},"store_id":{"0":"5","1":"3","2":"1","3":"1","4":"1"},"average_order_period":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"average_order_period_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"average_order_period_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_amount":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"order_amount_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_amount_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_price":{"0":"0.01","1":"0.02","2":"26","3":"27","4":"58"},"order_price_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_price_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"last_order_time":{"0":"2022-06-02","1":"2022-06-02","2":"2022-06-02","3":"2022-06-19","4":"2022-06-19"},"last_order_amount":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"last_order_price":{"0":"0.01","1":"0.02","2":"26","3":"27","4":"58"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |    item_id |   store_id |   order_amount |   order_amount_last_period |   last_order_amount |
|:------|------------:|-----------:|-----------:|---------------:|---------------------------:|--------------------:|
| count | 10000       | 10000      |  10000     |     10000      |                10000       |         10000       |
| mean  |     4.6738  |   204.431  |    202.339 |         6.5621 |                    2.9726  |             2.5312  |
| std   |     0.99282 |    92.4234 |    141.803 |        12.5082 |                    8.07703 |             5.47149 |
| min   |     2       |     1      |      1     |         1      |                    0       |             1       |
| 25%   |     4       |   134      |     66     |         1      |                    0       |             1       |
| 50%   |     4       |   189      |    182     |         2      |                    0       |             1       |
| 75%   |     6       |   266      |    314     |         6      |                    2       |             2       |
| max   |     8       |   573      |    655     |       250      |                  142       |           250       |