# app_spu_delivery_selfowend_mi
* comment: 城市整体配送数据日表
* last_data_modified_time: 2025-09-18 03:19:15

# schema:
CREATE TABLE summerfarm_tech.`app_spu_delivery_selfowend_mi` (
  `month` STRING COMMENT '月份',
  `cause_type` STRING COMMENT '鲜沐，SAAS',
  `province` STRING COMMENT '省',
  `admin_city` STRING COMMENT '市',
  `area` STRING COMMENT '区',
  `spu_id` BIGINT COMMENT 'pd_id',
  `spu_name` STRING COMMENT 'spu名称',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举：大客户,普通,批发客户;<以前的：单店,批发大客户,普通大客户,KA大客户 已弃用>',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目',
  `category2_id` STRING COMMENT '二级类目id',
  `category2` STRING COMMENT '二级类目',
  `category3_id` STRING COMMENT '三级类目id',
  `category3` STRING COMMENT '三级类目',
  `category4_id` STRING COMMENT '四级类目id',
  `category4` STRING COMMENT '四级类目',
  `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '总成本',
  `origin_pay_margin` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_pay_margin` DECIMAL(38,18) COMMENT '实付毛利润',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `deliver_total_amt` DECIMAL(38,18) COMMENT '配送GMV'
)
COMMENT '城市整体配送数据日表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509","5":"202509","6":"202509","7":"202509","8":"202509","9":"202509"},"cause_type":{"0":"SAAS","1":"SAAS","2":"SAAS","3":"SAAS","4":"SAAS","5":"SAAS","6":"SAAS","7":"SAAS","8":"SAAS","9":"SAAS"},"province":{"0":"上海","1":"上海","2":"上海","3":"上海","4":"上海","5":"上海","6":"上海","7":"上海","8":"上海","9":"上海"},"admin_city":{"0":"上海市","1":"上海市","2":"上海市","3":"上海市","4":"上海市","5":"上海市","6":"上海市","7":"上海市","8":"上海市","9":"上海市"},"area":{"0":"嘉定区","1":"徐汇区","2":"徐汇区","3":"徐汇区","4":"徐汇区","5":"徐汇区","6":"徐汇区","7":"徐汇区","8":"徐汇区","9":"普陀区"},"spu_id":{"0":"4869","1":"4154","2":"4758","3":"4869","4":"6974","5":"7978","6":"8781","7":"9260","8":"13605","9":"3830"},"spu_name":{"0":"Protag纯牛奶","1":"C味糖水红豆罐头","2":"C味血糯米罐头","3":"Protag纯牛奶","4":"澄善草莓果酱","5":"澄善HPP速冻芭乐浆","6":"Protag纯牛奶（带盖）","7":"酷盖纯牛奶","8":"C味冷冻黑糖珍珠（快煮）","9":"C味马蹄爆爆珠"},"cust_type":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"brand_type":{"0":"大客户","1":"大客户","2":"大客户","3":"大客户","4":"大客户","5":"大客户","6":"大客户","7":"大客户","8":"大客户","9":"大客户"},"brand_name":{"0":"Protag蛋白标签","1":"C味","2":"C味","3":"Protag蛋白标签","4":"澄善","5":"澄善","6":"Protag蛋白标签","7":"酷盖","8":"C味","9":"C味"},"category1":{"0":"乳制品","1":"其他","2":"其他","3":"乳制品","4":"其他","5":"其他","6":"乳制品","7":"乳制品","8":"其他","9":"其他"},"category2_id":{"0":"401","1":"416","2":"409","3":"401","4":"417","5":"421","6":"401","7":"401","8":"405","9":"405"},"category2":{"0":"乳制品","1":"蔬菜制品","2":"谷物制品","3":"乳制品","4":"水果制品","5":"饮品原料","6":"乳制品","7":"乳制品","8":"成品原料","9":"成品原料"},"category3_id":{"0":"435","1":"500","2":"470","3":"435","4":"506","5":"522","6":"435","7":"435","8":"457","9":"458"},"category3":{"0":"液体乳","1":"罐头","2":"谷物罐头","3":"液体乳","4":"水果风味制品","5":"果汁原料","6":"液体乳","7":"液体乳","8":"粉圆类配料","9":"果冻类配料"},"category4_id":{"0":"607","1":"775","2":"713","3":"607","4":"785","5":"830","6":"607","7":"607","8":"663","9":"671"},"category4":{"0":"常温牛奶","1":"杂粮罐头","2":"杂粮罐头","3":"常温牛奶","4":"果茶酱","5":"果汁原浆","6":"常温牛奶","7":"常温牛奶","8":"珍珠","9":"爆爆珠"},"origin_total_amt":{"0":"75","1":"22","2":"11","3":"70","4":"375","5":"932","6":"79","7":"5490","8":"2100","9":"66.1"},"real_total_amt":{"0":"75","1":"22","2":"11","3":"70","4":"375","5":"932","6":"79","7":"5490","8":"2100","9":"66.1"},"cost_amt":{"0":"55","1":"15.2","2":"8","3":"55","4":"270","5":"646","6":"57","7":"3474.8","8":"1200","9":"91"},"origin_pay_margin":{"0":"20","1":"6.8","2":"3","3":"15","4":"105","5":"286","6":"22","7":"2015.2","8":"900","9":"-24.9"},"real_pay_margin":{"0":"20","1":"6.8","2":"3","3":"15","4":"105","5":"286","6":"22","7":"2015.2","8":"900","9":"-24.9"},"preferential_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"after_sale_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"deliver_total_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"ds":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509","5":"202509","6":"202509","7":"202509","8":"202509","9":"202509"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   spu_id |
|:------|---------:|
| count | 10000    |
| mean  |  3929.85 |
| std   |  1131.84 |
| min   |  1528    |
| 25%   |  3092    |
| 50%   |  4145    |
| 75%   |  4426    |
| max   | 13605    |