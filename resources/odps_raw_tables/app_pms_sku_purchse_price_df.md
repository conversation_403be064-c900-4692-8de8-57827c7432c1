# app_pms_sku_purchse_price_df
* comment: 历史七天采购品平均价
* last_data_modified_time: 2025-09-18 00:34:59

# schema:
CREATE TABLE summerfarm_tech.`app_pms_sku_purchse_price_df` (
  `statistics_date` DATETIME COMMENT '统计日期',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `sku` STRING COMMENT 'SKU编码',
  `type` BIGINT COMMENT '抽数类型,1近7天采购平均价',
  `average_price` DECIMAL(38,18) COMMENT '平均价'
)
COMMENT '历史七天采购品平均价'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"statistics_date":{"0":"2025-09-17","1":"2025-09-17","2":"2025-09-17","3":"2025-09-17","4":"2025-09-17"},"warehouse_no":{"0":"150","1":"150","2":"150","3":"150","4":"150"},"sku":{"0":"1333513326848","1":"1253412104135","2":"1333645613870","3":"1319080350507","4":"1241487357138"},"type":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"average_price":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | statistics_date     |   warehouse_no |   type |
|:------|:--------------------|---------------:|-------:|
| count | 853                 |       853      |    853 |
| mean  | 2025-09-17 00:00:00 |        39.1841 |      1 |
| min   | 2025-09-17 00:00:00 |         1      |      1 |
| 25%   | 2025-09-17 00:00:00 |        10      |      1 |
| 50%   | 2025-09-17 00:00:00 |        10      |      1 |
| 75%   | 2025-09-17 00:00:00 |        69      |      1 |
| max   | 2025-09-17 00:00:00 |       155      |      1 |
| std   | nan                 |        47.134  |      0 |