# app_xianmu_sale_order_df
* comment: 鲜沐->销售主体销售单
* last_data_modified_time: 2025-09-18 02:42:33

# schema:
CREATE TABLE summerfarm_tech.`app_xianmu_sale_order_df` (
  `order_no` STRING COMMENT '订单号',
  `order_time` DATETIME COMMENT '下单时间',
  `account_model` STRING COMMENT '订单类型',
  `contact` STRING COMMENT '联系人',
  `delivery_time` DATETIME COMMENT '配送时间',
  `address` STRING COMMENT '配送地址',
  `order_account` STRING COMMENT '下单账号',
  `bill_status` STRING COMMENT '开票状态',
  `order_source` STRING COMMENT '订单来源',
  `customer_name` STRING COMMENT '客户名称',
  `customer_type` STRING COMMENT '客户类型',
  `payment_time` DATETIME COMMENT '支付时间',
  `contact_mode` STRING COMMENT '联系方式',
  `sale_manager` STRING COMMENT '销售经理',
  `order_status` STRING COMMENT '订单出库状态',
  `store_no` BIGINT COMMENT '城配仓',
  `task_created` STRING COMMENT '是否生成出库任务',
  `delivery_status` STRING COMMENT '订单配送状态',
  `sale_entity` STRING COMMENT '销售主体',
  `stock_task_type` BIGINT COMMENT '出库任务类型:51-销售出库，52-样品出库，57-补发出库，58-销售自提出库，62-样品字体出库，63-越库出库',
  `stock_task_id` BIGINT COMMENT '出库任务id',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `order_total_price` DECIMAL(38,18) COMMENT '订单商品总价',
  `delivery_fee` DECIMAL(38,18) COMMENT '配送费',
  `delivery_exact` DECIMAL(38,18) COMMENT '精准送',
  `order_overtime` DECIMAL(38,18) COMMENT '超时加单',
  `special_price` DECIMAL(38,18) COMMENT '特价优惠',
  `ladder_price` DECIMAL(38,18) COMMENT '阶梯价优惠',
  `change_price` DECIMAL(38,18) COMMENT '换购优惠',
  `extend_price` DECIMAL(38,18) COMMENT '扩展购买优惠',
  `full_reduce` DECIMAL(38,18) COMMENT '满减',
  `equity_card` DECIMAL(38,18) COMMENT '权益卡',
  `coupon` DECIMAL(38,18) COMMENT '优惠券',
  `red` DECIMAL(38,18) COMMENT '红包',
  `delivery_fee_coupon` DECIMAL(38,18) COMMENT '运费券',
  `delivery_exact_coupon` DECIMAL(38,18) COMMENT '精准送优惠券',
  `advance` DECIMAL(38,18) COMMENT '预付商品'
)
COMMENT '鲜沐->销售主体销售单'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"order_no":{"0":"0125KUYKTA0328141557","1":"0125LQ3OK20328141478","2":"0125OWJCTJ0328142568"},"order_time":{"0":"2025-03-28 14:15:34","1":"2025-03-28 14:14:04","2":"2025-03-28 14:25:04"},"account_model":{"0":"现结","1":"现结","2":"现结"},"contact":{"0":"None","1":"None","2":"None"},"delivery_time":{"0":"2025-03-28 23:59:59","1":"2025-03-28 23:59:59","2":"2025-03-29 00:00:00"},"address":{"0":"浙江省杭州市西湖区春申街","1":"浙江省杭州市西湖区春申街","2":"浙江省杭州市西湖区春申街"},"order_account":{"0":"嘉兴顺鹿达供应链管理有限公司","1":"嘉兴顺鹿达供应链管理有限公司","2":"嘉兴顺鹿达供应链管理有限公司"},"bill_status":{"0":"未开票","1":"未开票","2":"未开票"},"order_source":{"0":"销转采","1":"销转采","2":"销转采"},"customer_name":{"0":"嘉兴顺鹿达供应链管理有限公司","1":"嘉兴顺鹿达供应链管理有限公司","2":"嘉兴顺鹿达供应链管理有限公司"},"customer_type":{"0":"普通客户","1":"普通客户","2":"普通客户"},"payment_time":{"0":"2025-03-28 14:15:46","1":"2025-03-28 14:14:15","2":"2025-03-28 14:25:16"},"contact_mode":{"0":"None","1":"None","2":"None"},"sale_manager":{"0":"None","1":"None","2":"None"},"order_status":{"0":"已出库","1":"已出库","2":"已出库"},"store_no":{"0":"None","1":"None","2":"None"},"task_created":{"0":"是","1":"是","2":"是"},"delivery_status":{"0":"已完成","1":"已完成","2":"已完成"},"sale_entity":{"0":"杭州鲜沐科技有限公司","1":"杭州鲜沐科技有限公司","2":"杭州鲜沐科技有限公司"},"stock_task_type":{"0":"58","1":"58","2":"51"},"stock_task_id":{"0":"704310","1":"704311","2":"704325"},"warehouse_no":{"0":"12","1":"12","2":"12"},"order_total_price":{"0":"1.02","1":"1020","2":"2.045"},"delivery_fee":{"0":"0","1":"0","2":"0"},"delivery_exact":{"0":"0","1":"0","2":"0"},"order_overtime":{"0":"0","1":"0","2":"0"},"special_price":{"0":"0","1":"0","2":"0"},"ladder_price":{"0":"0","1":"0","2":"0"},"change_price":{"0":"0","1":"0","2":"0"},"extend_price":{"0":"0","1":"0","2":"0"},"full_reduce":{"0":"0","1":"0","2":"0"},"equity_card":{"0":"0","1":"0","2":"0"},"coupon":{"0":"0","1":"0","2":"0"},"red":{"0":"0","1":"0","2":"0"},"delivery_fee_coupon":{"0":"0","1":"0","2":"0"},"delivery_exact_coupon":{"0":"0","1":"0","2":"0"},"advance":{"0":"0","1":"0","2":"0"},"ds":{"0":"20250420","1":"20250420","2":"20250420"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | order_time          | delivery_time                 | payment_time                  |   stock_task_type |   stock_task_id |   warehouse_no |
|:------|:--------------------|:------------------------------|:------------------------------|------------------:|----------------:|---------------:|
| count | 3                   | 3                             | 3                             |           3       |          3      |              3 |
| mean  | 2025-03-28 14:18:14 | 2025-03-28 23:59:59.333333248 | 2025-03-28 14:18:25.666666496 |          55.6667  |     704315      |             12 |
| min   | 2025-03-28 14:14:04 | 2025-03-28 23:59:59           | 2025-03-28 14:14:15           |          51       |     704310      |             12 |
| 25%   | 2025-03-28 14:14:49 | 2025-03-28 23:59:59           | 2025-03-28 14:15:00.500000    |          54.5     |     704310      |             12 |
| 50%   | 2025-03-28 14:15:34 | 2025-03-28 23:59:59           | 2025-03-28 14:15:46           |          58       |     704311      |             12 |
| 75%   | 2025-03-28 14:20:19 | 2025-03-28 23:59:59.500000    | 2025-03-28 14:20:31           |          58       |     704318      |             12 |
| max   | 2025-03-28 14:25:04 | 2025-03-29 00:00:00           | 2025-03-28 14:25:16           |          58       |     704325      |             12 |
| std   | nan                 | nan                           | nan                           |           4.04145 |          8.3865 |              0 |