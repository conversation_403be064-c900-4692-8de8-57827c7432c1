# app_log_mall_search_skey_di
* comment: 商城搜索词流量分析表
* last_data_modified_time: 2025-09-18 03:36:04

# schema:
CREATE TABLE summerfarm_tech.`app_log_mall_search_skey_di` (
  `date` STRING COMMENT '日期',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `cust_type` STRING COMMENT '客户行业类型:面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `life_cycle` STRING COMMENT '生命周期标签（粗）',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细）',
  `register_province` STRING COMMENT '注册时省',
  `register_city` STRING COMMENT '注册时市',
  `register_area` STRING COMMENT '注册时区',
  `city_id` BIGINT COMMENT '运营服务区ID',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区id',
  `large_area_name` STRING COMMENT '运营服务大区name',
  `module_name` STRING COMMENT '模块名称',
  `skey` STRING COMMENT '搜索词',
  `pv` BIGINT COMMENT 'PV',
  `uv` BIGINT COMMENT 'UV'
)
COMMENT '商城搜索词流量分析表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"cust_team":{"0":"Mars大客户","1":"Mars大客户","2":"Mars大客户","3":"Mars大客户","4":"Mars大客户"},"cust_type":{"0":"茶饮","1":"茶饮","2":"茶饮","3":"茶饮","4":"茶饮"},"life_cycle":{"0":"稳定期","1":"稳定期","2":"稳定期","3":"稳定期","4":"稳定期"},"life_cycle_detail":{"0":"S1","1":"S1","2":"S1","3":"S1","4":"S1"},"register_province":{"0":"浙江","1":"浙江","2":"浙江","3":"浙江","4":"浙江"},"register_city":{"0":"杭州市","1":"杭州市","2":"杭州市","3":"杭州市","4":"杭州市"},"register_area":{"0":"滨江区","1":"上城区","2":"滨江区","3":"上城区","4":"上城区"},"city_id":{"0":"1001","1":"1001","2":"1001","3":"1001","4":"1001"},"city_name":{"0":"杭州","1":"杭州","2":"杭州","3":"杭州","4":"杭州"},"large_area_id":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"large_area_name":{"0":"杭州大区","1":"杭州大区","2":"杭州大区","3":"杭州大区","4":"杭州大区"},"module_name":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他"},"skey":{"0":"佳农凤梨","1":"凤梨","2":"小青柑","3":"广东粗皮香水柠檬","4":"广东红心芭乐"},"pv":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"uv":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   city_id |   large_area_id |          pv |          uv |
|:------|----------:|----------------:|------------:|------------:|
| count |   10000   |      10000      | 10000       | 10000       |
| mean  |   21316.8 |         32.0143 |     1.6102  |     1.0156  |
| std   |   15326.4 |         31.6844 |     1.25945 |     0.13623 |
| min   |    1001   |          1      |     1       |     1       |
| 25%   |    9167   |          2      |     1       |     1       |
| 50%   |   17006   |         14      |     1       |     1       |
| 75%   |   37403   |         72      |     2       |     1       |
| max   |   44269   |         91      |    27       |     4       |