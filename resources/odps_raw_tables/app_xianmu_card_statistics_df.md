# app_xianmu_card_statistics_df
* comment: 鲜沐卡余额统计表
* last_data_modified_time: 2025-09-18 02:06:35

# schema:
CREATE TABLE summerfarm_tech.`app_xianmu_card_statistics_df` (
  `stat_date` STRING COMMENT '统计日期 yyyymmdd格式',
  `total_balance` DECIMAL(38,18) COMMENT '鲜沐卡总余额',
  `total_balance_weekly_ratio` DECIMAL(38,18) COMMENT '鲜沐卡总余额周环比（%）',
  `store_count_with_balance` BIGINT COMMENT '有余额的门店数量',
  `store_count_weekly_ratio` DECIMAL(38,18) COMMENT '有余额的门店数量周环比（%）',
  `avg_store_balance` DECIMAL(38,18) COMMENT '店均余额',
  `avg_store_balance_weekly_ratio` DECIMAL(38,18) COMMENT '店均余额周环比（%）'
)
COMMENT '鲜沐卡余额统计表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"stat_date":{"0":"20250917"},"total_balance":{"0":"1973062.579999999"},"total_balance_weekly_ratio":{"0":"-6.889113552873378"},"store_count_with_balance":{"0":"3892"},"store_count_weekly_ratio":{"0":"0.1286339078981219"},"avg_store_balance":{"0":"506.95338643371"},"avg_store_balance_weekly_ratio":{"0":"-7.008731855092192"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   store_count_with_balance |
|:------|---------------------------:|
| count |                          1 |
| mean  |                       3892 |
| std   |                        nan |
| min   |                       3892 |
| 25%   |                       3892 |
| 50%   |                       3892 |
| 75%   |                       3892 |
| max   |                       3892 |