# app_saas_product_sales_overview_week_di
* comment: saas商品销售概况表(周维度)
* last_data_modified_time: 2025-09-18 02:34:43

# schema:
CREATE TABLE summerfarm_tech.`app_saas_product_sales_overview_week_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd(周一)',
  `category_id` BIGINT COMMENT '三级类目id',
  `store_type` BIGINT COMMENT '门店类型：0、直营店 1、加盟店 2、托管店',
  `store_id` BIGINT COMMENT '门店id',
  `store_name` STRING COMMENT '门店名',
  `province` STRING COMMENT '省',
  `city` STRING COMMENT '市',
  `pay_success_num` BIGINT COMMENT '支付成功商品件数',
  `pay_success_price` DECIMAL(38,18) COMMENT '支付成功金额',
  `refund_num` BIGINT COMMENT '退款件数',
  `refund_price` DECIMAL(38,18) COMMENT '退款金额',
  `warehouse_type` BIGINT COMMENT '归属类型 0自营品 1三方品',
  `delivery_type` BIGINT COMMENT '配送方式 0品牌方配送 1三方配送',
  `item_id` BIGINT COMMENT '商品编码',
  `title` STRING COMMENT '商品名称',
  `goods_type` BIGINT COMMENT '商品类型 0无货商品 1报价货品 2自营货品'
)
COMMENT 'saas商品销售概况表(周维度)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"7","4":"7"},"time_tag":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"category_id":{"0":"nan","1":"nan","2":"611.0","3":"892.0","4":"892.0"},"store_type":{"0":"0","1":"0","2":"0","3":"1","4":"1"},"store_id":{"0":"2","1":"348361","2":"348361","3":"521","4":"521"},"store_name":{"0":"乔治门店","1":"佳一门店2","2":"佳一门店2","3":"佛山狮山小塘万民广场店","4":"佛山狮山小塘万民广场店"},"province":{"0":"广东","1":"浙江","2":"浙江","3":"广东","4":"广东"},"city":{"0":"梅州市","1":"杭州市","2":"杭州市","3":"佛山市","4":"佛山市"},"pay_success_num":{"0":"1","1":"5","2":"5","3":"1","4":"1"},"pay_success_price":{"0":"0.01","1":"0.1","2":"1000","3":"205","4":"345"},"refund_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"refund_price":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"warehouse_type":{"0":"0","1":"0","2":"2","3":"1","4":"1"},"delivery_type":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"item_id":{"0":"18841","1":"18842","2":"44829","3":"374","4":"380"},"title":{"0":"安佳奶油A","1":"安佳奶油B","2":"佳一20250916","3":"香酥盐酥鸡","4":"冷冻名古屋脆皮全翅"},"goods_type":{"0":"0","1":"0","2":"2","3":"2","4":"2"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   category_id |   store_type |   store_id |   pay_success_num |   refund_num |   warehouse_type |   item_id |   goods_type |
|:------|------------:|--------------:|-------------:|-----------:|------------------:|-------------:|-----------------:|----------:|-------------:|
| count |   6887      |      6178     |  6887        |       6887 |         6887      |    6887      |      6887        |    6887   |  6887        |
| mean  |     59.166  |       669.18  |     0.729055 |     363545 |            2.8471 |      18.4238 |         1.04879  |   31273.5 |     1.33512  |
| std   |     38.1231 |       160.997 |     0.470201 |     200091 |           15.6003 |     239.622  |         0.502334 |   13136.9 |     0.654808 |
| min   |      2      |       526     |     0        |          2 |            0      |       0      |         0        |     279   |     0        |
| 25%   |     14      |       550     |     0        |     359883 |            1      |       0      |         1        |   24831   |     1        |
| 50%   |     59      |       607     |     1        |     443516 |            1      |       0      |         1        |   35453   |     1        |
| 75%   |    100      |       784     |     1        |     509536 |            2      |       0      |         1        |   42195   |     2        |
| max   |    123      |      1129     |     2        |     543362 |         1000      |    8000      |         2        |   44880   |     2        |