# app_service_area_deliver_cost_wi
* comment: 服务区域城配仓配送费用明细
* last_data_modified_time: 2025-09-17 12:00:39

# schema:
CREATE TABLE summerfarm_tech.`app_service_area_deliver_cost_wi` (
  `year` STRING COMMENT '年',
  `week_of_year` STRING COMMENT '周数',
  `monday` STRING COMMENT '周一',
  `sunday` STRING COMMENT '周天',
  `service_area` STRING COMMENT '服务区域',
  `area_no` BIGINT COMMENT '城配仓号',
  `area_name` STRING COMMENT '城配仓',
  `out_point_cnt` BIGINT COMMENT '外区点位数',
  `in_point_cnt` BIGINT COMMENT '内区点位数',
  `total_point_cnt` BIGINT COMMENT '总点位数',
  `heytea_point_cnt` BIGINT COMMENT '喜茶点位数',
  `self_point_cnt` BIGINT COMMENT '自营点位数',
  `bms_delivery_amt` DECIMAL(38,18) COMMENT 'BMS 配送费',
  `offine_delivery_amt` DECIMAL(38,18) COMMENT '线下配送费',
  `total_delivery_amt` DECIMAL(38,18) COMMENT '总配送费',
  `offine_other_amt` DECIMAL(38,18) COMMENT '自营配送额外费_管理费税费及其他',
  `heytea_other_amt` DECIMAL(38,18) COMMENT '喜茶配送额外费_管理费税费及其他',
  `heytea_bms_deliver_amt` DECIMAL(38,18) COMMENT '喜茶BMS费',
  `heytea_may_car_amt` DECIMAL(38,18) COMMENT '喜茶专车费',
  `heytea_car_amt` DECIMAL(38,18) COMMENT '喜茶打车费',
  `heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送费',
  `self_delivery_amt` DECIMAL(38,18) COMMENT '自营配送费'
)
COMMENT '服务区域城配仓配送费用明细'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025","5":"2025","6":"2025","7":"2025","8":"2025","9":"2025"},"week_of_year":{"0":"37","1":"37","2":"37","3":"37","4":"37","5":"37","6":"37","7":"37","8":"37","9":"37"},"monday":{"0":"20250908","1":"20250908","2":"20250908","3":"20250908","4":"20250908","5":"20250908","6":"20250908","7":"20250908","8":"20250908","9":"20250908"},"sunday":{"0":"20250914","1":"20250914","2":"20250914","3":"20250914","4":"20250914","5":"20250914","6":"20250914","7":"20250914","8":"20250914","9":"20250914"},"service_area":{"0":"华东","1":"华北","2":"华南","3":"福建","4":"广西","5":"华东","6":"华东","7":"福建","8":"昆明","9":"华东"},"area_no":{"0":"34","1":"144","2":"27","3":"58","4":"75","5":"2","6":"33","7":"38","8":"76","9":"9"},"area_name":{"0":"淮安仓","1":"济南仓","2":"深圳仓","3":"莆田仓","4":"南宁仓","5":"上海仓","6":"扬州仓","7":"福州仓","8":"昆明仓","9":"苏州仓"},"out_point_cnt":{"0":"220","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_point_cnt":{"0":"391","1":"347","2":"2329","3":"459","4":"482","5":"2021","6":"1238","7":"775","8":"228","9":"3326"},"total_point_cnt":{"0":"611","1":"347","2":"2329","3":"459","4":"482","5":"2021","6":"1238","7":"775","8":"228","9":"3326"},"heytea_point_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"self_point_cnt":{"0":"611","1":"347","2":"2329","3":"459","4":"482","5":"2021","6":"1238","7":"775","8":"228","9":"3326"},"bms_delivery_amt":{"0":"18266.08","1":"6072.5","2":"41764.4","3":"10511.45","4":"8917","5":"45411.4","6":"21750.72","7":"14822.15","8":"4332","9":"70334.34"},"offine_delivery_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"3794","6":"0","7":"0","8":"0","9":"0"},"total_delivery_amt":{"0":"18266.08","1":"6072.5","2":"41764.4","3":"10511.45","4":"8917","5":"49205.4","6":"21750.72","7":"14822.15","8":"4332","9":"70334.34"},"offine_other_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"3274","6":"0","7":"0","8":"0","9":"0"},"heytea_other_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"520","6":"0","7":"0","8":"0","9":"0"},"heytea_bms_deliver_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"heytea_may_car_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"heytea_car_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"heytea_deliver_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"520","6":"0","7":"0","8":"0","9":"0"},"self_delivery_amt":{"0":"18266.08","1":"6072.5","2":"41764.4","3":"10511.45","4":"8917","5":"48685.4","6":"21750.72","7":"14822.15","8":"4332","9":"70334.34"},"ds":{"0":"20250910","1":"20250910","2":"20250910","3":"20250910","4":"20250910","5":"20250910","6":"20250910","7":"20250910","8":"20250910","9":"20250910"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   area_no |   out_point_cnt |   in_point_cnt |   total_point_cnt |   heytea_point_cnt |   self_point_cnt |
|:------|----------:|----------------:|---------------:|------------------:|-------------------:|-----------------:|
| count |   53      |         53      |         53     |            53     |                 53 |           53     |
| mean  |   57.5472 |         78.3019 |       1077.21  |          1155.51  |                  0 |         1155.51  |
| std   |   37.6285 |        158.523  |        863.116 |           851.845 |                  0 |          851.845 |
| min   |    1      |          0      |          0     |             0     |                  0 |            0     |
| 25%   |   27      |          0      |        391     |           493     |                  0 |          493     |
| 50%   |   52      |          0      |        823     |           876     |                  0 |          876     |
| 75%   |   85      |         63      |       1397     |          1717     |                  0 |         1717     |
| max   |  144      |        801      |       3326     |          3326     |                  0 |         3326     |