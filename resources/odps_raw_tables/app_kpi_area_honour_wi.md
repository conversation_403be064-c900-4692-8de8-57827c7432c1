# app_kpi_area_honour_wi
* comment: 履约kpi城配仓维度日表
* last_data_modified_time: 2025-09-18 03:43:04

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_area_honour_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `area_no` BIGINT COMMENT '城配仓',
  `area_name` STRING COMMENT '城配仓名',
  `sku_type` STRING COMMENT '自营，代仓',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本价',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率',
  `refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `total_deliver_amt` DECIMAL(38,18) COMMENT '履约总费用'
)
COMMENT '履约kpi城配仓维度日表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"area_no":{"0":"1","1":"1","2":"1","3":"2","4":"2"},"area_name":{"0":"杭州仓","1":"杭州仓","2":"杭州仓","3":"上海仓","4":"上海仓"},"sku_type":{"0":"代仓","1":"代售","2":"自营","3":"代售","4":"自营"},"origin_total_amt":{"0":"66490.1","1":"36668.74","2":"295893.54","3":"51651.62","4":"324523.76"},"real_total_amt":{"0":"66490.1","1":"36053.260000000000000002","2":"287632.160000000000000002","3":"50261.765238095238095238","4":"314120.994999999999999996"},"cost_amt":{"0":"982.05","1":"18570.67","2":"246783.22","3":"29390.14","4":"271886.71"},"preferential_amt":{"0":"0","1":"615.479999999999999998","2":"8261.379999999999999998","3":"1389.854761904761904762","4":"10402.765000000000000004"},"origin_pay_rate":{"0":"0.985230132004614221","1":"0.493555818934602062","2":"0.165972937428779283","3":"0.430992871085166351","4":"0.162197831061737976"},"real_pay_rate":{"0":"0.985230132004614221","1":"0.484910102442885886","2":"0.142017985749576821","3":"0.415258499959644606","4":"0.134452283267471504"},"refund_amt":{"0":"0","1":"242","2":"598.98","3":"984.54","4":"581.73"},"cust_cnt":{"0":"12","1":"135","2":"569","3":"162","4":"509"},"cust_unit_amt":{"0":"5540.841666666666666667","1":"271.620296296296296296","2":"520.023796133567662566","3":"318.837160493827160494","4":"637.571237721021611002"},"order_cnt":{"0":"20","1":"167","2":"839","3":"212","4":"816"},"point_cnt":{"0":"12","1":"135","2":"587","3":"176","4":"559"},"storage_amt":{"0":"254.553595178367623335","1":"686.075803416362311","2":"5367.920991344773371607","3":"1453.64208260217773217","4":"8645.292631309018545658"},"arterial_roads_amt":{"0":"211.969083798922020425","1":"571.301534220618476915","2":"4469.916418360009664216","3":"0","4":"0"},"deliver_amt":{"0":"738.190547443807600626","1":"1989.579729003623034952","2":"15566.657121693044728887","3":"4024.163557673164765907","4":"23933.038241474185208015"},"total_deliver_amt":{"0":"1204.713226421097244386","1":"3246.957066640603822867","2":"25404.49453139782776471","3":"5477.805640275342498077","4":"32578.330872783203753673"},"ds":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   area_no |   cust_cnt |   order_cnt |   point_cnt |
|:------|---------------:|----------:|-----------:|------------:|------------:|
| count |            131 |  131      |    131     |     131     |     131     |
| mean  |             38 |   62.3435 |    165.191 |     230.145 |     172.992 |
| std   |              0 |   44.1246 |    203.15  |     302.422 |     215.611 |
| min   |             38 |    1      |      1     |       1     |       1     |
| 25%   |             38 |   25.5    |     18.5   |      22.5   |      18.5   |
| 50%   |             38 |   52      |     83     |      94     |      84     |
| 75%   |             38 |   93.5    |    221     |     288.5   |     231.5   |
| max   |             38 |  155      |    880     |    1315     |     929     |