# app_kpi_cust_category_trade_wi
* comment: 交易口径kpi指标周汇总
* last_data_modified_time: 2025-09-18 02:52:05

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_cust_category_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标周汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"cust_team":{"0":"Mars大客户","1":"Mars大客户","2":"Mars大客户","3":"平台客户","4":"平台客户"},"category":{"0":"乳制品","1":"其他","2":"鲜果","3":"乳制品","4":"其他"},"origin_total_amt":{"0":"435","1":"803","2":"121427.99","3":"6468421.03","4":"1995062.99"},"real_total_amt":{"0":"385","1":"802","2":"121400.99","3":"6273761.03","4":"1907403.57"},"cust_cnt":{"0":"1","1":"5","2":"331","3":"7153","4":"6538"},"cust_arpu":{"0":"435","1":"160.6","2":"366.851933534743202417","3":"904.294845519362505243","4":"305.148820740287549709"},"order_cnt":{"0":"1","1":"5","2":"491","3":"8471","4":"7835"},"order_avg":{"0":"435","1":"160.6","2":"247.307515274949083503","3":"763.59591901782552237","4":"254.634714741544352265"},"after_sale_noreceived_amt":{"0":"0","1":"0","2":"1176.32","3":"353561.48","4":"79220.61"},"after_sale_rate":{"0":"0","1":"0","2":"0.00968738756196162","3":"0.054659626879606506","4":"0.039708325199296088"},"dire_origin_total_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"delivery_amt":{"0":"0","1":"0","2":"920","3":"13035.17","4":"4571.52"},"timing_origin_total_amt":{"0":"0","1":"0","2":"0","3":"537269","4":"152215"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   order_cnt |
|:------|---------------:|-----------:|------------:|
| count |              8 |       8    |        8    |
| mean  |             38 |    3282.12 |     4212    |
| std   |              0 |    4726.68 |     6267.98 |
| min   |             38 |       1    |        1    |
| 25%   |             38 |       4    |        4    |
| 50%   |             38 |     185.5  |      272    |
| 75%   |             38 |    6691.75 |     7994    |
| max   |             38 |   12188    |    16839    |