# app_saas_market_item_sales_summary_di
* comment: SAAS对账单-商品汇总表（近10天数据）
* last_data_modified_time: 2025-09-18 02:52:44

# schema:
CREATE TABLE summerfarm_tech.`app_saas_market_item_sales_summary_di` (
  `tenant_id` BIGINT COMMENT '租户Id',
  `time_tag` STRING COMMENT '时间标签',
  `pay_type` BIGINT COMMENT '支付方式 1、微信2、账期 3、余额支付',
  `item_id` BIGINT COMMENT '商品item_id',
  `item_code` STRING COMMENT '商品自有编码',
  `item_title` STRING COMMENT '商品标题',
  `item_specification` STRING COMMENT '商品规格',
  `total_amount` BIGINT COMMENT '商品数量',
  `average_payable_price` DECIMAL(38,18) COMMENT '平均售卖单价',
  `total_price` DECIMAL(38,18) COMMENT '售卖总价',
  `total_refund_price` DECIMAL(38,18) COMMENT '售后总金额',
  `total_price_deducted_refund` DECIMAL(38,18) COMMENT '扣除售后的售卖金额',
  `good_average_supply_price` DECIMAL(38,18) COMMENT '货品平均单价',
  `goods_total_supply_price` DECIMAL(38,18) COMMENT '货品总价',
  `sales_and_supply_difference_deducted_price` DECIMAL(38,18) COMMENT '销售与采购差额（剔除售后）',
  `goods_refund_price` DECIMAL(38,18) COMMENT '等比换算后采购售后金额',
  `goods_price_deducted_refund` DECIMAL(38,18) COMMENT '扣除售后的采购金额',
  `supplier_id` BIGINT COMMENT '供应商id'
)
COMMENT 'SAAS对账单-商品汇总表（近10天数据）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"8","1":"8","2":"8","3":"8","4":"8"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"pay_type":{"0":"1","1":"1","2":"1","3":"1","4":"2"},"item_id":{"0":"14475","1":"29965","2":"42019","3":"44104","4":"326"},"item_code":{"0":"","1":"None","2":"None","3":"None","4":""},"item_title":{"0":"澄善金枕榴莲碎果肉","1":"青凯特芒","2":"广东红心芭乐","3":"秘鲁蓝莓","4":"艾炒封口贴"},"item_specification":{"0":"1KG*1包","1":"净重12-12.5斤\/一级\/单果250-400g","2":"3斤*1盒\/一级\/熟果\/标准规格","3":"125g*2盒\/一级\/果径14-16mm","4":"0_1卷*1卷"},"total_amount":{"0":"1","1":"3","2":"1","3":"5","4":"2"},"average_payable_price":{"0":"85","1":"47.95","2":"37","3":"20.29","4":"9"},"total_price":{"0":"85","1":"143.85","2":"37","3":"101.45","4":"18"},"total_refund_price":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"total_price_deducted_refund":{"0":"85","1":"143.85","2":"37","3":"101.45","4":"18"},"good_average_supply_price":{"0":"85","1":"47.95","2":"37","3":"20.29","4":"7"},"goods_total_supply_price":{"0":"85","1":"143.85","2":"37","3":"101.45","4":"14"},"sales_and_supply_difference_deducted_price":{"0":"0","1":"0","2":"0","3":"0","4":"4"},"goods_refund_price":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"goods_price_deducted_refund":{"0":"85","1":"143.85","2":"37","3":"101.45","4":"14"},"supplier_id":{"0":"0","1":"0","2":"0","3":"0","4":"1830"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   pay_type |   item_id |   total_amount |   supplier_id |
|:------|------------:|-----------:|----------:|---------------:|--------------:|
| count |    335      | 335        |     335   |      335       |       335     |
| mean  |     73.7433 |   1.75522  |   32014.7 |        9.07761 |       647.534 |
| std   |     39.2517 |   0.908918 |   11970.7 |       23.0258  |      1247.5   |
| min   |      8      |   1        |     326   |        0       |         0     |
| 25%   |     35      |   1        |   27113.5 |        1       |         0     |
| 50%   |     95      |   1        |   33571   |        2       |         0     |
| 75%   |    107      |   2        |   41937.5 |        6.5     |         0     |
| max   |    123      |   4        |   44850   |      203       |      3404     |