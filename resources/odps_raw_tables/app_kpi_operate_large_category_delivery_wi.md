# app_kpi_operate_large_category_delivery_wi
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:42:43

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_large_category_delivery_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` STRING COMMENT '周数',
  `monday` STRING COMMENT '周一',
  `sunday` STRING COMMENT '周天',
  `large_area_name` STRING COMMENT '运营服务大区',
  `category` STRING COMMENT '鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"large_area_name":{"0":"上海大区","1":"上海大区","2":"上海大区","3":"南宁大区","4":"南宁大区"},"category":{"0":"乳制品","1":"其他","2":"鲜果","3":"乳制品","4":"其他"},"origin_total_amt":{"0":"513259.08","1":"192896.88","2":"271202.84","3":"141820.06","4":"30770.4"},"real_total_amt":{"0":"498738.615000000000000011","1":"186274.619999999999999995","2":"265213.449999999999999996","3":"137314","4":"29624.660000000000000001"},"marketing_amt":{"0":"14520.464999999999999989","1":"6622.260000000000000005","2":"5989.390000000000000004","3":"4506.06","4":"1145.739999999999999999"},"cost_amt":{"0":"472555.55","1":"160738.18","2":"200328.69","3":"133181.37","4":"26293.34"},"origin_gross":{"0":"40703.53","1":"32158.7","2":"70874.15","3":"8638.69","4":"4477.06"},"real_gross":{"0":"26183.065000000000000011","1":"25536.439999999999999995","2":"64884.759999999999999996","3":"4132.63","4":"3331.320000000000000001"},"origin_gross_margin":{"0":"0.079304062190190576","1":"0.166714464225652587","2":"0.261332624687853564","3":"0.060913033036370172","4":"0.145498921041000442"},"real_gross_margin":{"0":"0.052498571822035476","1":"0.137090281005539026","2":"0.244651091413350266","3":"0.030096202863509912","4":"0.112450910829018797"},"cust_cnt":{"0":"541","1":"503","2":"965","3":"143","4":"96"},"point_cnt":{"0":"662","1":"676","2":"1378","3":"156","4":"107"},"origin_pre_cust_price":{"0":"948.72288354898336414","1":"383.492803180914512922","2":"281.039212435233160622","3":"991.748671328671328671","4":"320.525"},"real_pre_cust_price":{"0":"921.882837338262476895","1":"370.327276341948310139","2":"274.832590673575129534","3":"960.237762237762237762","4":"308.590208333333333333"},"timing_origin_amt":{"0":"30611","1":"30665","2":"0","3":"9919","4":"3343"},"timing_real_amt":{"0":"28411.625","1":"28171.5","2":"0","3":"9328","4":"3064"},"consign_origin_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_marketing_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_origin_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_cust_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"storage_amt":{"0":"10601.041870754947573924","1":"9285.50810373032327682","2":"11716.525374847763071999","3":"2193.979100042789188996","4":"1506.104893904338675669"},"arterial_roads_amt":{"0":"1473.081172008298422964","1":"1338.910133212523398553","2":"1356.862199148349014553","3":"1729.690384294934603513","4":"560.352817269410698067"},"self_picked_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_amt":{"0":"179.913218241890421191","1":"157.83082311990781242","2":"199.464002753117860128","3":"1265.030452463672515347","4":"868.40779631692265254"},"other_amt":{"0":"45.385575749979113002","1":"43.82496544330197741","2":"58.379245548966902943","3":"238.477318445239737795","4":"163.707966216579341726"},"deliver_amt":{"0":"21857.906118821306641205","1":"19446.880840998642165547","2":"24772.574612644973527392","3":"3721.083028571218791643","4":"2566.216975720602813434"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |     43     |      43     |                 43 |
| mean  |    618.14  |     734.023 |                  0 |
| std   |    624.103 |     766.843 |                  0 |
| min   |      1     |       1     |                  0 |
| 25%   |    210     |     229.5   |                  0 |
| 50%   |    503     |     588     |                  0 |
| 75%   |    755     |     878     |                  0 |
| max   |   2632     |    3448     |                  0 |