# app_kpi_operate_large_area_delivery_wi
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:39:53

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_large_area_delivery_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` STRING COMMENT '周数',
  `monday` STRING COMMENT '周一',
  `sunday` STRING COMMENT '周天',
  `large_area_name` STRING COMMENT '运营服务大区',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"large_area_name":{"0":"南宁大区","1":"昆明快递大区","2":"杭州大区","3":"武汉大区","4":"贵阳大区"},"origin_total_amt":{"0":"184933.72","1":"788","2":"2216196.84","3":"728853.44","4":"84009.31"},"real_total_amt":{"0":"178824.06","1":"788","2":"2151368.576666666666666701","3":"705354.566666666666666666","4":"82602.3"},"marketing_amt":{"0":"6109.66","1":"0","2":"64828.263333333333333299","3":"23498.873333333333333334","4":"1407.01"},"cost_amt":{"0":"169759.65","1":"746.17","2":"1883877.09","3":"650397.84","4":"77140.17"},"origin_gross":{"0":"15174.07","1":"41.83","2":"332319.75","3":"78455.6","4":"6869.14"},"real_gross":{"0":"9064.41","1":"41.83","2":"267491.486666666666666701","3":"54956.726666666666666666","4":"5462.13"},"origin_gross_margin":{"0":"0.082051396576027346","1":"0.053083756345177665","2":"0.149950466493761448","3":"0.10764249119823047","4":"0.081766413746285977"},"real_gross_margin":{"0":"0.050688984468868451","1":"0.053083756345177665","2":"0.124335499536354822","3":"0.077913618573958809","4":"0.066125640569330394"},"cust_cnt":{"0":"228","1":"1","2":"3735","3":"986","4":"106"},"point_cnt":{"0":"259","1":"1","2":"4892","3":"1240","4":"126"},"origin_pre_cust_price":{"0":"811.112807017543859649","1":"788","2":"593.359261044176706827","3":"739.202271805273833671","4":"792.540660377358490566"},"real_pre_cust_price":{"0":"784.316052631578947368","1":"788","2":"576.002296296296296296","3":"715.369743069641649763","4":"779.266981132075471698"},"timing_origin_amt":{"0":"13262","1":"0","2":"102529","3":"62420","4":"870"},"timing_real_amt":{"0":"12392","1":"0","2":"95275.866666666666666667","3":"58904.666666666666666667","4":"740"},"consign_origin_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_marketing_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_origin_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_cust_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"storage_amt":{"0":"3900.875503009427737826","1":"0","2":"37237.590387343151049075","3":"19891.090637428111446175","4":"1965.463333225022474046"},"arterial_roads_amt":{"0":"2419.834916204288511158","1":"0","2":"30641.339576485331503757","3":"32921.519831984805485906","4":"0"},"self_picked_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_amt":{"0":"2249.212995026041039466","1":"0","2":"83.718581671934392524","3":"2649.727453363761921699","4":"0"},"other_amt":{"0":"424.010570348248664421","1":"0","2":"12377.138879907874386006","3":"2502.136372942361014109","4":"598.504569044670283774"},"deliver_amt":{"0":"6633.292577604854606641","1":"0","2":"108850.975771057445921883","3":"73877.144740332650525165","4":"3282.672838935294946345"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |      15    |       15    |                 15 |
| mean  |    1214.07 |     1527.93 |                  0 |
| std   |    1189.49 |     1502.34 |                  0 |
| min   |       1    |        1    |                  0 |
| 25%   |     377    |      433.5  |                  0 |
| 50%   |     811    |      991    |                  0 |
| 75%   |    1476    |     1992.5  |                  0 |
| max   |    3735    |     4892    |                  0 |