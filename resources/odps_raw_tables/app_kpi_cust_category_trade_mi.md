# app_kpi_cust_category_trade_mi
* comment: 交易口径kpi指标月汇总
* last_data_modified_time: 2025-09-18 02:52:01

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_cust_category_trade_mi` (
  `month` STRING COMMENT '月份',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标月汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"cust_team":{"0":"Mars大客户","1":"Mars大客户","2":"Mars大客户","3":"平台客户","4":"平台客户"},"category":{"0":"乳制品","1":"其他","2":"鲜果","3":"乳制品","4":"其他"},"origin_total_amt":{"0":"3181","1":"292894.3","2":"714509.21","3":"38969416.16","4":"11449420.83"},"real_total_amt":{"0":"3011","1":"292611","2":"714262.98","3":"37840704.83","4":"10943032.54"},"cust_cnt":{"0":"3","1":"11","2":"619","3":"23679","4":"22400"},"cust_arpu":{"0":"1060.333333333333333333","1":"26626.754545454545454545","2":"1154.295977382875605816","3":"1645.737411208243591368","4":"511.134858482142857143"},"order_cnt":{"0":"5","1":"32","2":"2899","3":"50405","4":"45954"},"order_avg":{"0":"636.2","1":"9152.946875","2":"246.467474991376336668","3":"773.126002579109215356","4":"249.149602428515471994"},"after_sale_noreceived_amt":{"0":"0","1":"0","2":"13068.99","3":"1658377.2","4":"401506.46"},"after_sale_rate":{"0":"0","1":"0","2":"0.01829086289874416","3":"0.042555864660406039","4":"0.035067840195721062"},"dire_origin_total_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"delivery_amt":{"0":"0","1":"0","2":"5070","3":"76730.2","4":"29512.29"},"timing_origin_total_amt":{"0":"0","1":"0","2":"0","3":"4243923","4":"1134577"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |
|:------|-----------:|------------:|
| count |       9    |         9   |
| mean  |    8520.78 |     22263   |
| std   |   12743    |     35945.9 |
| min   |       1    |         2   |
| 25%   |       7    |         7   |
| 50%   |     140    |       382   |
| 75%   |   22400    |     45954   |
| max   |   29827    |    100681   |