# app_warehouse_sku_unsalable_sale_storage_di
* comment: 每日sku、库存仓维度滞销情况表
* last_data_modified_time: 2025-09-18 02:19:57

# schema:
CREATE TABLE summerfarm_tech.`app_warehouse_sku_unsalable_sale_storage_di` (
  `warehouse_no` BIGINT COMMENT '库存仓',
  `warehouse_name` STRING COMMENT '库存仓名',
  `sku_id` STRING COMMENT 'sku编号',
  `category1` STRING COMMENT '类目一级',
  `category4_id` STRING COMMENT '类目id',
  `category4` STRING COMMENT '类目',
  `sku_brand` STRING COMMENT '品牌',
  `spu_name` STRING COMMENT 'spu名称',
  `disc` STRING COMMENT 'sku描述',
  `sku_origin` STRING COMMENT '进口，国产',
  `sku_property` STRING COMMENT 'sku性质：常规，活动，临保，拆包，不卖，破袋',
  `store_method` STRING COMMENT '存储性质：冷冻,冷藏,常温',
  `warn_days` BIGINT COMMENT '预警天数',
  `purchase_in_quality` BIGINT COMMENT '历史两周采购入库数量',
  `sale_out_quality` BIGINT COMMENT '历史两周销售出库数量',
  `allocate_in_quality` BIGINT COMMENT '历史两周调拨入库数量',
  `allocate_out_quality` BIGINT COMMENT '历史两周调拨出库数量',
  `purchase_on_way_quality` BIGINT COMMENT '采购在途数量',
  `allocate_on_way_quality` BIGINT COMMENT '调拨在途数量',
  `lately_arrived_time` STRING COMMENT '八周内最近入库时间',
  `enable_quality` BIGINT COMMENT '在库库存',
  `safe_quality` BIGINT COMMENT '安全库存',
  `avg_quality` BIGINT COMMENT '过去2周日均出库量',
  `doc_quality` BIGINT COMMENT 'DOC（现货）',
  `doc_on_way_quality` BIGINT COMMENT 'DOC（现货+在途)',
  `use_days` BIGINT COMMENT '库存可用天数',
  `duration_on_shelf` DECIMAL(38,18) COMMENT '上架时长',
  `duration_sale_out` DECIMAL(38,18) COMMENT '售罄时长',
  `sale_out_rask` DECIMAL(38,18) COMMENT '售罄率',
  `is_sale_out_rask` STRING COMMENT '售罄风险判定：是，否',
  `unsalable_decided` STRING COMMENT '动销判定：两周低动销，两周无动销',
  `last_sale_time` DATETIME COMMENT '最后一次销售距今时间（天）',
  `last_sale_day` BIGINT COMMENT '最后一次销售距今时间（天）',
  `storage_amt` DECIMAL(38,18) COMMENT '在库库存成本'
)
COMMENT '每日sku、库存仓维度滞销情况表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"warehouse_no":{"0":"2","1":"2","2":"2","3":"2","4":"2","5":"2","6":"2","7":"2","8":"2","9":"2"},"warehouse_name":{"0":"上海总仓","1":"上海总仓","2":"上海总仓","3":"上海总仓","4":"上海总仓","5":"上海总仓","6":"上海总仓","7":"上海总仓","8":"上海总仓","9":"上海总仓"},"sku_id":{"0":"1233884862","1":"1235674425","2":"15126316068","3":"591137773277","4":"591137773388","5":"605113606670","6":"782026342286","7":"782864856548","8":"784776715513","9":"99351"},"category1":{"0":"乳制品","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品","5":"其他","6":"其他","7":"其他","8":"其他","9":"乳制品"},"category4_id":{"0":"591","1":"590","2":"605","3":"591","4":"591","5":"763","6":"782","7":"782","8":"784","9":"581"},"category4":{"0":"马苏里拉","1":"切达再制干酪","2":"搅打型稀奶油","3":"马苏里拉","4":"马苏里拉","5":"植脂奶油","6":"冷冻果泥","7":"冷冻果泥","8":"果泥丨果茸","9":"无盐黄油"},"sku_brand":{"0":"安佳","1":"西诺迪斯","2":"西诺迪斯","3":"安佳","4":"安佳","5":"立高","6":"安德鲁","7":"安德鲁","8":"安德鲁","9":"安佳"},"spu_name":{"0":"安佳碎条状马苏里拉干酪","1":"百瑞酪车达芝士片(黄色)","2":"爱乐薇（粉塔）马斯卡波尼稀奶油","3":"安佳芝易马苏里拉干酪碎","4":"安佳芝易马苏里拉干酪碎","5":"立高美蒂雅乳脂植脂奶油","6":"安德鲁果溶（威廉梨）","7":"安德鲁果溶（香蕉）","8":"安德鲁果溶（芒果）","9":"安佳无盐黄油5KG"},"disc":{"0":"12KG*1箱\/红标(产品适用于堂食，胶质感较强，拉丝效果较好)","1":"960g*12包","2":"1L*6盒","3":"1KG*1包","4":"1KG*12包","5":"907g*12盒","6":"1KG*8盒","7":"1KG*8盒","8":"1KG*8盒\/芒果\/12*15","9":"5KG*4盒"},"sku_origin":{"0":"进口","1":"国产","2":"进口","3":"进口","4":"进口","5":"国产","6":"国产","7":"国产","8":"国产","9":"进口"},"sku_property":{"0":"常规","1":"常规","2":"常规","3":"拆包","4":"常规","5":"常规","6":"常规","7":"常规","8":"常规","9":"常规"},"store_method":{"0":"冷冻","1":"冷藏","2":"冷藏","3":"冷冻","4":"冷冻","5":"冷冻","6":"冷冻","7":"冷冻","8":"冷冻","9":"冷冻"},"warn_days":{"0":"45","1":"30","2":"30","3":"45","4":"45","5":"45","6":"5","7":"5","8":"45","9":"45"},"purchase_in_quality":{"0":"11","1":"0","2":"0","3":"0","4":"2","5":"0","6":"70","7":"45","8":"80","9":"0"},"sale_out_quality":{"0":"10","1":"0","2":"3","3":"8","4":"1","5":"0","6":"70","7":"45","8":"80","9":"13"},"allocate_in_quality":{"0":"3","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"allocate_out_quality":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"purchase_on_way_quality":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"allocate_on_way_quality":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"lately_arrived_time":{"0":"20250905","1":"无","2":"20250829","3":"20250905","4":"20250905","5":"无","6":"20250917","7":"20250917","8":"20250917","9":"20250902"},"enable_quality":{"0":"8","1":"2","2":"0","3":"2","4":"0","5":"0","6":"0","7":"0","8":"0","9":"49"},"safe_quality":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"avg_quality":{"0":"1","1":"0","2":"1","3":"1","4":"1","5":"0","6":"5","7":"4","8":"6","9":"1"},"doc_quality":{"0":"12","1":"0","2":"0","3":"4","4":"0","5":"0","6":"0","7":"0","8":"0","9":"53"},"doc_on_way_quality":{"0":"12","1":"0","2":"0","3":"4","4":"0","5":"0","6":"0","7":"0","8":"0","9":"53"},"use_days":{"0":"12","1":"0","2":"0","3":"4","4":"0","5":"0","6":"0","7":"0","8":"0","9":"53"},"duration_on_shelf":{"0":"336","1":"336","2":"336","3":"336","4":"336","5":"-1","6":"336","7":"336","8":"336","9":"336"},"duration_sale_out":{"0":"0","1":"0","2":"297.302222222222222","3":"41.47111111111111","4":"280.8647222222222178","5":"-1","6":"335.96277777777778","7":"335.96277777777778","8":"335.96277777777778","9":"0"},"sale_out_rask":{"0":"0","1":"0","2":"0.88","3":"0.12","4":"0.84","5":"-1","6":"1","7":"1","8":"1","9":"0"},"is_sale_out_rask":{"0":"否","1":"否","2":"否","3":"是","4":"否","5":"否","6":"否","7":"否","8":"否","9":"否"},"unsalable_decided":{"0":"-","1":"两周无动销","2":"-","3":"-","4":"-","5":"-","6":"-","7":"-","8":"-","9":"-"},"last_sale_time":{"0":"2025-09-16 01:59:45","1":"2025-08-16 02:56:31","2":"2025-09-06 02:12:03","3":"2025-09-15 03:29:13","4":"2025-09-09 02:38:26","5":"2025-07-20 02:15:59","6":"2025-09-17 13:55:58","7":"2025-09-17 13:55:58","8":"2025-09-17 13:55:58","9":"2025-09-16 01:59:34"},"last_sale_day":{"0":"1.0","1":"32.0","2":"11.0","3":"2.0","4":"8.0","5":"59.0","6":"0.0","7":"0.0","8":"0.0","9":"1.0"},"storage_amt":{"0":"4224","1":"1197.6","2":"0","3":"88","4":"0","5":"0","6":"0","7":"0","8":"0","9":"74970"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   warn_days |   purchase_in_quality |   sale_out_quality |   allocate_in_quality |   allocate_out_quality |   purchase_on_way_quality |   allocate_on_way_quality |   enable_quality |   safe_quality |   avg_quality |   doc_quality |   doc_on_way_quality |   use_days | last_sale_time                |   last_sale_day |
|:------|---------------:|------------:|----------------------:|-------------------:|----------------------:|-----------------------:|--------------------------:|--------------------------:|-----------------:|---------------:|--------------:|--------------:|---------------------:|-----------:|:------------------------------|----------------:|
| count |      6995      |  6995       |             6995      |          6995      |            6995       |             6995       |                 6995      |                 6995      |         6995     |  6995          |    6995       |     6995      |            6995      |  6995      | 4079                          |       4079      |
| mean  |        67.1271 |    39.2204  |               23.6253 |            28.6375 |               5.16426 |                5.22959 |                    1.996  |                    1.0822 |          300.084 |     0.00171551 |       2.81573 |       24.0355 |              25.7038 |    24.0359 | 2025-08-16 05:33:22.079676416 |         32.3719 |
| min   |         2      |     0       |                0      |             0      |               0       |                0       |                    0      |                    0      |          -71     |    -2          |       0       |      -10      |             -10      |   -10      | 2021-03-14 23:16:24           |         -1      |
| 25%   |        29      |    30       |                0      |             0      |               0       |                0       |                    0      |                    0      |            2     |     0          |       0       |        0      |               0      |     0      | 2025-09-01 08:29:05.500000    |          0      |
| 50%   |        59      |    45       |                0      |             3      |               0       |                0       |                    0      |                    0      |            6     |     0          |       1       |        6      |               7      |     6      | 2025-09-14 10:43:06           |          3      |
| 75%   |       117      |    45       |                0      |            14      |               0       |                0       |                    0      |                    0      |           17     |     0          |       2       |       25      |              28      |    25      | 2025-09-17 08:14:48           |         16      |
| max   |       170      |    45       |             5700      |          5209      |            2927       |             2017       |                 1000      |                  500      |       500560     |     4          |     401       |     8666      |            8666      |  8666      | 2025-09-18 00:29:05           |       1648      |
| std   |        45.69   |     9.57246 |              167.836  |           151.907  |              50.636   |               47.7264  |                   26.1816 |                   10.2258 |         8095.07  |     0.0756058  |      12.5616  |      131.771  |             132.344  |   131.771  | nan                           |        114.783  |