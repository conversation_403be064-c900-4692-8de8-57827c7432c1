# app_saas_brand_sku_trade_mi
* comment: saas品牌品类结构数据表
* last_data_modified_time: 2025-09-18 02:23:02

# schema:
CREATE TABLE summerfarm_tech.`app_saas_brand_sku_trade_mi` (
  `month` STRING COMMENT '月份',
  `brand_alias` STRING COMMENT '品牌名称',
  `title` STRING COMMENT '商品标题',
  `specification` STRING COMMENT '商品规格',
  `category1` STRING COMMENT '后台一级类目',
  `sku_type` STRING COMMENT '商品类型：商城下单、鲜沐自营、代仓、代售、客户自营',
  `total_gmv` DECIMAL(38,18) COMMENT '总交易GMV',
  `sku_cnt` BIGINT COMMENT '商品销量'
)
COMMENT 'saas品牌品类结构数据表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"brand_alias":{"0":"新加坡斯味洛鲜奶茶 ","1":"蔡小甜","2":"蔡小甜","3":"蔡小甜","4":"蔡小甜"},"title":{"0":"500ml易拉罐","1":"金枕头榴莲果肉","2":"真味珍冷冻芋头块","3":"蛋糕蓝色勺叉","4":"五盘五叉"},"specification":{"0":"0_200个*1箱","1":"3KG*1袋","2":"2.5KG*1包","3":"100个*1包","4":"300个*1箱"},"category1":{"0":"无","1":"水果制品","2":"蔬菜制品","3":"包材","4":"包材"},"sku_type":{"0":"客户自营","1":"代售","2":"代仓","3":"代仓","4":"代仓"},"total_gmv":{"0":"686434","1":"3223","2":"1138.5","3":"561.26","4":"396"},"sku_cnt":{"0":"4573","1":"11","2":"23","3":"133","4":"3"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   sku_cnt |
|:------|----------:|
| count | 2175      |
| mean  |   45.4754 |
| std   |  197.237  |
| min   |    1      |
| 25%   |    2      |
| 50%   |    7      |
| 75%   |   24      |
| max   | 4573      |