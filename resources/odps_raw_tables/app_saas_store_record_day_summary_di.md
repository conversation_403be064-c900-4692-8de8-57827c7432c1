# app_saas_store_record_day_summary_di
* comment: 出入库汇总天表（含自营仓）
* last_data_modified_time: 2025-09-18 01:38:07

# schema:
CREATE TABLE summerfarm_tech.`app_saas_store_record_day_summary_di` (
  `day_tag` STRING COMMENT '日期',
  `warehouse_no` BIGINT COMMENT '库存仓ID',
  `warehouse_name` STRING COMMENT '仓库名称',
  `warehouse_provider` STRING COMMENT '仓库服务商',
  `pd_id` BIGINT COMMENT '货品编码',
  `sku` STRING COMMENT 'sku编号',
  `saas_sku_id` BIGINT COMMENT 'saas skuId',
  `category_id` BIGINT COMMENT '类目id',
  `sku_tenant_id` BIGINT COMMENT 'sku租户id',
  `warehouse_tenant_id` BIGINT COMMENT '仓库租户id',
  `opening_quantity` BIGINT COMMENT '期初库存',
  `opening_amount` DECIMAL(38,18) COMMENT '期初金额',
  `ending_quantity` BIGINT COMMENT '期末库存',
  `ending_amount` DECIMAL(38,18) COMMENT '期末金额',
  `allocation_in_quantity` BIGINT COMMENT '调拨入库数量',
  `allocation_in_amount` DECIMAL(38,18) COMMENT '调拨入库金额',
  `purchase_in_quantity` BIGINT COMMENT '采购入库数量',
  `purchase_in_amount` DECIMAL(38,18) COMMENT '采购入库金额',
  `after_sale_in_quantity` BIGINT COMMENT '退货入库数量',
  `after_sale_in_amount` DECIMAL(38,18) COMMENT '退货入库金额',
  `stock_taking_in_quantity` BIGINT COMMENT '盘盈入库数量',
  `stock_taking_in_amount` DECIMAL(38,18) COMMENT '盘盈入库金额',
  `transfer_in_quantity` BIGINT COMMENT '转换入库数量',
  `transfer_in_amount` DECIMAL(38,18) COMMENT '转换入库金额',
  `allocation_abnormal_in_quantity` BIGINT COMMENT '调拨回库数量',
  `allocation_abnormal_in_amount` DECIMAL(38,18) COMMENT '调拨回库金额',
  `other_in_quantity` BIGINT COMMENT '其他入库数量',
  `other_in_amount` DECIMAL(38,18) COMMENT '其他入库金额',
  `in_quantity` BIGINT COMMENT '入库合计数量',
  `in_amount` DECIMAL(38,18) COMMENT '入库合计金额',
  `allocation_out_quantity` BIGINT COMMENT '调拨出库数量',
  `allocation_out_amount` DECIMAL(38,18) COMMENT '调拨出库金额',
  `sale_out_quantity` BIGINT COMMENT '销售出库数量',
  `sale_out_amount` DECIMAL(38,18) COMMENT '销售出库金额',
  `damage_out_quantity` BIGINT COMMENT '货损出库数量',
  `damage_out_amount` DECIMAL(38,18) COMMENT '货损出库金额',
  `stock_taking_out_quantity` BIGINT COMMENT '盘亏出库数量',
  `stock_taking_out_amount` DECIMAL(38,18) COMMENT '盘亏出库金额',
  `transfer_out_quantity` BIGINT COMMENT '转换出库数量',
  `transfer_out_amount` DECIMAL(38,18) COMMENT '转换出库金额',
  `purchase_back_out_quantity` BIGINT COMMENT '采购退货出库数量',
  `purchase_back_out_amount` DECIMAL(38,18) COMMENT '采购退货出库金额',
  `supply_again_out_quantity` BIGINT COMMENT '补货出库数量',
  `supply_again_out_amount` DECIMAL(38,18) COMMENT '补货出库金额',
  `own_self_out_quantity` BIGINT COMMENT '自提销售出库数量',
  `own_self_out_amount` DECIMAL(38,18) COMMENT '自提销售出库金额',
  `other_out_quantity` BIGINT COMMENT '其他出库数量',
  `other_out_amount` DECIMAL(38,18) COMMENT '其他出库金额',
  `out_quantity` BIGINT COMMENT '出库合计数量',
  `out_amount` DECIMAL(38,18) COMMENT '出库合计金额',
  `init_in_quantity` BIGINT COMMENT '期初入库数量',
  `init_in_amount` DECIMAL(38,18) COMMENT '期初入库金额',
  `lack_in_quantity` BIGINT COMMENT '缺货入库数量',
  `lack_in_amount` DECIMAL(38,18) COMMENT '缺货入库金额',
  `intercept_in_quantity` BIGINT COMMENT '拦截入库数量',
  `intercept_in_amount` DECIMAL(38,18) COMMENT '拦截入库金额',
  `out_more_in_quantity` BIGINT COMMENT '多出入库数量',
  `out_more_in_amount` DECIMAL(38,18) COMMENT '多出入库金额',
  `allocation_damage_out_quantity` BIGINT COMMENT '调拨货损出库数量',
  `allocation_damage_out_amount` DECIMAL(38,18) COMMENT '调拨货损出金额'
)
COMMENT '出入库汇总天表（含自营仓）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"day_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"warehouse_no":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"warehouse_name":{"0":"杭州总仓","1":"杭州总仓","2":"杭州总仓","3":"杭州总仓","4":"杭州总仓"},"warehouse_provider":{"0":"杭州鲜沐科技有限公司","1":"杭州鲜沐科技有限公司","2":"杭州鲜沐科技有限公司","3":"杭州鲜沐科技有限公司","4":"杭州鲜沐科技有限公司"},"pd_id":{"0":"5702","1":"6515","2":"6584","3":"6895","4":"4178"},"sku":{"0":"1038158531770","1":"1078264105168","2":"613161203823","3":"614680280306","4":"782081647574"},"saas_sku_id":{"0":"102164.0","1":"104334.0","2":"104550.0","3":"106583.0","4":"100240.0"},"category_id":{"0":"699.0","1":"1078.0","2":"613.0","3":"614.0","4":"782.0"},"sku_tenant_id":{"0":"4","1":"38","2":"38","3":"38","4":"4"},"warehouse_tenant_id":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"opening_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"opening_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ending_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ending_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"after_sale_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"after_sale_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_taking_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_taking_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"transfer_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"transfer_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_abnormal_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_abnormal_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"sale_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"sale_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"damage_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"damage_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_taking_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_taking_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"transfer_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"transfer_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_back_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_back_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"supply_again_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"supply_again_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"own_self_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"own_self_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"init_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"init_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"lack_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"lack_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"intercept_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"intercept_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"out_more_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"out_more_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_damage_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_damage_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |    pd_id |   saas_sku_id |   category_id |   sku_tenant_id |   warehouse_tenant_id |   opening_quantity |   ending_quantity |   allocation_in_quantity |   purchase_in_quantity |   after_sale_in_quantity |   stock_taking_in_quantity |   transfer_in_quantity |   allocation_abnormal_in_quantity |   other_in_quantity |   in_quantity |   allocation_out_quantity |   sale_out_quantity |   damage_out_quantity |   stock_taking_out_quantity |   transfer_out_quantity |   purchase_back_out_quantity |   supply_again_out_quantity |   own_self_out_quantity |   other_out_quantity |   out_quantity |   init_in_quantity |   lack_in_quantity |   intercept_in_quantity |   out_more_in_quantity |   allocation_damage_out_quantity |
|:------|---------------:|---------:|--------------:|--------------:|----------------:|----------------------:|-------------------:|------------------:|-------------------------:|-----------------------:|-------------------------:|---------------------------:|-----------------------:|----------------------------------:|--------------------:|--------------:|--------------------------:|--------------------:|----------------------:|----------------------------:|------------------------:|-----------------------------:|----------------------------:|------------------------:|---------------------:|---------------:|-------------------:|-------------------:|------------------------:|-----------------------:|---------------------------------:|
| count |      5939      |  5939    |        5938   |      5903     |       5939      |             5939      |     5939           |    5939           |            5939          |            5939        |                     5939 |                       5939 |                   5939 |                              5939 |         5939        |   5939        |              5939         |         5939        |        5939           |              5939           |                    5939 |                         5939 |                        5939 |           5939          |                 5939 |    5939        |               5939 |               5939 |                    5939 |                   5939 |                             5939 |
| mean  |        68.7326 | 10885.3  |      113424   |       795.593 |         57.3418 |               15.4696 |      586.226       |     586.106       |               0.00623001 |               0.243475 |                        0 |                          0 |                      0 |                                 0 |            0.148173 |      0.397878 |                 0.0281192 |            0.487793 |           0.000168379 |                 0.000505136 |                       0 |                            0 |                           0 |              0.00286243 |                    0 |       0.519448 |                  0 |                  0 |                       0 |                      0 |                                0 |
| std   |        56.7128 |  4388.36 |        7932.9 |       184.306 |         35.2605 |               30.5544 |    19169.4         |   19169.4         |               0.181109   |               5.38404  |                        0 |                          0 |                      0 |                                 0 |           10.1654   |     11.5013   |                 0.557309  |            4.23016  |           0.0129761   |                 0.0389282   |                       0 |                            0 |                           0 |              0.0721971  |                    0 |       4.29524  |                  0 |                  0 |                       0 |                      0 |                                0 |
| min   |         1      |  1055    |      100039   |       526     |          2      |                1      |        0           |       0           |               0          |               0        |                        0 |                          0 |                      0 |                                 0 |            0        |      0        |                 0         |            0        |           0           |                 0           |                       0 |                            0 |                           0 |              0          |                    0 |       0        |                  0 |                  0 |                       0 |                      0 |                                0 |
| 25%   |        10      |  6640    |      106123   |       629     |         32      |                1      |        0           |       0           |               0          |               0        |                        0 |                          0 |                      0 |                                 0 |            0        |      0        |                 0         |            0        |           0           |                 0           |                       0 |                            0 |                           0 |              0          |                    0 |       0        |                  0 |                  0 |                       0 |                      0 |                                0 |
| 50%   |        66      | 10865    |      115045   |       766     |         60      |                1      |        0           |       0           |               0          |               0        |                        0 |                          0 |                      0 |                                 0 |            0        |      0        |                 0         |            0        |           0           |                 0           |                       0 |                            0 |                           0 |              0          |                    0 |       0        |                  0 |                  0 |                       0 |                      0 |                                0 |
| 75%   |       119      | 14933    |      120367   |       926     |         85      |                2      |       15           |      15           |               0          |               0        |                        0 |                          0 |                      0 |                                 0 |            0        |      0        |                 0         |            0        |           0           |                 0           |                       0 |                            0 |                           0 |              0          |                    0 |       0        |                  0 |                  0 |                       0 |                      0 |                                0 |
| max   |       176      | 18910    |      126024   |      1385     |        118      |              109      |        1.06008e+06 |       1.06008e+06 |               8          |             200        |                        0 |                          0 |                      0 |                                 0 |          777        |    777        |                30         |          250        |           1           |                 3           |                       0 |                            0 |                           0 |              3          |                    0 |     250        |                  0 |                  0 |                       0 |                      0 |                                0 |