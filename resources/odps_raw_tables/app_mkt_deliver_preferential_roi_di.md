# app_mkt_deliver_preferential_roi_di
* comment: 履约维度营销活动ROI拆解报表
* last_data_modified_time: 2025-09-18 03:37:53

# schema:
CREATE TABLE summerfarm_tech.`app_mkt_deliver_preferential_roi_di` (
  `date` STRING COMMENT '日期',
  `city_id` BIGINT COMMENT '运营服务区ID',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细）',
  `preferential_type` STRING COMMENT '营销活动类型',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付金额',
  `cost_amt` DECIMAL(38,18) COMMENT '履约成本金额'
)
COMMENT '履约维度营销活动ROI拆解报表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"city_id":{"0":"19415","1":"44122","2":"44122","3":"2750","4":"2750"},"city_name":{"0":"义乌","1":"长沙普冷","2":"长沙普冷","3":"上海","4":"上海"},"large_area_id":{"0":"1","1":"50","2":"50","3":"2","4":"2"},"large_area_name":{"0":"杭州大区","1":"长沙大区","2":"长沙大区","3":"上海大区","4":"上海大区"},"cust_team":{"0":"平台客户","1":"平台客户","2":"平台客户","3":"平台客户","4":"平台客户"},"life_cycle_detail":{"0":"S2","1":"S2","2":"S2","3":"W","4":"A2"},"preferential_type":{"0":"行业活动券","1":"行业活动券","2":"临保活动","3":"行业活动券","4":"行业活动券"},"preferential_amt":{"0":"23.94","1":"30","2":"118","3":"41.61","4":"67.1"},"origin_total_amt":{"0":"609","1":"740","2":"1545","3":"1650.5","4":"2817.5"},"real_total_amt":{"0":"585.06","1":"684.999999999999999998","2":"1404.049999999999999999","3":"1590.89","4":"2658.4"},"cost_amt":{"0":"427.32","1":"555.71","2":"1265.24","3":"1191.16","4":"2089.27"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   city_id |   large_area_id |
|:------|----------:|----------------:|
| count |    2205   |       2205      |
| mean  |   27598.4 |         36.8558 |
| std   |   15200.1 |         32.8777 |
| min   |    1001   |          1      |
| 25%   |   14400   |          1      |
| 50%   |   25624   |         29      |
| 75%   |   44133   |         72      |
| max   |   44264   |         91      |