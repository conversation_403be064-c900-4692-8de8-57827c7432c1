# app_saas_merchant_store_item_order_analysis_quarter_df
* comment: saas-门店商品订货分析-季
* last_data_modified_time: 2025-09-18 02:20:11

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_item_order_analysis_quarter_df` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd 月初日期',
  `item_id` BIGINT COMMENT '商品id',
  `store_id` BIGINT COMMENT '门店id',
  `average_order_period` DECIMAL(38,18) COMMENT '平均订货周期',
  `average_order_period_last_period` DECIMAL(38,18) COMMENT '上周期平均订货周期',
  `average_order_period_upper_period` DECIMAL(38,18) COMMENT '平均订货周期环比',
  `order_amount` BIGINT COMMENT '订货数量',
  `order_amount_last_period` BIGINT COMMENT '上周期订货数量',
  `order_amount_upper_period` DECIMAL(38,18) COMMENT '订货数量环比',
  `order_price` DECIMAL(38,18) COMMENT '订货金额',
  `order_price_last_period` DECIMAL(38,18) COMMENT '上周期订货金额',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '订货金额环比',
  `last_order_time` STRING COMMENT '最后订货日期 yyyy-MM-dd',
  `last_order_amount` BIGINT COMMENT '最后订货数量',
  `last_order_price` DECIMAL(38,18) COMMENT '最后订货金额'
)
COMMENT 'saas-门店商品订货分析-季'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"time_tag":{"0":"20220401","1":"20220401","2":"20220401","3":"20220401","4":"20220401"},"item_id":{"0":"1","1":"3","2":"9","3":"16","4":"31"},"store_id":{"0":"5","1":"3","2":"1","3":"1","4":"1"},"average_order_period":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"average_order_period_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"average_order_period_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_amount":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"order_amount_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_amount_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_price":{"0":"0.01","1":"0.02","2":"26","3":"27","4":"23"},"order_price_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_price_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"last_order_time":{"0":"2022-06-02","1":"2022-06-02","2":"2022-06-02","3":"2022-06-19","4":"2022-06-19"},"last_order_amount":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"last_order_price":{"0":"0.01","1":"0.02","2":"26","3":"27","4":"23"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   item_id |   store_id |   order_amount |   order_amount_last_period |   last_order_amount |
|:------|------------:|----------:|-----------:|---------------:|---------------------------:|--------------------:|
| count | 10000       |  10000    |  10000     |     10000      |                 10000      |         10000       |
| mean  |     4.9248  |    421.34 |    377.878 |         8.5279 |                     2.6121 |             2.2474  |
| std   |     1.49304 |    342.81 |    315.883 |        18.7225 |                    10.7625 |             4.58274 |
| min   |     2       |      1    |      1     |         1      |                     0      |             1       |
| 25%   |     4       |    187    |    158     |         1      |                     0      |             1       |
| 50%   |     4       |    276    |    330     |         3      |                     0      |             1       |
| 75%   |     6       |    566    |    517     |         7      |                     0      |             2       |
| max   |    10       |   1386    |   2365     |       383      |                   239      |           250       |