# app_sku_label_di
* comment: 商品标签
* last_data_modified_time: 2025-09-18 03:05:29

# schema:
CREATE TABLE summerfarm_tech.`app_sku_label_di` (
  `date` STRING COMMENT '日期',
  `sku_id` STRING COMMENT 'sku编号',
  `sku_label` STRING COMMENT 'ABC标签',
  `dlv_30_origin_amt` DECIMAL(38,18) COMMENT '近30天履约应付GMV',
  `dlv_30_cust_cnt` BIGINT COMMENT '近30天履约客户数',
  `dlv_30_sku_cnt` BIGINT COMMENT '近30天履约数量',
  `dlv_30_gross_profit_amt` DECIMAL(38,18) COMMENT '近30天履约实付毛利润',
  `dlv_30_gross_rate` DECIMAL(38,18) COMMENT '近30天履约实付毛利率',
  `dlv_31_60_origin_amt` DECIMAL(38,18) COMMENT '近31-60天履约应付GMV',
  `dlv_31_60_cust_cnt` BIGINT COMMENT '近31-60天履约客户数',
  `dlv_31_60_sku_cnt` BIGINT COMMENT '近31-60天履约数量',
  `dlv_31_60_gross_profit_amt` DECIMAL(38,18) COMMENT '近31-60天履约实付毛利润',
  `dlv_31_60_gross_rate` DECIMAL(38,18) COMMENT '近31-60天履约实付毛利率',
  `dlv_origin_growth_amount` DECIMAL(38,18) COMMENT '履约应付增长额',
  `dlv_origin_growth_rate` DECIMAL(38,18) COMMENT '履约应付增长率',
  `dlv_cust_growth_cnt` BIGINT COMMENT '履约客户增长额',
  `dlv_cust_growth_rate` DECIMAL(38,18) COMMENT '履约客户增长率',
  `dlv_origin_profit_growth_amount` DECIMAL(38,18) COMMENT '履约毛利润增长额',
  `dlv_real_profit_growth_rate` DECIMAL(38,18) COMMENT '履约毛利润增长率',
  `dlv_origin_growth_label` STRING COMMENT 'GMC增速标签',
  `dlv_origin_rate_label` STRING COMMENT 'GMC增率标签',
  `dlv_cust_growth_label` STRING COMMENT '客户增速标签',
  `dlv_cust_rate_label` STRING COMMENT '客户增率标签',
  `dlv_profit_growth_label` STRING COMMENT '毛利润增速标签',
  `dlv_profit_rate_label` STRING COMMENT '毛利润增率标签',
  `spu_name_list` STRING COMMENT '购物车关联TOP3list'
)
COMMENT '商品标签'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"sku_id":{"0":"1003074364015","1":"1003572460835","2":"1003850240672","3":"1007565354171","4":"1007565354578","5":"100803","6":"1009660717334","7":"1017770764724","8":"1017770764850","9":"102228"},"sku_label":{"0":"B","1":"B","2":"C","3":"B","4":"C","5":"B","6":"B","7":"C","8":"C","9":"C"},"dlv_30_origin_amt":{"0":"137824","1":"144720","2":"13900","3":"269423.88","4":"13617.6","5":"191411.44","6":"44045","7":"3554.14","8":"7400","9":"12373.75"},"dlv_30_cust_cnt":{"0":"198","1":"56","2":"3","3":"1164","4":"538","5":"4316","6":"113","7":"101","8":"32","9":"358"},"dlv_30_sku_cnt":{"0":"292","1":"92","2":"8","3":"2024","4":"1035","5":"10633","6":"139","7":"187","8":"40","9":"428"},"dlv_30_gross_profit_amt":{"0":"25929.506666666666666687","1":"3750","2":"554.28","3":"70456.560526315789473687","4":"4710.140000000000000004","5":"30088.190000000000000025","6":"8797.13","7":"1177.890000000000000001","8":"1347","9":"2814.109999999999999999"},"dlv_30_gross_rate":{"0":"0.188134916028171194","1":"0.025912106135986733","2":"0.039876258992805755","3":"0.261508224609918725","4":"0.345886206086241335","5":"0.157191179377784316","6":"0.199730502894766716","7":"0.331413506502276219","8":"0.182027027027027027","9":"0.227425800585917769"},"dlv_31_60_origin_amt":{"0":"102896","1":"146330","2":"0","3":"364986.82","4":"16441.8","5":"133016.4","6":"33919","7":"3021","8":"7585","9":"16801.93"},"dlv_31_60_cust_cnt":{"0":"139","1":"42","2":"0","3":"1398","4":"714","5":"3330","6":"89","7":"88","8":"27","9":"265"},"dlv_31_60_sku_cnt":{"0":"218","1":"94","2":"0","3":"2741","4":"1248","5":"7390","6":"107","7":"159","8":"41","9":"292"},"dlv_31_60_gross_profit_amt":{"0":"20205.27000000000000002","1":"4210","2":"0","3":"95742.679473684210526321","4":"3585.220000000000000004","5":"22629.709999999999999996","6":"6852.330000000000000001","7":"980.930000000000000001","8":"1238.12","9":"3739.38"},"dlv_31_60_gross_rate":{"0":"0.196365942310682631","1":"0.028770587029317297","2":"0","3":"0.262318183088595392","4":"0.218055200768772276","5":"0.170127217395749697","6":"0.202020401544856865","7":"0.324703740483283681","8":"0.163232696110744891","9":"0.222556575345808487"},"dlv_origin_growth_amount":{"0":"41963.72999999999999998","1":"-910","2":"13900","3":"-69163.259473684210526321","4":"857.859999999999999996","5":"60743.840000000000000004","6":"10990.669999999999999999","7":"585.709999999999999999","8":"11.88","9":"-4136.48"},"dlv_origin_growth_rate":{"0":"0.437759355361715547","1":"-0.006248712490558264","2":"0","3":"-0.204270190477981051","4":"0.067231777450010737","5":"0.464873006009140751","6":"0.332503184907998438","7":"0.197313057744329494","8":"0.001607986876228323","9":"-0.25054042251379902"},"dlv_cust_growth_cnt":{"0":"59","1":"14","2":"3","3":"-234","4":"-176","5":"986","6":"24","7":"13","8":"5","9":"93"},"dlv_cust_growth_rate":{"0":"0.4244604316546763","1":"0.3333333333333333","2":"0","3":"-0.1673819742489271","4":"-0.2464985994397759","5":"0.2960960960960961","6":"0.2696629213483146","7":"0.1477272727272727","8":"0.1851851851851851","9":"0.350943396226415"},"dlv_origin_profit_growth_amount":{"0":"5724.236666666666666667","1":"-460","2":"554.28","3":"-25286.118947368421052634","4":"1124.92","5":"7458.480000000000000029","6":"1944.799999999999999999","7":"196.96","8":"108.88","9":"-925.270000000000000001"},"dlv_real_profit_growth_rate":{"0":"0.283304141279313103","1":"-0.109263657957244656","2":"0","3":"-0.264104985220499832","4":"0.313765961363598329","5":"0.32958796201984029","6":"0.283815869930374048","7":"0.20078904712874517","8":"0.087939779665945143","9":"-0.247439415090202119"},"dlv_origin_growth_label":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"dlv_origin_rate_label":{"0":"高增长","1":"其他","2":"其他","3":"其他","4":"其他","5":"高增长","6":"高增长","7":"其他","8":"其他","9":"其他"},"dlv_cust_growth_label":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他","5":"高增长","6":"其他","7":"其他","8":"其他","9":"其他"},"dlv_cust_rate_label":{"0":"高增长","1":"高增长","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"高增长"},"dlv_profit_growth_label":{"0":"其他","1":"其他","2":"其他","3":"锐减","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"dlv_profit_rate_label":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"高增长","5":"高增长","6":"其他","7":"其他","8":"其他","9":"其他"},"spu_name_list":{"0":"安佳淡奶油_爱乐薇(铁塔)淡奶油_酷盖纯牛奶","1":"安佳奶油奶酪5KG_安佳无盐大黄油25kg_安佳淡奶油","2":"优诺4.0冷藏牛乳_安佳块状马苏里拉奶酪_安佳芝易马苏里拉干酪碎","3":"Protag纯牛奶_广东粗皮香水柠檬_酷盖纯牛奶","4":"Protag纯牛奶_蒙特瑞草莓_酷盖纯牛奶","5":"安佳淡奶油_蒙特瑞草莓_蓝莓盒装","6":"安佳淡奶油_蒙特瑞草莓_酷盖纯牛奶","7":"味斯美3A海苔酥脆松_安佳淡奶油_蒙特瑞草莓","8":"秘鲁蓝莓_蒙特瑞草莓_蓝莓盒装","9":"南非橙_广东粗皮香水柠檬_薄荷叶"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   dlv_30_cust_cnt |   dlv_30_sku_cnt |   dlv_31_60_cust_cnt |   dlv_31_60_sku_cnt |   dlv_cust_growth_cnt |
|:------|------------------:|-----------------:|---------------------:|--------------------:|----------------------:|
| count |           1901    |         1901     |             1901     |            1901     |             1901      |
| mean  |            321.42 |          575.431 |              298.977 |             557.922 |               22.4429 |
| std   |           1122.37 |         2210.19  |             1002.5   |            2251.97  |              407.513  |
| min   |              0    |            0     |                0     |               0     |            -3050      |
| 25%   |              4    |            5     |                4     |               6     |              -13      |
| 50%   |             44    |           67     |               46     |              66     |                1      |
| 75%   |            198    |          312     |              198     |             319     |               20      |
| max   |          27001    |        41454     |            20718     |           41995     |             8178      |