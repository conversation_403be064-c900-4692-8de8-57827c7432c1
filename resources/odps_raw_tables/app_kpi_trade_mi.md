# app_kpi_trade_mi
* comment: 交易口径kpi指标月汇总
* last_data_modified_time: 2025-09-18 02:55:43

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_trade_mi` (
  `month` STRING COMMENT '月份',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `target_origin_total_amt` DECIMAL(38,18) COMMENT '目标应付总金额',
  `target_cust_cnt` BIGINT COMMENT '目标客户数',
  `target_cust_arpu` DECIMAL(38,18) COMMENT '目标ARPU',
  `target_order_cnt` BIGINT COMMENT '目标订单数',
  `target_after_sale_rate` DECIMAL(38,18) COMMENT '目标退货率',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标月汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509"},"origin_total_amt":{"0":"68734731.43"},"real_total_amt":{"0":"66564614.95"},"cust_cnt":{"0":"45562"},"cust_arpu":{"0":"1508.5977663403713621"},"order_cnt":{"0":"155022"},"order_avg":{"0":"443.386947852562862045"},"after_sale_noreceived_amt":{"0":"2420826.95"},"after_sale_rate":{"0":"0.035219850279991121"},"target_origin_total_amt":{"0":"0"},"target_cust_cnt":{"0":"0"},"target_cust_arpu":{"0":"0"},"target_order_cnt":{"0":"0"},"target_after_sale_rate":{"0":"0"},"dire_origin_total_amt":{"0":"0"},"delivery_amt":{"0":"272903.93"},"timing_origin_total_amt":{"0":"5378500"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   target_cust_cnt |   target_order_cnt |
|:------|-----------:|------------:|------------------:|-------------------:|
| count |          1 |           1 |                 1 |                  1 |
| mean  |      45562 |      155022 |                 0 |                  0 |
| std   |        nan |         nan |               nan |                nan |
| min   |      45562 |      155022 |                 0 |                  0 |
| 25%   |      45562 |      155022 |                 0 |                  0 |
| 50%   |      45562 |      155022 |                 0 |                  0 |
| 75%   |      45562 |      155022 |                 0 |                  0 |
| max   |      45562 |      155022 |                 0 |                  0 |