# app_stc_warehouse_kpi_mi
* comment: 仓配kpi库存数据
* last_data_modified_time: 2025-09-18 03:43:06

# schema:
CREATE TABLE summerfarm_tech.`app_stc_warehouse_kpi_mi` (
  `month` STRING COMMENT '月份',
  `warehouse_no` BIGINT COMMENT '库存仓号',
  `warehouse_name` STRING COMMENT '库存仓',
  `check_sku_cnt` BIGINT COMMENT '抽检数量',
  `in_bound_sku_cnt` BIGINT COMMENT '入库数量',
  `back_order_cnt` BIGINT COMMENT '退货总单数',
  `finish_order_cnt` BIGINT COMMENT '已完成单数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责',
  `sale_amt` DECIMAL(38,18) COMMENT '销售金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额',
  `after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责',
  `after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责',
  `after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责',
  `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责',
  `after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付GMV',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
)
COMMENT '仓配kpi库存数据'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509","5":"202509","6":"202509","7":"202509","8":"202509","9":"202509"},"warehouse_no":{"0":"63","1":"117","2":"24","3":"60","4":"91","5":"64","6":"121","7":"155","8":"48","9":"2"},"warehouse_name":{"0":"贵阳总仓","1":"东莞冷冻总仓","2":"华西总仓","3":"昆明总仓","4":"美团虚拟代下单总仓","5":"青岛总仓","6":"嘉兴海盐总仓","7":"武汉总仓","8":"长沙总仓","9":"上海总仓"},"check_sku_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_bound_sku_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"back_order_cnt":{"0":"5","1":"18","2":"26","3":"3","4":"0","5":"23","6":"50","7":"71","8":"82","9":"8"},"finish_order_cnt":{"0":"5","1":"7","2":"24","3":"3","4":"0","5":"18","6":"39","7":"54","8":"50","9":"8"},"damage_amt":{"0":"3366.13","1":"718.7","2":"7931.62","3":"945","4":"0","5":"2802.93","6":"291.16","7":"9721.17","8":"10267.21","9":"0"},"damage_amt_wah":{"0":"0","1":"0","2":"473.74","3":"0","4":"0","5":"1150.75","6":"0","7":"1698.48","8":"338.25","9":"0"},"sale_amt":{"0":"423105.02","1":"2105516.64","2":"3363929.08","3":"751868.02","4":"240958.2","5":"1301665.73","6":"2226821.14","7":"3074193.98","8":"5664975.45","9":"1636042.49"},"after_sale_amt":{"0":"1813.22","1":"3467.46","2":"16052.48","3":"970.26","4":"0","5":"13219.47","6":"5193.31","7":"16029.76","8":"41861.01","9":"860.13"},"after_sale_amt_wah":{"0":"26.88","1":"1369.96","2":"787.3","3":"448.48","4":"0","5":"233.47","6":"2867.21","7":"789.28","8":"1472.57","9":"0"},"after_sale_amt_pur":{"0":"0","1":"0","2":"1412.37","3":"0","4":"0","5":"853.42","6":"0","7":"2089.35","8":"3632.55","9":"0"},"after_sale_amt_che":{"0":"1734.27","1":"1373.74","2":"11682.53","3":"241.78","4":"0","5":"10596.83","6":"935.57","7":"9684.99","8":"33041.05","9":"860.13"},"after_sale_amt_pur_che":{"0":"1734.27","1":"1373.74","2":"13094.9","3":"241.78","4":"0","5":"11450.25","6":"935.57","7":"11774.34","8":"36673.6","9":"860.13"},"after_sale_amt_oth":{"0":"52.07","1":"723.76","2":"2170.28","3":"280","4":"0","5":"1535.75","6":"1390.53","7":"3466.14","8":"3714.84","9":"0"},"delivery_total_amt":{"0":"479104.900000000000000002","1":"2456076.346666666666622506","2":"3750118.66999999999998701","3":"815669.160000000000000009","4":"252410","5":"1444962.910000000000002037","6":"3628144.229999999999912432","7":"3654407.679999999999951745","8":"7041281.77999999999999109","9":"2120948.850000000000004"},"coupon_amt":{"0":"4240.24","1":"16160.3666666666666225","2":"36580.219999999999987","3":"3883.34","4":"0","5":"18331.390000000000002","6":"26098.9776190476189601","7":"36708.0197835497835015","8":"89801.0733333333333243","9":"4176.700000000000004"},"origin_total_amt":{"0":"485408.44","1":"2499504.08","2":"3893087.39","3":"836762.31","4":"0","5":"1484175.08","6":"2547380.28","7":"3733879.29","8":"7579102.19","9":"1600273.53"},"real_total_amt":{"0":"474708.060000000000000002","1":"2444028.290000000000000006","2":"3810603.50000000000000001","3":"805869.820000000000000009","4":"0","5":"1427297.520000000000000037","6":"2486575.082380952380952332","7":"3625753.610216450216450246","8":"7357997.686666666666666792","9":"1583506.03"},"storage_amt":{"0":"11021.692192142633543418","1":"24841.352756124823570479","2":"64516.343067175557187583","3":"12827.908653092922241268","4":"0","5":"28303.806530146421701716","6":"22499.730287963127413289","7":"107969.744372382620858859","8":"165600.515773850776649538","9":"16427.988711359695515868"},"arterial_roads_amt":{"0":"0","1":"18588.731944717566717837","2":"0","3":"0","4":"0","5":"16668.265434531946265608","6":"13508.361340366800649557","7":"177365.545415301032078272","8":"219306.589342535637789489","9":"1782.411037664149496392"},"deliver_amt":{"0":"18435.276630656462604209","1":"53557.858247351655139204","2":"226700.562303223314136972","3":"25070.498574426637846426","4":"0","5":"64130.951111987366137525","6":"68211.994830524923684675","7":"403354.842748672811791261","8":"808503.794967795606243657","9":"34642.015230225667424685"},"self_picked_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"other_amt":{"0":"3356.222944529542438161","1":"0","2":"0","3":"3326.696851015647930124","4":"0","5":"3128.427560994877385294","6":"4875.15236759396152311","7":"13682.511325313420737116","8":"31115.63004476959043812","9":"28.707726476245718774"},"allocation_amt":{"0":"0","1":"4342.32490055147652785","2":"55254.135309839174060894","3":"31344.876996236326965115","4":"0","5":"15546.653054412607035843","6":"251.377358089469069951","7":"14403.205533076604335921","8":"23811.226776626488113386","9":"280.18741039690280243"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   check_sku_cnt |   in_bound_sku_cnt |   back_order_cnt |   finish_order_cnt |
|:------|---------------:|----------------:|-------------------:|-----------------:|-------------------:|
| count |        19      |              19 |                 19 |          19      |            19      |
| mean  |        75.3684 |               0 |                  0 |          56.2105 |            43.6842 |
| std   |        47.6553 |               0 |                  0 |         101.224  |            81.2972 |
| min   |         2      |               0 |                  0 |           0      |             0      |
| 25%   |        43      |               0 |                  0 |           6      |             4.5    |
| 50%   |        63      |               0 |                  0 |          18      |             9      |
| 75%   |       119      |               0 |                  0 |          60.5    |            44.5    |
| max   |       155      |               0 |                  0 |         433      |           347      |