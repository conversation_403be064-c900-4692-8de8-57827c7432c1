# app_xianmu_sale_purchase_back_item_df
* comment: 采购退货单表
* last_data_modified_time: 2025-09-18 02:41:55

# schema:
CREATE TABLE summerfarm_tech.`app_xianmu_sale_purchase_back_item_df` (
  `return_no` STRING COMMENT '退货编号',
  `purchase_no` STRING COMMENT '采购单号',
  `order_no` STRING COMMENT '销售单号',
  `sku` STRING COMMENT 'sku编码',
  `pd_name` STRING COMMENT '商品名称',
  `weight` STRING COMMENT '规格',
  `purchase_quantity` BIGINT COMMENT '采购数量',
  `actual_quantity` BIGINT COMMENT '退订数量',
  `cost` DECIMAL(38,18) COMMENT '单个成本',
  `total_cost` DECIMAL(38,18) COMMENT '总成本',
  `no_in_quantity` BIGINT COMMENT '未入库数量'
)
COMMENT '采购退货单表'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"return_no":{"0":"ZCTG550850","1":"ZCTG550873"},"purchase_no":{"0":"ZC0125OWJCTJ0328142568","1":"ZC0125OWJCTJ0328142568"},"order_no":{"0":"0125OWJCTJ0328142568","1":"0125OWJCTJ0328142568"},"sku":{"0":"81737321485","1":"81737321814"},"pd_name":{"0":"性能测试品","1":"性能测试品"},"weight":{"0":"None","1":"None"},"purchase_quantity":{"0":"1","1":"1"},"actual_quantity":{"0":"1","1":"1"},"cost":{"0":"1.02","1":"1.025"},"total_cost":{"0":"1.02","1":"1.025"},"no_in_quantity":{"0":"0","1":"0"},"ds":{"0":"20250917","1":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   purchase_quantity |   actual_quantity |   no_in_quantity |
|:------|--------------------:|------------------:|-----------------:|
| count |                   2 |                 2 |                2 |
| mean  |                   1 |                 1 |                0 |
| std   |                   0 |                 0 |                0 |
| min   |                   1 |                 1 |                0 |
| 25%   |                   1 |                 1 |                0 |
| 50%   |                   1 |                 1 |                0 |
| 75%   |                   1 |                 1 |                0 |
| max   |                   1 |                 1 |                0 |