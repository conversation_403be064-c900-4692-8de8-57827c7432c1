# app_kpi_operate_large_category_delivery_mi
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:43:02

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_large_category_delivery_mi` (
  `month` STRING COMMENT '月份',
  `large_area_name` STRING COMMENT '运营服务大区',
  `category` STRING COMMENT '鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"large_area_name":{"0":"上海大区","1":"上海大区","2":"可可快递服务区","3":"广州大区","4":"广州大区"},"category":{"0":"乳制品","1":"其他","2":"鲜果","3":"乳制品","4":"其他"},"origin_total_amt":{"0":"2802064.62","1":"1060559.77","2":"230.6","3":"9054853.56","4":"2608300.83"},"real_total_amt":{"0":"2721917.66619047619047622","1":"1024119.736666666666666667","2":"230.6","3":"8823235.424444444444444444","4":"2485483.956666666666666672"},"marketing_amt":{"0":"80146.95380952380952378","1":"36440.033333333333333333","2":"0","3":"231618.135555555555555556","4":"122816.873333333333333328"},"cost_amt":{"0":"2566664.61","1":"857499.75","2":"157.22","3":"8495293.86","4":"2107853.29"},"origin_gross":{"0":"235400.01","1":"203060.02","2":"73.38","3":"559559.7","4":"500447.54"},"real_gross":{"0":"155253.05619047619047622","1":"166619.986666666666666667","2":"73.38","3":"327941.564444444444444444","4":"377630.666666666666666672"},"origin_gross_margin":{"0":"0.084009486547815589","1":"0.191464946855376194","2":"0.31821335646140503","3":"0.061796659249340748","4":"0.19186726248904349"},"real_gross_margin":{"0":"0.057038116221848934","1":"0.162695806653415373","2":"0.31821335646140503","3":"0.037167949019686654","4":"0.151934461557786465"},"cust_cnt":{"0":"1398","1":"1346","2":"2","3":"5281","4":"5161"},"point_cnt":{"0":"3783","1":"3742","2":"2","3":"10782","4":"9726"},"origin_pre_cust_price":{"0":"2004.338068669527896996","1":"787.934450222882615156","2":"115.3","3":"1714.609649687559174399","4":"505.386713815152102306"},"real_pre_cust_price":{"0":"1947.008344914503712787","1":"760.861617137196631996","2":"115.3","3":"1670.750885143806938922","4":"481.589606019505263838"},"timing_origin_amt":{"0":"226771","1":"142216","2":"0","3":"581894","4":"272793"},"timing_real_amt":{"0":"213234.226190476190476201","1":"130950.67666666666666665","2":"0","3":"542356.944444444444444436","4":"250842.116666666666666667"},"consign_origin_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_marketing_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_origin_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_cust_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"storage_amt":{"0":"59802.256382417242038117","1":"50677.364954743399906055","2":"0","3":"134735.291710871060097656","4":"126715.067461164176456318"},"arterial_roads_amt":{"0":"8158.013475083352659075","1":"6102.856765992118626356","2":"0","3":"107159.284542657619006268","4":"101974.104338507817994247"},"self_picked_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_amt":{"0":"1036.413319267754208376","1":"867.451430878204021773","2":"0","3":"23532.274727460252687095","4":"22149.130319196815608143"},"other_amt":{"0":"199.163352556899927985","1":"249.92977932731449472","2":"0","3":"40.957425761847517716","4":"1.880895894751656829"},"deliver_amt":{"0":"122941.799550188987527097","1":"106366.975667296650422728","2":"0","3":"289421.508723076875639688","4":"266454.941489468124801057"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |      47    |       47    |                 47 |
| mean  |    1618.28 |     3913.66 |                  0 |
| std   |    1630.16 |     4407.61 |                  0 |
| min   |       1    |        1    |                  0 |
| 25%   |     376    |      729.5  |                  0 |
| 50%   |    1177    |     2448    |                  0 |
| 75%   |    1970.5  |     4523.5  |                  0 |
| max   |    6082    |    20255    |                  0 |