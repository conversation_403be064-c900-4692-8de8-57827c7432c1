# app_financial_accounts_order_item_di
* comment: 账期订单费用项
* last_data_modified_time: 2025-09-18 02:52:44

# schema:
CREATE TABLE summerfarm_tech.`app_financial_accounts_order_item_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `brand_id` BIGINT COMMENT '品牌id',
  `type` BIGINT COMMENT '费用类型:0:订单明细;1:售后单明细',
  `order_no` STRING COMMENT '订单号',
  `order_item_id` BIGINT COMMENT '订单明细id',
  `order_time` DATETIME COMMENT '下单时间',
  `after_sale_order_no` STRING COMMENT '售后单号',
  `store_id` BIGINT COMMENT '门店id',
  `store_name` STRING COMMENT '门店名称',
  `delivery_address` STRING COMMENT '配送地址',
  `sku` STRING COMMENT 'sku',
  `pd_id` BIGINT COMMENT 'pdid',
  `pd_name` STRING COMMENT '商品名称',
  `weight` STRING COMMENT '商品规格',
  `sub_type` BIGINT COMMENT '商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓',
  `order_type` STRING COMMENT '订单类型/售后单类型',
  `handle_type` STRING COMMENT '售后服务原因',
  `remark` STRING COMMENT '订单备注/售后原因',
  `finish_time` DATETIME COMMENT '履约/售后完成时间',
  `quantity` BIGINT COMMENT '下单个数/售后数量',
  `payable_amount` DECIMAL(38,18) COMMENT '应付单价',
  `actually_paid_amount` DECIMAL(38,18) COMMENT '实付单价',
  `total_actually_paid_amount` DECIMAL(38,18) COMMENT '实付/售后总价',
  `delivery_fee` DECIMAL(38,18) COMMENT '运费',
  `item_unit` STRING COMMENT '售后单位'
)
COMMENT '账期订单费用项'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":123,"1":13,"2":13,"3":13,"4":13},"brand_id":{"0":1180838,"1":2041,"2":2041,"3":2041,"4":2041},"type":{"0":1,"1":1,"2":1,"3":1,"4":1},"order_no":{"0":"OR175801055850208","1":"OR175791053540655","2":"OR175791053540655","3":"OR175791053540655","4":"OR175791053540655"},"order_item_id":{"0":1785348,"1":1781141,"2":1781141,"3":1781141,"4":1781141},"order_time":{"0":1758039358000,"1":1757939335000,"2":1757939335000,"3":1757939335000,"4":1757939335000},"after_sale_order_no":{"0":"AS1968236920246972416","1":"AS1967950738980155392","2":"AS1968206129177636864","3":"AS1968235517701074944","4":"AS1968264612916834304"},"store_id":{"0":534679,"1":913,"2":913,"3":913,"4":913},"store_name":{"0":"杭州遇见村上（总店）","1":"乐山万达","2":"乐山万达","3":"乐山万达","4":"乐山万达"},"delivery_address":{"0":"浙江杭州市上城区机场路东方丽都花苑5幢商铺11号遇见村上千层蛋糕总店","1":"四川乐山市市中区一只酸奶牛(乐山万达店)3067b","2":"四川乐山市市中区一只酸奶牛(乐山万达店)3067b","3":"四川乐山市市中区一只酸奶牛(乐山万达店)3067b","4":"四川乐山市市中区一只酸奶牛(乐山万达店)3067b"},"sku":{"0":"5417640046","1":"5404785202","2":"5404785202","3":"5404785202","4":"5404785202"},"pd_id":{"0":1428,"1":1484,"2":1484,"3":1484,"4":1484},"pd_name":{"0":"无花果","1":"即食秘鲁牛油果","2":"即食秘鲁牛油果","3":"即食秘鲁牛油果","4":"即食秘鲁牛油果"},"weight":{"0":"净重5-6斤\/普通\/标准规格","1":"20个*1箱\/一级\/单果130-160g","2":"20个*1箱\/一级\/单果130-160g","3":"20个*1箱\/一级\/单果130-160g","4":"20个*1箱\/一级\/单果130-160g"},"sub_type":{"0":3,"1":3,"2":3,"3":3,"4":3},"order_type":{"0":"已到货售后","1":"已到货售后","2":"已到货售后","3":"已到货售后","4":"已到货售后"},"handle_type":{"0":"退款","1":"退款","2":"退款","3":"退款","4":"退款"},"remark":{"0":"商品品质问题","1":"商品品质问题","2":"商品品质问题","3":"商品品质问题","4":"商品品质问题"},"finish_time":{"0":1758135661000,"1":1758119341000,"2":1758122343000,"3":1758127742000,"4":1758135960000},"quantity":{"0":512,"1":1,"2":2,"3":1,"4":1},"payable_amount":{"0":0.0,"1":0.0,"2":0.0,"3":0.0,"4":0.0},"actually_paid_amount":{"0":0.0,"1":0.0,"2":0.0,"3":0.0,"4":0.0},"total_actually_paid_amount":{"0":-11.74,"1":-6.25,"2":-12.5,"3":-6.25,"4":-6.25},"delivery_fee":{"0":0.0,"1":0.0,"2":0.0,"3":0.0,"4":0.0},"item_unit":{"0":"g","1":"个","2":"个","3":"个","4":"个"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |         brand_id |        type |   order_item_id | order_time                    |   store_id |    pd_id |   sub_type | finish_time                   |   quantity |
|:------|------------:|-----------------:|------------:|----------------:|:------------------------------|-----------:|---------:|-----------:|:------------------------------|-----------:|
| count |     387     |    387           | 387         |   387           | 387                           |        387 |   387    | 387        | 387                           |   387      |
| mean  |      56.137 | 221860           |   0.0568475 |     1.78518e+06 | 2025-09-16 15:03:14.472868352 |     326988 |  1567.12 |   2.98966  | 2025-09-17 10:22:15.470284288 |    40.0336 |
| min   |       8     |    113           |   0         |     1.77608e+06 | 2025-09-13 17:25:50           |        410 |    28    |   1        | 2025-09-17 05:45:36           |     1      |
| 25%   |      13     |   2041           |   0         |     1.78509e+06 | 2025-09-16 16:08:43           |     181576 |  1123    |   3        | 2025-09-17 08:41:14           |     1      |
| 50%   |      59     |  11365           |   0         |     1.78519e+06 | 2025-09-16 16:09:22           |     373649 |  1484    |   3        | 2025-09-17 09:59:02           |     1      |
| 75%   |      59     |  11365           |   0         |     1.78557e+06 | 2025-09-16 16:53:07           |     482343 |  1640    |   3        | 2025-09-17 11:20:03.500000    |     2      |
| max   |     123     |      1.18084e+06 |   1         |     1.78711e+06 | 2025-09-16 22:13:13           |     540093 | 15887    |   3        | 2025-09-17 19:06:00           |  3952      |
| std   |      33.713 | 441945           |   0.231851  |  1229.44        | nan                           |     196871 |  1417.4  |   0.124245 | nan                           |   306.718  |