# app_warehouse_estimated_consumption_df
* comment: 库存仓预估消耗
* last_data_modified_time: 2025-09-18 04:28:51

# schema:
CREATE TABLE summerfarm_tech.`app_warehouse_estimated_consumption_df` (
  `pd_id` BIGINT COMMENT 'pd_id',
  `sku_id` STRING COMMENT 'sku',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `view_date` DATETIME COMMENT '日期',
  `estimated_sales` BIGINT COMMENT '预估销量',
  `estimated_transfer_out` BIGINT COMMENT '预估调拨量',
  `consumption` BIGINT COMMENT '预估消耗量',
  `sale_cnt` DECIMAL(38,18) COMMENT '预销出库量(不取整)',
  `allocation_cnt` DECIMAL(38,18) COMMENT '预调出库量(不取整)',
  `consumption_a` DECIMAL(38,18) COMMENT '预估消耗量(不取整)',
  `transfer_order_plan_out_quantity` DECIMAL(38,18) COMMENT '调拨计划出数量'
)
COMMENT '库存仓预估消耗'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"pd_id":{"0":"9629","1":"9629","2":"9629","3":"9629","4":"9629","5":"9629","6":"9629","7":"9629","8":"9629","9":"9629"},"sku_id":{"0":"1000063425545","1":"1000063425545","2":"1000063425545","3":"1000063425545","4":"1000063425545","5":"1000063425545","6":"1000063425545","7":"1000063425545","8":"1000063425545","9":"1000063425545"},"warehouse_no":{"0":"2","1":"2","2":"2","3":"2","4":"2","5":"2","6":"2","7":"2","8":"2","9":"2"},"view_date":{"0":"2025-09-18","1":"2025-09-20","2":"2025-09-22","3":"2025-09-24","4":"2025-09-25","5":"2025-09-27","6":"2025-09-28","7":"2025-09-30","8":"2025-10-03","9":"2025-10-04"},"estimated_sales":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"estimated_transfer_out":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"consumption":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"sale_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"allocation_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"consumption_a":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"transfer_order_plan_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |    pd_id |   warehouse_no | view_date                     |   estimated_sales |   estimated_transfer_out |   consumption |
|:------|---------:|---------------:|:------------------------------|------------------:|-------------------------:|--------------:|
| count | 10000    |      10000     | 10000                         |     10000         |                    10000 | 10000         |
| mean  |  8302.23 |         78.894 | 2025-10-30 00:07:37.919999744 |         0.0081    |                        0 |     0.0081    |
| min   |   100    |          2     | 2025-09-18 00:00:00           |         0         |                        0 |     0         |
| 25%   |  7545    |         38     | 2025-10-09 00:00:00           |         0         |                        0 |     0         |
| 50%   |  7915    |         63     | 2025-10-30 00:00:00           |         0         |                        0 |     0         |
| 75%   |  8558    |        125     | 2025-11-20 00:00:00           |         0         |                        0 |     0         |
| max   | 16711    |        169     | 2025-12-12 00:00:00           |         1         |                        0 |     1         |
| std   |  4045.76 |         50.841 | nan                           |         0.0896392 |                        0 |     0.0896392 |