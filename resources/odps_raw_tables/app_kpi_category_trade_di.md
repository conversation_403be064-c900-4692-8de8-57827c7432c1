# app_kpi_category_trade_di
* comment: 交易口径kpi指标日汇总
* last_data_modified_time: 2025-09-18 02:52:10

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_category_trade_di` (
  `date` STRING COMMENT '日期',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917"},"category":{"0":"乳制品","1":"其他","2":"鲜果"},"origin_total_amt":{"0":"2147647.37","1":"668133.34","2":"982831.54"},"real_total_amt":{"0":"2081425.54","1":"635894.64","2":"945607.6"},"cust_cnt":{"0":"2680","1":"2355","2":"4979"},"cust_arpu":{"0":"801.360958955223880597","1":"283.708424628450106157","2":"197.395368547901184977"},"order_cnt":{"0":"2885","1":"2583","2":"5609"},"order_avg":{"0":"744.418499133448873484","1":"258.665636856368563686","2":"175.22402210732750936"},"after_sale_noreceived_amt":{"0":"139164.9","1":"33103.96","2":"18646.04"},"after_sale_rate":{"0":"0.064798766289085903","1":"0.049546936244792095","2":"0.018971755831116287"},"dire_origin_total_amt":{"0":"0","1":"0","2":"0"},"delivery_amt":{"0":"4317.93","1":"1461.39","2":"9221.86"},"timing_origin_total_amt":{"0":"172592","1":"36581","2":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |
|:------|-----------:|------------:|
| count |       3    |        3    |
| mean  |    3338    |     3692.33 |
| std   |    1430.41 |     1666.74 |
| min   |    2355    |     2583    |
| 25%   |    2517.5  |     2734    |
| 50%   |    2680    |     2885    |
| 75%   |    3829.5  |     4247    |
| max   |    4979    |     5609    |