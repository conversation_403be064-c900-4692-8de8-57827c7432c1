# app_saas_product_sales_overview_month_di
* comment: saas商品销售概况表(月维度)
* last_data_modified_time: 2025-09-18 02:35:33

# schema:
CREATE TABLE summerfarm_tech.`app_saas_product_sales_overview_month_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd(当月1号)',
  `category_id` BIGINT COMMENT '三级类目id',
  `store_type` BIGINT COMMENT '门店类型：0、直营店 1、加盟店 2、托管店',
  `store_id` BIGINT COMMENT '门店id',
  `store_name` STRING COMMENT '门店名',
  `province` STRING COMMENT '省',
  `city` STRING COMMENT '市',
  `pay_success_num` BIGINT COMMENT '支付成功商品件数',
  `pay_success_price` DECIMAL(38,18) COMMENT '支付成功金额',
  `refund_num` BIGINT COMMENT '退款件数',
  `refund_price` DECIMAL(38,18) COMMENT '退款金额',
  `warehouse_type` BIGINT COMMENT '归属类型 0自营品 1三方品',
  `delivery_type` BIGINT COMMENT '配送方式 0品牌方配送 1三方配送',
  `item_id` BIGINT COMMENT '商品编码',
  `title` STRING COMMENT '商品名称',
  `goods_type` BIGINT COMMENT '商品类型 0无货商品 1报价货品 2自营货品'
)
COMMENT 'saas商品销售概况表(月维度)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"time_tag":{"0":"20250901","1":"20250901","2":"20250901","3":"20250901","4":"20250901"},"category_id":{"0":"nan","1":"nan","2":"nan","3":"nan","4":"nan"},"store_type":{"0":"0","1":"0","2":"1","3":"1","4":"1"},"store_id":{"0":"2","1":"2","2":"537731","3":"537731","4":"537731"},"store_name":{"0":"乔治门店","1":"乔治门店","2":"谷人说-中国谷饮（淮海中路店）","3":"谷人说-中国谷饮（淮海中路店）","4":"谷人说-中国谷饮（淮海中路店）"},"province":{"0":"广东","1":"广东","2":"上海","3":"上海","4":"上海"},"city":{"0":"梅州市","1":"梅州市","2":"上海市","3":"上海市","4":"上海市"},"pay_success_num":{"0":"10","1":"1","2":"1","3":"0","4":"0"},"pay_success_price":{"0":"0.1","1":"10","2":"1","3":"0","4":"0"},"refund_num":{"0":"0","1":"1","2":"0","3":"1","4":"1"},"refund_price":{"0":"0","1":"10","2":"0","3":"1","4":"333"},"warehouse_type":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"delivery_type":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"item_id":{"0":"1","1":"8","2":"35","3":"950","4":"986"},"title":{"0":"大草莓","1":"大草莓","2":"忙格测试的商品","3":"红颜草莓","4":"代仓测试"},"goods_type":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   category_id |   store_type |   store_id |   pay_success_num |   refund_num |   warehouse_type |   item_id |   goods_type |
|:------|------------:|--------------:|-------------:|-----------:|------------------:|-------------:|-----------------:|----------:|-------------:|
| count |  10000      |      8954     | 10000        |      10000 |        10000      |   10000      |     10000        |   10000   | 10000        |
| mean  |     23.6932 |       652.334 |     0.7712   |     236262 |            5.2195 |      48.5042 |         0.9966   |   22176.6 |     1.2608   |
| std   |     12.981  |       147.523 |     0.451963 |     225866 |           38.5164 |     536.974  |         0.453662 |   14562   |     0.634053 |
| min   |      2      |       526     |     0        |          2 |            0      |       0      |         0        |       1   |     0        |
| 25%   |     13      |       533     |     1        |       1735 |            1      |       0      |         1        |   13778   |     1        |
| 50%   |     14      |       589     |     1        |     354894 |            2      |       0      |         1        |   23353   |     1        |
| 75%   |     32      |       780     |     1        |     447290 |            5      |       0      |         1        |   33839   |     2        |
| max   |     58      |      1129     |     2        |     542067 |         3000      |   17216      |         2        |   44829   |     2        |