# app_mkt_order_sku_preferential_mi
* comment: 商品粒度订单优惠明细月表
* last_data_modified_time: 2025-09-18 03:38:15

# schema:
CREATE TABLE summerfarm_tech.`app_mkt_order_sku_preferential_mi` (
  `month` STRING COMMENT '月份',
  `sku_id` STRING COMMENT '商品SKU',
  `spu_id` BIGINT COMMENT '商品pd_id',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品规格',
  `sku_type` STRING COMMENT '商品类型; 自营 ,代仓 ,代售',
  `category1` STRING COMMENT '一级分类',
  `category2` STRING COMMENT '二级分类',
  `category3` STRING COMMENT '三级分类',
  `category4` STRING COMMENT '四级分类',
  `city_id` BIGINT COMMENT '运营服务区ID',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细）',
  `sku_cnt` BIGINT COMMENT '订单商品数量',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付金额',
  `activity_coupon_amt` DECIMAL(38,18) COMMENT '平台活动券优惠金额',
  `sh_coupon_amt` DECIMAL(38,18) COMMENT '售后补偿券优惠金额',
  `new_cust_coupon_amt` DECIMAL(38,18) COMMENT '区域拉新券优惠金额',
  `qy_coupon_amt` DECIMAL(38,18) COMMENT '会员权益券优惠金额',
  `bd_coupon_amt` DECIMAL(38,18) COMMENT '销售客情券优惠金额',
  `recall_coupon_amt` DECIMAL(38,18) COMMENT '销售月活券优惠金额',
  `xp_coupon_amt` DECIMAL(38,18) COMMENT '行业活动券优惠金额',
  `return_coupon_amt` DECIMAL(38,18) COMMENT '满返优惠金额(作废)',
  `ygfl_coupon_amt` DECIMAL(38,18) COMMENT '员工福利券优惠金额',
  `thbt_coupon_amt` DECIMAL(38,18) COMMENT '销售囤货券优惠金额',
  `jjbc_coupon_amt` DECIMAL(38,18) COMMENT '区域活动券优惠金额',
  `pllx_coupon_amt` DECIMAL(38,18) COMMENT '销售品类券优惠金额',
  `plzh_coupon_amt` DECIMAL(38,18) COMMENT '品类召回优惠金额(作废)',
  `lsfx_coupon_amt` DECIMAL(38,18) COMMENT '销售现货券优惠金额',
  `zxbt_coupon_amt` DECIMAL(38,18) COMMENT '滞销补贴优惠金额(作废)',
  `lbbt_coupon_amt` DECIMAL(38,18) COMMENT '临保补贴优惠金额(作废)',
  `tsqk_coupon_amt` DECIMAL(38,18) COMMENT '功能测试券优惠金额',
  `jzs_coupon_amt` DECIMAL(38,18) COMMENT '精准送优惠金额',
  `bed_pack_amt` DECIMAL(38,18) COMMENT '红包优惠金额',
  `deliver_coupon_amt` DECIMAL(38,18) COMMENT '运费优惠券金额',
  `activity_nolb_amt` DECIMAL(38,18) COMMENT '活动优惠金额（不含临保）',
  `activity_lb_amt` DECIMAL(38,18) COMMENT '临保活动优惠金额',
  `ladder_price_amt` DECIMAL(38,18) COMMENT '阶梯价优惠金额',
  `collocation_amt` DECIMAL(38,18) COMMENT '搭配购优惠金额',
  `suit_amt` DECIMAL(38,18) COMMENT '组合包优惠金额',
  `expand_amt` DECIMAL(38,18) COMMENT '拓展购买优惠金额',
  `replace_amt` DECIMAL(38,18) COMMENT '换购优惠金额',
  `presale_amt` DECIMAL(38,18) COMMENT '预售优惠金额',
  `presale_balance_amt` DECIMAL(38,18) COMMENT '预售尾款立减优惠金额',
  `group_purchase_amt` DECIMAL(38,18) COMMENT '多人拼团优惠金额',
  `reduce_amt` DECIMAL(38,18) COMMENT '满减优惠金额',
  `seckill_amt` DECIMAL(38,18) COMMENT '秒杀优惠金额',
  `cream_card_amt` DECIMAL(38,18) COMMENT '奶油卡优惠金额',
  `milk_card_amt` DECIMAL(38,18) COMMENT '鲜奶卡优惠金额',
  `other_card_amt` DECIMAL(38,18) COMMENT '其他黄金卡优惠金额',
  `gift_amt` DECIMAL(38,18) COMMENT '赠品优惠金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费应付',
  `delivery_real_amt` DECIMAL(38,18) COMMENT '运费实付',
  `live_exclusive_amt` DECIMAL(38,18) COMMENT '直播专享优惠金额',
  `live_sharing_amt` DECIMAL(38,18) COMMENT '直播同享优惠金额',
  `ppbc_coupon_amt` DECIMAL(38,18) COMMENT '平台补偿券优惠金额',
  `psbc_coupon_amt` DECIMAL(38,18) COMMENT '配送补偿券优惠金额',
  `qyzh_coupon_amt` DECIMAL(38,18) COMMENT '区域召回券优惠金额',
  `schd_coupon_amt` DECIMAL(38,18) COMMENT '市场活动券优惠金额',
  `qt_coupon_amt` DECIMAL(38,18) COMMENT '其他优惠券优惠金额'
)
COMMENT '商品粒度订单优惠明细月表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"sku_id":{"0":"1001450537647","1":"1003026205810","2":"1003026205810","3":"1003065575401","4":"1003074364015"},"spu_id":{"0":"16711","1":"9691","2":"9691","3":"7884","4":"3902"},"spu_name":{"0":"麦子妈意面黑松露牛肉","1":"EVA乳酸黄油","2":"EVA乳酸黄油","3":"柏札莱阿尔卑黄油","4":"ProtagxEva乳酸黄油"},"sku_disc":{"0":"281.9g*1袋","1":"25KG*1箱","2":"25KG*1箱","3":"400g*10盒\/400g","4":"10KG*1箱"},"sku_type":{"0":"代售","1":"代售","2":"代售","3":"代售","4":"自营"},"category1":{"0":"其他","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品"},"category2":{"0":"谷物制品","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品"},"category3":{"0":"谷物加工品","1":"黄油","2":"黄油","3":"黄油","4":"黄油"},"category4":{"0":"意面","1":"乳酸黄油","2":"乳酸黄油","3":"乳酸黄油","4":"乳酸黄油"},"city_id":{"0":"9137","1":"14343","2":"14400","3":"10358","4":"1001"},"city_name":{"0":"张家港","1":"广州","2":"佛山","3":"嘉兴","4":"杭州"},"large_area_id":{"0":"84","1":"14","2":"14","3":"1","4":"1"},"large_area_name":{"0":"苏南大区","1":"广州大区","2":"广州大区","3":"杭州大区","4":"杭州大区"},"cust_team":{"0":"平台客户","1":"平台客户","2":"平台客户","3":"平台客户","4":"平台客户"},"cust_type":{"0":"面包蛋糕","1":"甜品冰淇淋","2":"面包蛋糕","3":"面包蛋糕","4":"茶饮"},"life_cycle_detail":{"0":"S1","1":"A3","2":"S1","3":"S1","4":"S2"},"sku_cnt":{"0":"2","1":"1","2":"1","3":"1","4":"1"},"origin_total_amt":{"0":"13.8","1":"1299","2":"1299","3":"455","4":"472"},"real_total_amt":{"0":"13.8","1":"1284","2":"1299","3":"440","4":"457"},"activity_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"sh_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"new_cust_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"qy_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"bd_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"recall_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"xp_coupon_amt":{"0":"0","1":"15","2":"0","3":"15","4":"15"},"return_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ygfl_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"thbt_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"jjbc_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"pllx_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"plzh_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"lsfx_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"zxbt_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"lbbt_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"tsqk_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"jzs_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"bed_pack_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"deliver_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"activity_nolb_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"activity_lb_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ladder_price_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"collocation_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"suit_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"expand_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"replace_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"presale_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"presale_balance_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"group_purchase_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"reduce_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"seckill_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"cream_card_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"milk_card_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_card_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"gift_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"delivery_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"delivery_real_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"live_exclusive_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"live_sharing_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ppbc_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"psbc_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"qyzh_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"schd_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"qt_coupon_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   spu_id |   city_id |   large_area_id |    sku_cnt |
|:------|---------:|----------:|----------------:|-----------:|
| count | 10000    |   10000   |      10000      | 10000      |
| mean  |  1759.04 |   25693.9 |         35.2966 |     6.8799 |
| std   |  2633.63 |   15356.5 |         31.1315 |   120.044  |
| min   |    14    |    1001   |          1      |     1      |
| 25%   |   147    |   14343   |         14      |     1      |
| 50%   |  1572    |   24635   |         24      |     2      |
| 75%   |  1691    |   44123   |         72      |     4      |
| max   | 18782    |   44269   |         91      |  8260      |