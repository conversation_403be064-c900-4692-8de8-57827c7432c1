# app_saas_merchant_store_order_proportion_analysis_week_df
* comment: saas-门店订货占比分析-周
* last_data_modified_time: 2025-09-18 02:19:29

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_order_proportion_analysis_week_df` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `item_id` BIGINT COMMENT '商品id',
  `store_id` BIGINT COMMENT '门店id',
  `order_amount` BIGINT COMMENT '订货数量',
  `order_amount_proportion` DECIMAL(38,18) COMMENT '订货数量占比',
  `order_amount_proportion_upper_period` DECIMAL(38,18) COMMENT '订货数量占比环比',
  `order_price` DECIMAL(38,18) COMMENT '订货金额',
  `order_price_proportion` DECIMAL(38,18) COMMENT '订货金额占比',
  `order_price_proportion_upper_period` DECIMAL(38,18) COMMENT '订货金额占比环比',
  `total_order_amount` BIGINT COMMENT '总订货数量',
  `total_order_price` DECIMAL(38,18) COMMENT '总订货金额',
  `order_amount_upper_period` BIGINT COMMENT '上周期订货数量',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '上周期订货金额',
  `total_order_amount_upper_period` BIGINT COMMENT '上周期总订货数量',
  `total_order_price_upper_period` DECIMAL(38,18) COMMENT '上周期总订货金额'
)
COMMENT 'saas-门店订货占比分析-周'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"time_tag":{"0":"20220530","1":"20220530","2":"20220530","3":"20220613","4":"20220613"},"item_id":{"0":"1","1":"3","2":"9","3":"16","4":"17"},"store_id":{"0":"5","1":"3","2":"1","3":"1","4":"1"},"order_amount":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"order_amount_proportion":{"0":"0.3333333333333333","1":"0.3333333333333333","2":"0.3333333333333333","3":"0.1666666666666667","4":"0.1666666666666667"},"order_amount_proportion_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_price":{"0":"0.01","1":"0.02","2":"26","3":"27","4":"58"},"order_price_proportion":{"0":"0.000384172109104879","1":"0.000768344218209758","2":"0.998847483672685363","3":"0.157894736842105263","4":"0.339181286549707602"},"order_price_proportion_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"total_order_amount":{"0":"3","1":"3","2":"3","3":"6","4":"6"},"total_order_price":{"0":"26.03","1":"26.03","2":"26.03","3":"171","4":"171"},"order_amount_upper_period":{"0":"nan","1":"nan","2":"nan","3":"nan","4":"nan"},"order_price_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"total_order_amount_upper_period":{"0":"nan","1":"nan","2":"nan","3":"nan","4":"nan"},"total_order_price_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |    item_id |   store_id |   order_amount |   total_order_amount |   order_amount_upper_period |   total_order_amount_upper_period |
|:------|------------:|-----------:|-----------:|---------------:|---------------------:|----------------------------:|----------------------------------:|
| count | 10000       | 10000      |  10000     |    10000       |             10000    |                  3770       |                           3770    |
| mean  |     4.935   |   166.402  |    146.661 |        3.5405  |              2320.64 |                     4.38541 |                           2271.83 |
| std   |     1.00791 |    67.1476 |    110.254 |        5.41874 |              1047.69 |                     4.77304 |                           1058.72 |
| min   |     2       |     1      |      1     |        1       |                 1    |                     1       |                              6    |
| 25%   |     4       |   112      |     43     |        1       |              1375    |                     1       |                           1375    |
| 50%   |     4       |   162      |    118     |        2       |              2412    |                     3       |                           1863    |
| 75%   |     6       |   214.75   |    237     |        4       |              3176    |                     5.75    |                           2995    |
| max   |     8       |   398      |    419     |      250       |              4281    |                    70       |                           4281    |