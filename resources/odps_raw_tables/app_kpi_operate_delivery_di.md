# app_kpi_operate_delivery_di
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:40:09

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_delivery_di` (
  `date` STRING COMMENT '日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917"},"origin_total_amt":{"0":"3922935.6"},"real_total_amt":{"0":"3783091.111904761904761989"},"marketing_amt":{"0":"139844.488095238095238011"},"cost_amt":{"0":"3386109.43"},"origin_gross":{"0":"536826.17"},"real_gross":{"0":"396981.681904761904761989"},"origin_gross_margin":{"0":"0.136842972900192397"},"real_gross_margin":{"0":"0.104935797252021296"},"cust_cnt":{"0":"7374"},"point_cnt":{"0":"7658"},"origin_pre_cust_price":{"0":"531.995606183889340928"},"real_pre_cust_price":{"0":"513.031070233897735932"},"timing_origin_amt":{"0":"253286"},"timing_real_amt":{"0":"237279.261904761904761907"},"consign_origin_amt":{"0":"0"},"consign_real_amt":{"0":"0"},"consign_marketing_amt":{"0":"0"},"consign_origin_gross":{"0":"0"},"consign_real_gross":{"0":"0"},"consign_cust_cnt":{"0":"0"},"turnover_day_cnt":{"0":"14.054531142682171497"},"damage_amt":{"0":"5983.13"},"storage_amt":{"0":"81608.155426826096448224"},"arterial_roads_amt":{"0":"69441.984013813407864345"},"self_picked_amt":{"0":"0"},"allocation_amt":{"0":"13667.907418832355601167"},"other_amt":{"0":"10439.017586164564160042"},"deliver_amt":{"0":"255893.895626644070084217"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |          1 |           1 |                  1 |
| mean  |       7374 |        7658 |                  0 |
| std   |        nan |         nan |                nan |
| min   |       7374 |        7658 |                  0 |
| 25%   |       7374 |        7658 |                  0 |
| 50%   |       7374 |        7658 |                  0 |
| 75%   |       7374 |        7658 |                  0 |
| max   |       7374 |        7658 |                  0 |