# app_kpi_trade_wi
* comment: 交易口径kpi指标周汇总
* last_data_modified_time: 2025-09-18 02:53:01

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标周汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025"},"week_of_year":{"0":"38"},"monday":{"0":"20250915"},"sunday":{"0":"20250921"},"origin_total_amt":{"0":"11457680.75"},"real_total_amt":{"0":"11061150.33"},"cust_cnt":{"0":"18188"},"cust_arpu":{"0":"629.958255443149329228"},"order_cnt":{"0":"26220"},"order_avg":{"0":"436.982484744469870328"},"after_sale_noreceived_amt":{"0":"494849.37"},"after_sale_rate":{"0":"0.043189313858304177"},"dire_origin_total_amt":{"0":"0"},"delivery_amt":{"0":"44548.44"},"timing_origin_total_amt":{"0":"689484"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   order_cnt |
|:------|---------------:|-----------:|------------:|
| count |              1 |          1 |           1 |
| mean  |             38 |      18188 |       26220 |
| std   |            nan |        nan |         nan |
| min   |             38 |      18188 |       26220 |
| 25%   |             38 |      18188 |       26220 |
| 50%   |             38 |      18188 |       26220 |
| 75%   |             38 |      18188 |       26220 |
| max   |             38 |      18188 |       26220 |