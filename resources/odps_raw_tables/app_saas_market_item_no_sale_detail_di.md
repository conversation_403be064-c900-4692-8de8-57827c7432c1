# app_saas_market_item_no_sale_detail_di
* comment: saas商品滞销明细
* last_data_modified_time: 2025-09-18 02:18:36

# schema:
CREATE TABLE summerfarm_tech.`app_saas_market_item_no_sale_detail_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `type` BIGINT COMMENT '类型：1、7日滞销；2、30日滞销',
  `item_id` BIGINT COMMENT '商品id',
  `sale_price` DECIMAL(38,18) COMMENT '售价'
)
COMMENT 'saas商品滞销明细'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"type":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"item_id":{"0":"2","1":"4","2":"5","3":"6","4":"9"},"sale_price":{"0":"0.02","1":"550","2":"550","3":"100","4":"0.1"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |         type |   item_id |
|:------|------------:|-------------:|----------:|
| count |  10000      | 10000        |   10000   |
| mean  |     55.834  |     1.8774   |   23370.7 |
| std   |     33.2964 |     0.327994 |   12303.3 |
| min   |      2      |     1        |       2   |
| 25%   |     37      |     2        |   15433   |
| 50%   |     53      |     2        |   23371.5 |
| 75%   |     85      |     2        |   33793.8 |
| max   |    123      |     2        |   44901   |