# app_self_category_delivery_warehouse_kpi_mi
* comment: 履约口径kpi指标日汇总(自营)
* last_data_modified_time: 2025-09-18 03:40:22

# schema:
CREATE TABLE summerfarm_tech.`app_self_category_delivery_warehouse_kpi_mi` (
  `month` STRING COMMENT '月份',
  `category` STRING COMMENT '鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `day_point_cnt` BIGINT COMMENT '日均点位',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费',
  `cust_runoff` DECIMAL(38,18) COMMENT '客户流失率',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
  `damage_rate` DECIMAL(38,18) COMMENT '货损占比',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
)
COMMENT '履约口径kpi指标日汇总(自营)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509"},"category":{"0":"其他","1":"乳制品","2":"鲜果"},"origin_total_amt":{"0":"11377779.27","1":"36106439.57","2":"17572701.19"},"real_total_amt":{"0":"10899463.425454545454545485","1":"35080862.208000000000000249","2":"17069646.600000000000000309"},"cost_amt":{"0":"9231686.12","1":"33649358.3","2":"13183106.49"},"timing_origin_total_amt":{"0":"1031243","1":"2476140","2":"0"},"timing_real_total_amt":{"0":"953791.315454545454545436","1":"2318594.94799999999999999","2":"0"},"cust_cnt":{"0":"22548","1":"23756","2":"30617"},"order_cnt":{"0":"46229","1":"50017","2":"103067"},"point_cnt":{"0":"24037","1":"25345","2":"32740"},"day_point_cnt":{"0":"44324","1":"48228","2":"94657"},"sku_cnt":{"0":"113375","1":"119193","2":"373788"},"delivery_amt":{"0":"11232.81","1":"30332.94","2":"92225.39"},"cust_runoff":{"0":"0.1376271655632194","1":"0.1348922126255179","2":"0.1338579555590795"},"after_sale_received_amt":{"0":"20864.11","1":"52886.81","2":"88845.94"},"inventory_loss_amt":{"0":"6318.58","1":"283","2":"226.44"},"inventory_profit_amt":{"0":"6183.75","1":"0","2":"226.44"},"damage_amt":{"0":"13859.45","1":"10287.36","2":"105392.01"},"damage_rate":{"0":"0.001218115562897539","1":"0.000284917597041264","2":"0.00599748489776716"},"storage_amt":{"0":"352989.602737755814395623","1":"459892.407012859327101188","2":"515044.132954204015735539"},"arterial_roads_amt":{"0":"279461.776033824678715474","1":"359500.921104312188464936","2":"403476.472098018678211122"},"deliver_amt":{"0":"1022428.015361174202247449","1":"1339576.026434126280151853","2":"1619835.361551071182245128"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"other_amt":{"0":"41212.976779168223517989","1":"55067.735438510574273514","2":"69840.505622293277820843"},"allocation_amt":{"0":"75544.940005634513050713","1":"97754.823942993538728532","2":"85368.205285483830177307"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   point_cnt |   day_point_cnt |   sku_cnt |
|:------|-----------:|------------:|------------:|----------------:|----------:|
| count |       3    |         3   |        3    |             3   |         3 |
| mean  |   25640.3  |     66437.7 |    27374    |         62403   |    202119 |
| std   |    4352.04 |     31778.4 |     4692.89 |         28000.9 |    148698 |
| min   |   22548    |     46229   |    24037    |         44324   |    113375 |
| 25%   |   23152    |     48123   |    24691    |         46276   |    116284 |
| 50%   |   23756    |     50017   |    25345    |         48228   |    119193 |
| 75%   |   27186.5  |     76542   |    29042.5  |         71442.5 |    246490 |
| max   |   30617    |    103067   |    32740    |         94657   |    373788 |