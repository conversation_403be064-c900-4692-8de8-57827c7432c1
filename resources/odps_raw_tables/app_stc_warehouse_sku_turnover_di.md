# app_stc_warehouse_sku_turnover_di
* comment: 库存仓+sku 近30天库存周转
* last_data_modified_time: 2025-09-18 02:12:09

# schema:
CREATE TABLE summerfarm_tech.`app_stc_warehouse_sku_turnover_di` (
  `warehouse_no` BIGINT COMMENT '库存仓编号',
  `warehouse_name` STRING COMMENT '库存仓名',
  `sku_id` STRING COMMENT 'sku编号',
  `sku_type` STRING COMMENT '自营，代仓',
  `spu_id` BIGINT COMMENT 'pd_id',
  `spu_no` STRING COMMENT 'spu编号',
  `spu_name` STRING COMMENT '商品名',
  `sku_disc` STRING COMMENT '商品描述',
  `category1` STRING COMMENT '一级类目',
  `category2` STRING COMMENT '二级类目',
  `sku_property` STRING COMMENT '常规,活动,临保,拆包,破袋',
  `sku_life` STRING COMMENT '上新处理中,使用中，已删除',
  `sku_core_type` STRING COMMENT '核心，非核心',
  `sale_quantity` BIGINT COMMENT '当日销量数量',
  `sale_cost` DECIMAL(38,18) COMMENT '当日销量成本',
  `inventory_quantity` BIGINT COMMENT '当日库存数量',
  `inventory_cost` DECIMAL(38,18) COMMENT '当日库存成本',
  `nearest_7_days_sale_quantity` BIGINT COMMENT '近7天销量数量',
  `nearest_7_days_total_sale_cost` DECIMAL(38,18) COMMENT '近7天总销量成本',
  `nearest_7_days_inventory_quantity` BIGINT COMMENT '近7天库存数量',
  `nearest_7_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近7天总库存成本',
  `nearest_30_days_sale_quantity` BIGINT COMMENT '近30天销量数量',
  `nearest_30_days_total_sale_cost` DECIMAL(38,18) COMMENT '近30天总销量成本',
  `nearest_30_days_inventory_quantity` BIGINT COMMENT '近30天库存数量',
  `nearest_30_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近30天总库存成本',
  `month_sale_quality` BIGINT COMMENT '当月销量',
  `month_total_sale_cost` DECIMAL(38,18) COMMENT '当月总销量成本',
  `month_inventory_quantity` BIGINT COMMENT '当月库存数量',
  `month_total_inventory_cost` DECIMAL(38,18) COMMENT '当月总库存成本',
  `nearest_14_days_inventory_quantity` BIGINT COMMENT '近14天库存数量',
  `nearest_14_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近14天总库存成本',
  `nearest_14_days_sale_quantity` BIGINT COMMENT '近30天销量数量',
  `nearest_14_days_total_sale_cost` DECIMAL(38,18) COMMENT '近30天总销量成本'
)
COMMENT '库存仓+sku 近30天库存周转'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"warehouse_no":{"0":"2","1":"2","2":"2","3":"2","4":"2","5":"2","6":"2","7":"2","8":"2","9":"2"},"warehouse_name":{"0":"上海总仓","1":"上海总仓","2":"上海总仓","3":"上海总仓","4":"上海总仓","5":"上海总仓","6":"上海总仓","7":"上海总仓","8":"上海总仓","9":"上海总仓"},"sku_id":{"0":"1003572460835","1":"1005125236575","2":"1007150250266","3":"1007284863502","4":"1017143724678","5":"1049228883875","6":"1049280842681","7":"1049508022485","8":"1049577336267","9":"1053345175150"},"sku_type":{"0":"自营","1":"代仓","2":"代仓","3":"代仓","4":"代仓","5":"代仓","6":"代仓","7":"代仓","8":"代仓","9":"代仓"},"spu_id":{"0":"3798","1":"14730","2":"10701","3":"10702","4":"16158","5":"11191","6":"17497","7":"17496","8":"12247","9":"13642"},"spu_no":{"0":"1003572460","1":"1005125236","2":"1007150250","3":"1007284863","4":"1017143724","5":"1049228883","6":"1049280842","7":"1049508022","8":"1049577336","9":"1053345175"},"spu_name":{"0":"安佳片状乳酸黄油","1":"浸渍粉","2":"椰子水(冷冻)","3":"生椰乳(冷冻)","4":"牛角包酱料","5":"纸浆双杯托（可拆）-现货","6":"四杯托","7":"双杯托","8":"纸浆四杯托-可撕","9":"手握冰淇淋杯\n（无锤纹）300ML"},"sku_disc":{"0":"1KG*20包","1":"500g*20包","2":"1箱*12瓶","3":"1箱*12瓶","4":"250g*1袋","5":"50个*12包","6":"400个*1箱","7":"600个*1箱","8":"20个*15盒","9":"50只*10条"},"category1":{"0":"乳制品","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"category2":{"0":"乳制品","1":"其他","2":"饮料","3":"饮料","4":"调味品","5":"包材","6":"包材","7":"包材","8":"包材","9":"包材"},"sku_property":{"0":"常规","1":"常规","2":"常规","3":"常规","4":"常规","5":"常规","6":"常规","7":"常规","8":"常规","9":"常规"},"sku_life":{"0":"使用中","1":"使用中","2":"使用中","3":"使用中","4":"使用中","5":"使用中","6":"使用中","7":"使用中","8":"使用中","9":"使用中"},"sku_core_type":{"0":"非核心","1":"非核心","2":"非核心","3":"非核心","4":"非核心","5":"非核心","6":"非核心","7":"非核心","8":"非核心","9":"非核心"},"sale_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"7","5":"1","6":"0","7":"5","8":"0","9":"0"},"sale_cost":{"0":"0","1":"0","2":"0","3":"0","4":"154","5":"150","6":"0","7":"1110","8":"0","9":"0"},"inventory_quantity":{"0":"10","1":"10","2":"37","3":"44","4":"47","5":"45","6":"2","7":"20","8":"19","9":"4"},"inventory_cost":{"0":"15300","1":"2900","2":"4440","3":"5544","4":"956","5":"6750","6":"400","7":"4440","8":"2280","9":"980"},"nearest_7_days_sale_quantity":{"0":"4","1":"2","2":"0","3":"0","4":"38","5":"7","6":"1","7":"12","8":"1","9":"0"},"nearest_7_days_total_sale_cost":{"0":"6120","1":"380","2":"0","3":"0","4":"1100","5":"1050","6":"200","7":"2664","8":"120","9":"0"},"nearest_7_days_inventory_quantity":{"0":"35","1":"80","2":"259","3":"308","4":"447","5":"223","6":"16","7":"198","8":"108","9":"28"},"nearest_7_days_total_inventory_cost":{"0":"53550","1":"22200","2":"31080","3":"38808","4":"10982","5":"33450","6":"3200","7":"43956","8":"12960","9":"6860"},"nearest_30_days_sale_quantity":{"0":"19","1":"3","2":"0","3":"0","4":"225","5":"34","6":"2","7":"60","8":"8","9":"0"},"nearest_30_days_total_sale_cost":{"0":"29070","1":"570","2":"0","3":"0","4":"17764","5":"5640","6":"400","7":"13320","8":"960","9":"0"},"nearest_30_days_inventory_quantity":{"0":"229","1":"252","2":"1110","3":"1320","4":"2658","5":"760","6":"103","7":"1494","8":"411","9":"120"},"nearest_30_days_total_inventory_cost":{"0":"350370","1":"66880","2":"133200","3":"166320","4":"160438","5":"115260","6":"20600","7":"331668","8":"49320","9":"29400"},"month_sale_quality":{"0":"13","1":"2","2":"0","3":"0","4":"110","5":"16","6":"2","7":"39","8":"4","9":"0"},"month_total_sale_cost":{"0":"19890","1":"380","2":"0","3":"0","4":"3058","5":"2400","6":"400","7":"8658","8":"480","9":"0"},"month_inventory_quantity":{"0":"124","1":"200","2":"629","3":"748","4":"1272","5":"388","6":"51","7":"614","8":"220","9":"68"},"month_total_inventory_cost":{"0":"189720","1":"55000","2":"75480","3":"94248","4":"34490","5":"58200","6":"10200","7":"136308","8":"26400","9":"16660"},"nearest_14_days_inventory_quantity":{"0":"95","1":"164","2":"518","3":"616","4":"962","5":"328","6":"39","7":"469","8":"184","9":"56"},"nearest_14_days_total_inventory_cost":{"0":"145350","1":"45160","2":"62160","3":"77616","4":"26056","5":"49200","6":"7800","7":"104118","8":"22080","9":"13720"},"nearest_14_days_sale_quantity":{"0":"11","1":"2","2":"0","3":"0","4":"93","5":"14","6":"2","7":"23","8":"2","9":"0"},"nearest_14_days_total_sale_cost":{"0":"16830","1":"380","2":"0","3":"0","4":"2684","5":"2100","6":"400","7":"5106","8":"240","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   spu_id |   sale_quantity |   inventory_quantity |   nearest_7_days_sale_quantity |   nearest_7_days_inventory_quantity |   nearest_30_days_sale_quantity |   nearest_30_days_inventory_quantity |   month_sale_quality |   month_inventory_quantity |   nearest_14_days_inventory_quantity |   nearest_14_days_sale_quantity |
|:------|---------------:|---------:|----------------:|---------------------:|-------------------------------:|------------------------------------:|--------------------------------:|-------------------------------------:|---------------------:|---------------------------:|-------------------------------------:|--------------------------------:|
| count |     10000      | 10000    |      10000      |            10000     |                     10000      |                     10000           |                       10000     |                      10000           |           10000      |            10000           |                      10000           |                      10000      |
| mean  |        59.7539 |  6005.29 |          4.5051 |              222.935 |                        27.2408 |                      1643.27        |                         125.333 |                       6906.78        |              66.5757 |             3976.26        |                       3302.91        |                         55.2911 |
| std   |        44.6111 |  5714.71 |         25.0866 |             6771.42  |                       120.239  |                     47545.3         |                         553.868 |                     204257           |             295.934  |           115794           |                      95375.9         |                        240.272  |
| min   |         2      |     6    |          0      |                0     |                         0      |                         0           |                           0     |                          0           |               0      |                0           |                          0           |                          0      |
| 25%   |        24      |  1536    |          0      |                2     |                         0      |                        17.75        |                           1     |                         89           |               0      |               47           |                         38           |                          0      |
| 50%   |        48      |  3710    |          0      |                8     |                         2      |                        59           |                          10.5   |                        256           |               5      |              144           |                        119           |                          4      |
| 75%   |        69      | 10661.2  |          2      |               24     |                        11      |                       173.25        |                          55     |                        720           |              28      |              410           |                        347           |                         23      |
| max   |       155      | 18910    |       1303      |           500560     |                      3990      |                         3.50392e+06 |                       21453     |                          1.51426e+07 |           10952      |                8.56423e+06 |                          7.04614e+06 |                       8270      |