# app_saas_goods_no_sale_summary_di
* comment: saas近15天滞销货品汇总
* last_data_modified_time: 2025-09-18 02:04:42

# schema:
CREATE TABLE summerfarm_tech.`app_saas_goods_no_sale_summary_di` (
  `time_tag` STRING COMMENT '日期',
  `tenant_id` BIGINT COMMENT 'sku租户id',
  `sku_id` BIGINT COMMENT 'saas skuId',
  `item_id` BIGINT COMMENT '商品id',
  `sale_price` DECIMAL(38,18) COMMENT '售价'
)
COMMENT 'saas近15天滞销货品汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"tenant_id":{"0":"38","1":"38","2":"38","3":"22","4":"99"},"sku_id":{"0":"113091","1":"110881","2":"106100","3":"110592","4":"118445"},"item_id":{"0":"24269.0","1":"20673.0","2":"nan","3":"19638.0","4":"32235.0"},"sale_price":{"0":"None","1":"None","2":"None","3":"24","4":"1"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |    sku_id |   item_id |
|:------|------------:|----------:|----------:|
| count |   4240      |   4240    |    3306   |
| mean  |     51.1425 | 112322    |   22800.6 |
| std   |     38.1739 |   7727.61 |   13950.8 |
| min   |      2      | 100039    |      40   |
| 25%   |     11      | 102930    |   13048.2 |
| 50%   |     45      | 114302    |   26723   |
| 75%   |     88      | 118337    |   32200.8 |
| max   |    118      | 126031    |   44884   |