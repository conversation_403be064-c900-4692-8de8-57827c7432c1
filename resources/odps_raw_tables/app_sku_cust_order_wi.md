# app_sku_cust_order_wi
* comment: 区域渗透数据
* last_data_modified_time: 2025-09-18 02:27:42

# schema:
CREATE TABLE summerfarm_tech.`app_sku_cust_order_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `sku_id` STRING COMMENT 'sku编号',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品描述',
  `cust_type` STRING COMMENT '客户业态',
  `cust_cnt` BIGINT COMMENT '交易客户数',
  `group_cust_cnt` BIGINT COMMENT '业态总客户数'
)
COMMENT '区域渗透数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025","5":"2025","6":"2025","7":"2025","8":"2025","9":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38","5":"38","6":"38","7":"38","8":"38","9":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915","5":"20250915","6":"20250915","7":"20250915","8":"20250915","9":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921","5":"20250921","6":"20250921","7":"20250921","8":"20250921","9":"20250921"},"sku_id":{"0":"1003074364015","1":"1003572460835","2":"1007565354171","3":"1007565354578","4":"100803","5":"102228","6":"102254","7":"102530","8":"103162","9":"103764"},"spu_name":{"0":"ProtagxEva乳酸黄油","1":"安佳片状乳酸黄油","2":"Protag常温生椰乳","3":"Protag常温生椰乳","4":"奥利奥中号饼干碎(无夹心)","5":"海南小金桔","6":"海南小金桔","7":"海南小金桔","8":"无籽青柠檬","9":"无籽青柠檬"},"sku_disc":{"0":"10KG*1箱","1":"1KG*20包","2":"1L*12盒","3":"1L*1盒","4":"400g*1包","5":"净重5-5.3斤\/一级\/8-13g","6":"3斤*1包\/二级\/标准规格(轻微烧皮)","7":"2斤*1包\/一级\/标准规格","8":"5斤*1箱\/其他\/快递\/茉莉奶白专用","9":"8斤*1箱\/其他\/茉莉奶白城配专用"},"cust_type":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"cust_cnt":{"0":"1","1":"3","2":"3","3":"5","4":"25","5":"6","6":"1","7":"34","8":"1","9":"5"},"group_cust_cnt":{"0":"4752","1":"4752","2":"4752","3":"4752","4":"4752","5":"4752","6":"4752","7":"4752","8":"4752","9":"4752"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   group_cust_cnt |
|:------|---------------:|-----------:|-----------------:|
| count |           4554 |  4554      |          4554    |
| mean  |             38 |    10.9616 |          9410.36 |
| std   |              0 |    40.5321 |          7540.86 |
| min   |             38 |     1      |             2    |
| 25%   |             38 |     1      |          3400    |
| 50%   |             38 |     3      |          4752    |
| 75%   |             38 |     8      |         14709    |
| max   |             38 |  1472      |         21241    |