# app_sale_large_area_kpi_trade_wi
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:47:26

# schema:
CREATE TABLE summerfarm_tech.`app_sale_large_area_kpi_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `large_area_name` STRING COMMENT '运营服务大区',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"large_area_name":{"0":"昆明快递大区","1":"广东一点点快递区域","2":"上海大区","3":"长沙大区","4":"青岛大区"},"order_origin_total_amt":{"0":"1858","1":"942","2":"999234.11","3":"1193523.51","4":"374531.76"},"order_real_total_amt":{"0":"1838","1":"942","2":"971052.26","3":"1147761.14","4":"360070.12"},"order_cust_cnt":{"0":"2","1":"1","2":"1294","3":"2039","4":"561"},"order_cust_arpu":{"0":"929","1":"942","2":"772.205649149922720247","3":"585.347479156449239823","4":"667.614545454545454545"},"order_cnt":{"0":"2","1":"1","2":"2228","3":"2873","4":"744"},"delivery_origin_total_amt":{"0":"788","1":"0","2":"977358.8","3":"1309435.55","4":"313373.62"},"delivery_real_total_amt":{"0":"788","1":"0","2":"950226.685000000000000002","3":"1269154.376666666666666674","4":"301716.340000000000000013"},"delivery_cust_cnt":{"0":"1","1":"0","2":"1318","3":"2406","4":"526"},"delivery_origin_profit":{"0":"41.83","1":"0","2":"143736.38","3":"191672.05","4":"34821.25"},"delivery_real_profit":{"0":"41.83","1":"0","2":"116604.265000000000000002","3":"151390.876666666666666674","4":"23163.970000000000000013"},"delivery_after_profit":{"0":"41.83","1":"0","2":"28331.477221395976949885","3":"-54609.133745653753569322","4":"7019.193321377204442735"},"delivery_days_avg":{"0":"1","1":"0","2":"1.333839150227618","3":"1.192851205320033","4":"1.114068441064639"},"delivery_point_cnt":{"0":"1","1":"0","2":"1940","3":"2927","4":"608"},"delivery_amt":{"0":"0","1":"0","2":"88272.787778604023050117","3":"206000.010412320420235996","4":"16144.776678622795557278"},"new_delivery_origin_total_amt":{"0":"0","1":"0","2":"24270.33","3":"56435.13","4":"31995.05"},"new_delivery_real_total_amt":{"0":"0","1":"0","2":"23340.230000000000000001","3":"54456.310000000000000001","4":"31656.549999999999999999"},"new_delivery_cust_cnt":{"0":"0","1":"0","2":"49","3":"129","4":"33"},"new_delivery_real_profit":{"0":"0","1":"0","2":"3042.830000000000000001","3":"5817.940000000000000001","4":"953.909999999999999999"},"old_delivery_origin_total_amt":{"0":"788","1":"0","2":"953088.47","3":"1253000.42","4":"281378.57"},"old_delivery_real_total_amt":{"0":"788","1":"0","2":"926886.455000000000000001","3":"1214698.066666666666666673","4":"270059.790000000000000014"},"old_delivery_cust_cnt":{"0":"1","1":"0","2":"1269","3":"2277","4":"493"},"old_delivery_real_profit":{"0":"41.83","1":"0","2":"113561.435000000000000001","3":"145572.936666666666666673","4":"22210.060000000000000014"},"order_sku_cnt":{"0":"3","1":"1","2":"557","3":"454","4":"248"},"order_sku_weight":{"0":"48.7","1":"25.4","2":"60834.45","3":"72773.01999999996","4":"18313.07000000001"},"delivery_sku_cnt":{"0":"2","1":"0","2":"556","3":"472","4":"234"},"delivery_sku_weight":{"0":"24.7","1":"0","2":"60331.11000000002","3":"79206.83999999997","4":"16654.02"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   order_cust_cnt |   order_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|---------------:|-----------------:|------------:|--------------------:|---------------------:|------------------------:|------------------------:|----------------:|-------------------:|
| count |             16 |            16    |       16    |               16    |                16    |                 16      |                   16    |          16     |             16     |
| mean  |             38 |          1113.5  |     1604.62 |             1138.19 |              1432.44 |                 52      |                 1086.19 |         330.812 |            329     |
| std   |              0 |          1149.12 |     1672.26 |             1188.57 |              1500.82 |                 53.9852 |                 1136.23 |         218.183 |            221.024 |
| min   |             38 |             1    |        1    |                0    |                 0    |                  0      |                    0    |           1     |              0     |
| 25%   |             38 |           242.5  |      329    |              197.5  |               225.75 |                 14.5    |                  183    |         158     |            129.25  |
| 50%   |             38 |           824.5  |     1164    |              791.5  |               957    |                 42.5    |                  750    |         337     |            343.5   |
| 75%   |             38 |          1434    |     2241.25 |             1429    |              1966.25 |                 50      |                 1382.75 |         479.25  |            491.5   |
| max   |             38 |          3705    |     5597    |             3735    |              4892    |                170      |                 3575    |         694     |            701     |