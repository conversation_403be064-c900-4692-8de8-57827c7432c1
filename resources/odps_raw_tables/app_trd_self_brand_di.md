# app_trd_self_brand_di
* comment: 自营品牌数据
* last_data_modified_time: 2025-09-18 03:21:43

# schema:
CREATE TABLE summerfarm_tech.`app_trd_self_brand_di` (
  `date` STRING COMMENT '日期',
  `brand_name` STRING COMMENT '品牌名称',
  `large_area_name` STRING COMMENT '运营大区',
  `m3_name` STRING COMMENT 'M3',
  `m2_name` STRING COMMENT 'M2',
  `m1_name` STRING COMMENT 'M1',
  `cust_type` STRING COMMENT '客户业态',
  `cust_group` STRING COMMENT '客户类型',
  `cust_id` BIGINT COMMENT '客户ID',
  `first_buy_date` STRING COMMENT '商品维度首次购买时间',
  `category1` STRING COMMENT '一级类目',
  `category2` STRING COMMENT '二级类目',
  `category3` STRING COMMENT '三级类目',
  `category4` STRING COMMENT '四级类目',
  `spu_name` STRING COMMENT '商品名称',
  `sku_id` STRING COMMENT 'sku',
  `sku_disc` STRING COMMENT '商品规格',
  `orgin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV',
  `trade_sku_cnt` BIGINT COMMENT '交易sku数',
  `dlv_orgin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `dlv_sku_cnt` BIGINT COMMENT '履约sku数',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本'
)
COMMENT '自营品牌数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"brand_name":{"0":"Protag蛋白标签","1":"Protag蛋白标签","2":"Protag蛋白标签","3":"Protag蛋白标签","4":"Protag蛋白标签","5":"Protag蛋白标签","6":"Protag蛋白标签","7":"Protag蛋白标签","8":"Protag蛋白标签","9":"C味"},"large_area_name":{"0":"上海大区","1":"福州大区","2":"广州大区","3":"上海大区","4":"武汉大区","5":"杭州大区","6":"苏州大区","7":"重庆大区","8":"青岛大区","9":"武汉大区"},"m3_name":{"0":"吕建杰","1":"孙日达","2":"李茂源","3":"吕建杰","4":"吕建杰","5":"吕建杰","6":"孙日达","7":"孙日达","8":"吕建杰","9":"吕建杰"},"m2_name":{"0":"赵奎","1":"林金秋","2":"李茂源","3":"赵奎","4":"彭琨","5":"翟远方","6":"桂少达","7":"姜浪","8":"孙军杰","9":"彭琨"},"m1_name":{"0":"陈露露","1":"张浩亮","2":"李茂源","3":"徐晟昊","4":"唐宽","5":"翟远方","6":"冯朝皇","7":"汪林俊","8":"陈忠良","9":"唐宽"},"cust_type":{"0":"甜品冰淇淋","1":"茶饮","2":"咖啡","3":"咖啡","4":"面包蛋糕","5":"咖啡","6":"面包蛋糕","7":"茶饮","8":"茶饮","9":"茶饮"},"cust_group":{"0":"平台客户","1":"平台客户","2":"平台客户","3":"平台客户","4":"平台客户","5":"平台客户","6":"平台客户","7":"平台客户","8":"平台客户","9":"平台客户"},"cust_id":{"0":"10029","1":"121422","2":"385002","3":"420541","4":"431695","5":"486410","6":"98947","7":"353871","8":"530499","9":"106626"},"first_buy_date":{"0":"20220821","1":"20240111","2":"20240101","3":"20240110","4":"20240820","5":"20250812","6":"20250916","7":"20240701","8":"20250916","9":"20211026"},"category1":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"category2":{"0":"饮料","1":"饮料","2":"饮料","3":"饮料","4":"饮料","5":"饮料","6":"饮料","7":"饮料","8":"饮料","9":"成品原料"},"category3":{"0":"植物蛋白饮料","1":"植物蛋白饮料","2":"植物蛋白饮料","3":"植物蛋白饮料","4":"植物蛋白饮料","5":"植物蛋白饮料","6":"植物蛋白饮料","7":"植物蛋白饮料","8":"植物蛋白饮料","9":"果冻类配料"},"category4":{"0":"其他植物蛋白饮料","1":"其他植物蛋白饮料","2":"其他植物蛋白饮料","3":"其他植物蛋白饮料","4":"其他植物蛋白饮料","5":"其他植物蛋白饮料","6":"其他植物蛋白饮料","7":"其他植物蛋白饮料","8":"其他植物蛋白饮料","9":"波波丨晶球"},"spu_name":{"0":"Protag常温生椰乳","1":"Protag常温生椰乳","2":"Protag常温生椰乳","3":"Protag常温生椰乳","4":"Protag常温生椰乳","5":"Protag常温生椰乳","6":"Protag常温生椰乳","7":"Protag常温生椰乳","8":"Protag常温生椰乳","9":"C味原味波波晶球"},"sku_id":{"0":"1007565354171","1":"1007565354171","2":"1007565354171","3":"1007565354171","4":"1007565354171","5":"1007565354171","6":"1007565354578","7":"1007565354578","8":"1007565354578","9":"16680730631"},"sku_disc":{"0":"1L*12盒","1":"1L*12盒","2":"1L*12盒","3":"1L*12盒","4":"1L*12盒","5":"1L*12盒","6":"1L*1盒","7":"1L*1盒","8":"1L*1盒","9":"1KG*12包"},"orgin_total_amt":{"0":"0","1":"133","2":"133","3":"133","4":"133","5":"0","6":"0","7":"0","8":"0","9":"92"},"real_total_amt":{"0":"0","1":"115","2":"120","3":"125","4":"120","5":"0","6":"0","7":"0","8":"0","9":"87"},"trade_sku_cnt":{"0":"0","1":"1","2":"1","3":"1","4":"1","5":"0","6":"0","7":"0","8":"0","9":"1"},"dlv_orgin_total_amt":{"0":"266","1":"0","2":"0","3":"0","4":"0","5":"266","6":"13","7":"13","8":"13","9":"0"},"dlv_real_total_amt":{"0":"238.5","1":"0","2":"0","3":"0","4":"0","5":"240","6":"6.43","7":"13","8":"7.9","9":"0"},"dlv_sku_cnt":{"0":"2","1":"0","2":"0","3":"0","4":"0","5":"2","6":"1","7":"1","8":"1","9":"0"},"cost_amt":{"0":"177.6","1":"0","2":"0","3":"0","4":"0","5":"177.6","6":"7.5","7":"7.4","8":"7.4","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_id |   trade_sku_cnt |   dlv_sku_cnt |
|:------|----------:|----------------:|--------------:|
| count |      4469 |      4469       |    4469       |
| mean  |    353488 |         1.08548 |       1.12508 |
| std   |    169169 |         2.44681 |       2.39166 |
| min   |        99 |         0       |       0       |
| 25%   |    223879 |         0       |       0       |
| 50%   |    389029 |         0       |       1       |
| 75%   |    500194 |         1       |       1       |
| max   |    575004 |        70       |     100       |