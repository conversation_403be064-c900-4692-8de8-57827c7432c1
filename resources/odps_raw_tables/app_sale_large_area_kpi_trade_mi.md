# app_sale_large_area_kpi_trade_mi
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:47:56

# schema:
CREATE TABLE summerfarm_tech.`app_sale_large_area_kpi_trade_mi` (
  `month` STRING COMMENT '月份',
  `large_area_name` STRING COMMENT '运营服务大区',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"large_area_name":{"0":"广州大区","1":"武汉大区","2":"贵阳大区","3":"苏南大区","4":"广东一点点快递区域"},"order_origin_total_amt":{"0":"15306625.89","1":"3601254.04","2":"522630.14","3":"5109422.3","4":"2826"},"order_real_total_amt":{"0":"14742973.32","1":"3494122.76","2":"510596.67","3":"4959576.25","4":"2826"},"order_cust_cnt":{"0":"9662","1":"2226","2":"325","3":"3647","4":"2"},"order_cust_arpu":{"0":"1584.208848064582902091","1":"1617.814034141958670261","2":"1608.092738461538461538","3":"1400.993227310117905128","4":"1413"},"order_cnt":{"0":"29618","1":"7218","2":"775","3":"13698","4":"3"},"delivery_origin_total_amt":{"0":"14597754.78","1":"3446842.99","2":"482409.44","3":"4920415.1","4":"1884"},"delivery_real_total_amt":{"0":"14066364.88111111111111114","1":"3344795.350216450216450241","2":"471709.060000000000000002","3":"4781731.240000000000000072","4":"1884"},"delivery_cust_cnt":{"0":"9598","1":"2239","2":"326","3":"3677","4":"2"},"delivery_origin_profit":{"0":"1761488.08","1":"408749.66","2":"40742.25","3":"833462.33","4":"25.32"},"delivery_real_profit":{"0":"1230098.18111111111111114","1":"306702.020216450216450241","2":"30041.870000000000000002","3":"694778.470000000000000072","4":"25.32"},"delivery_after_profit":{"0":"-50930.395807893290525721","1":"-311756.867788943172182455","2":"966.759037898993367757","3":"308829.194860601231728379","4":"25.32"},"delivery_days_avg":{"0":"2.597832881850386","1":"2.782492184010719","2":"2.092024539877301","3":"3.221104161000816","4":"1"},"delivery_point_cnt":{"0":"25772","1":"6533","2":"698","3":"12230","4":"2"},"delivery_amt":{"0":"1281028.576919004401636861","1":"618458.888005393388632696","2":"29075.110962101006632245","3":"385949.275139398768271693","4":"0"},"new_delivery_origin_total_amt":{"0":"246287.54","1":"52997.4","2":"9399.52","3":"58210.31","4":"942"},"new_delivery_real_total_amt":{"0":"231836.830000000000000006","1":"50756.060000000000000007","2":"8989.520000000000000001","3":"55793.009999999999999997","4":"942"},"new_delivery_cust_cnt":{"0":"490","1":"112","2":"24","3":"107","4":"1"},"new_delivery_real_profit":{"0":"25816.040000000000000006","1":"5549.530000000000000007","2":"636.620000000000000001","3":"6722.529999999999999997","4":"12.94"},"old_delivery_origin_total_amt":{"0":"14351467.24","1":"3393845.59","2":"473009.92","3":"4862204.79","4":"942"},"old_delivery_real_total_amt":{"0":"13834528.051111111111111134","1":"3294039.290216450216450234","2":"462719.540000000000000001","3":"4725938.230000000000000075","4":"942"},"old_delivery_cust_cnt":{"0":"9108","1":"2127","2":"302","3":"3570","4":"1"},"old_delivery_real_profit":{"0":"1204282.141111111111111134","1":"301152.490216450216450234","2":"29405.250000000000000001","3":"688055.940000000000000075","4":"12.38"},"order_sku_cnt":{"0":"810","1":"473","2":"187","3":"782","4":"1"},"order_sku_weight":{"0":"823012.4999999988","1":"183007.34","2":"22367.20999999999","3":"319030.12","4":"76.19999999999999"},"delivery_sku_cnt":{"0":"809","1":"468","2":"187","3":"787","4":"1"},"delivery_sku_weight":{"0":"802654.9799999968","1":"179936.2100000001","2":"21397.35000000001","3":"310564.1799999999","4":"50.8"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   order_cust_cnt |   order_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|-----------------:|------------:|--------------------:|---------------------:|------------------------:|------------------------:|----------------:|-------------------:|
| count |            17    |       17    |               17    |                17    |                  17     |                   17    |          17     |             17     |
| mean  |          2635.18 |     8924.59 |             2631.71 |              7803.12 |                 133     |                 2498.71 |         453.647 |            454.471 |
| std   |          2843.97 |     9856.33 |             2840.28 |              8599.19 |                 143.372 |                 2701.41 |         293.327 |            293.896 |
| min   |             2    |        2    |                2    |                 2    |                   1     |                    0    |           1     |              1     |
| 25%   |           325    |      775    |              326    |               698    |                  24     |                  306    |         187     |            187     |
| 50%   |          2178    |     6664    |             2168    |              5716    |                 108     |                 2060    |         501     |            505     |
| 75%   |          3647    |    12700    |             3677    |             10949    |                 142     |                 3570    |         663     |            655     |
| max   |          9662    |    32297    |             9598    |             28131    |                 490     |                 9108    |         898     |            906     |