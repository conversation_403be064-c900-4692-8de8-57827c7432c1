# app_kpi_cust_trade_wi
* comment: 交易口径kpi指标周汇总
* last_data_modified_time: 2025-09-18 02:53:16

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_cust_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `cust_class` STRING COMMENT '客户类型:大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标周汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025"},"week_of_year":{"0":"38","1":"38","2":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921"},"cust_class":{"0":"Mars大客户","1":"平台客户","2":"集团大客户（茶百道）"},"origin_total_amt":{"0":"122665.99","1":"11324368.66","2":"10646.1"},"real_total_amt":{"0":"122587.99","1":"10928100.35","2":"10461.99"},"cust_cnt":{"0":"332","1":"17816","2":"40"},"cust_arpu":{"0":"369.475873493975903614","1":"635.629134485855410867","2":"266.1525"},"order_cnt":{"0":"493","1":"25674","2":"53"},"order_avg":{"0":"248.81539553752535497","1":"441.083144815766923736","2":"200.869811320754716981"},"after_sale_noreceived_amt":{"0":"1176.32","1":"492694.23","2":"978.82"},"after_sale_rate":{"0":"0.009589618116643415","1":"0.043507434700558397","2":"0.091941649993894478"},"dire_origin_total_amt":{"0":"0","1":"0","2":"0"},"delivery_amt":{"0":"920","1":"43568.44","2":"60"},"timing_origin_total_amt":{"0":"0","1":"689484","2":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   order_cnt |
|:------|---------------:|-----------:|------------:|
| count |              3 |       3    |         3   |
| mean  |             38 |    6062.67 |      8740   |
| std   |              0 |   10179.7  |     14666.9 |
| min   |             38 |      40    |        53   |
| 25%   |             38 |     186    |       273   |
| 50%   |             38 |     332    |       493   |
| 75%   |             38 |    9074    |     13083.5 |
| max   |             38 |   17816    |     25674   |