# app_log_search_di
* comment: 搜索数据
* last_data_modified_time: 2025-09-18 02:38:46

# schema:
CREATE TABLE summerfarm_tech.`app_log_search_di` (
  `date` STRING COMMENT '日期',
  `search_name` STRING COMMENT '搜索词',
  `search_cnt` BIGINT COMMENT '搜索次数',
  `search_cust_cnt` BIGINT COMMENT '搜索客户数'
)
COMMENT '搜索数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250916"},"search_name":{"0":"芒果"},"search_cnt":{"0":"2"},"search_cust_cnt":{"0":"1"},"ds":{"0":"20250916"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   search_cnt |   search_cust_cnt |
|:------|-------------:|------------------:|
| count |            1 |                 1 |
| mean  |            2 |                 1 |
| std   |          nan |               nan |
| min   |            2 |                 1 |
| 25%   |            2 |                 1 |
| 50%   |            2 |                 1 |
| 75%   |            2 |                 1 |
| max   |            2 |                 1 |