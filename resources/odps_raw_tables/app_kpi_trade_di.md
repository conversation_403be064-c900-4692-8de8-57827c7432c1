# app_kpi_trade_di
* comment: 交易口径kpi指标日汇总
* last_data_modified_time: 2025-09-18 02:53:01

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_trade_di` (
  `date` STRING COMMENT '日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917"},"origin_total_amt":{"0":"3798612.25"},"real_total_amt":{"0":"3662927.78"},"cust_cnt":{"0":"7286"},"cust_arpu":{"0":"521.357706560527038155"},"order_cnt":{"0":"8611"},"order_avg":{"0":"441.134856578794565091"},"after_sale_noreceived_amt":{"0":"190914.9"},"after_sale_rate":{"0":"0.050259117655401654"},"dire_origin_total_amt":{"0":"0"},"delivery_amt":{"0":"15001.18"},"timing_origin_total_amt":{"0":"209173"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |
|:------|-----------:|------------:|
| count |          1 |           1 |
| mean  |       7286 |        8611 |
| std   |        nan |         nan |
| min   |       7286 |        8611 |
| 25%   |       7286 |        8611 |
| 50%   |       7286 |        8611 |
| 75%   |       7286 |        8611 |
| max   |       7286 |        8611 |