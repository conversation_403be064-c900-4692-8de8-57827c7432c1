# app_stc_warehouse_batch_sku_cost_df
* comment: 批次成本明细
* last_data_modified_time: 2025-09-18 01:59:50

# schema:
CREATE TABLE summerfarm_tech.`app_stc_warehouse_batch_sku_cost_df` (
  `date` STRING COMMENT '时间',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '仓库',
  `batch_no` STRING COMMENT '采购号',
  `product_batch_no` STRING COMMENT '生产批次',
  `sku_id` STRING COMMENT '商品编码',
  `sku_disc` STRING COMMENT '商品规格',
  `spu_name` STRING COMMENT '商品名称',
  `category1` STRING COMMENT '一级类目',
  `sku_property` STRING COMMENT 'sku性质：常规，活动，临保，拆包，破袋',
  `sku_sale_unit` DECIMAL(38,18) COMMENT '当前售价',
  `store_quantity` BIGINT COMMENT '库存量',
  `batch_quantity` BIGINT COMMENT '批次量',
  `batch_self_cost` DECIMAL(38,18) COMMENT '批次自提成本',
  `batch_allocate_cost` DECIMAL(38,18) COMMENT '批次调拨成本',
  `batch_1_quantity` BIGINT COMMENT '批次1库存量',
  `batch_1_self_cost` DECIMAL(38,18) COMMENT '批次1自提成本',
  `batch_1_allocate_cost` DECIMAL(38,18) COMMENT '批次1调拨成本',
  `batch_2_quantity` BIGINT COMMENT '批次2库存量',
  `batch_2_self_cost` DECIMAL(38,18) COMMENT '批次2自提成本',
  `batch_2_allocate_cost` DECIMAL(38,18) COMMENT '批次2调拨成本',
  `batch_3_quantity` BIGINT COMMENT '批次3库存量',
  `batch_3_self_cost` DECIMAL(38,18) COMMENT '批次3自提成本',
  `batch_3_allocate_cost` DECIMAL(38,18) COMMENT '批次3调拨成本',
  `batch_4_quantity` BIGINT COMMENT '批次4库存量',
  `batch_4_self_cost` DECIMAL(38,18) COMMENT '批次4自提成本',
  `batch_4_allocate_cost` DECIMAL(38,18) COMMENT '批次4调拨成本',
  `batch_5_quantity` BIGINT COMMENT '批次5库存量',
  `batch_5_self_cost` DECIMAL(38,18) COMMENT '批次5自提成本',
  `batch_5_allocate_cost` DECIMAL(38,18) COMMENT '批次5调拨成本',
  `batch_6_quantity` BIGINT COMMENT '批次6库存量',
  `batch_6_self_cost` DECIMAL(38,18) COMMENT '批次6自提成本',
  `batch_6_allocate_cost` DECIMAL(38,18) COMMENT '批次6调拨成本',
  `total_purchase_cost` DECIMAL(38,18) COMMENT '总采购单价',
  `total_unit_cost` DECIMAL(38,18) COMMENT '总单价',
  `total_cost` DECIMAL(38,18) COMMENT '总成本',
  `total_self_cost` DECIMAL(38,18) COMMENT '总自提单价',
  `total_allocate_cost` DECIMAL(38,18) COMMENT '总调拨单价'
)
COMMENT '批次成本明细'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"2025-02-27","1":"2025-07-07","2":"2025-07-26","3":"2025-08-06","4":"2025-08-08","5":"2025-08-17","6":"2025-08-24","7":"2025-08-28","8":"2025-08-28","9":"2025-09-01"},"warehouse_no":{"0":"63","1":"29","2":"121","3":"117","4":"38","5":"48","6":"48","7":"64","8":"145","9":"24"},"warehouse_name":{"0":"贵阳总仓","1":"重庆总仓","2":"嘉兴海盐总仓","3":"东莞冷冻总仓","4":"福州总仓","5":"长沙总仓","6":"长沙总仓","7":"青岛总仓","8":"济南总仓","9":"华西总仓"},"batch_no":{"0":"202412040485304133","1":"202504160485515188","2":"202507240485474135","3":"202507240485474135","4":"202507240485474135","5":"202507240485474135","6":"202507240485474135","7":"202507240485474135","8":"202507240485474135","9":"202507240485474135"},"product_batch_no":{"0":"917493","1":"1020015","2":"1097779","3":"1109467","4":"1111517","5":"1121588","6":"1121588","7":"1133783","8":"1133956","9":"1139586"},"sku_id":{"0":"1003074364015","1":"1003074364015","2":"1003074364015","3":"1003074364015","4":"1003074364015","5":"1003074364015","6":"1003074364015","7":"1003074364015","8":"1003074364015","9":"1003074364015"},"sku_disc":{"0":"10KG*1箱","1":"10KG*1箱","2":"10KG*1箱","3":"10KG*1箱","4":"10KG*1箱","5":"10KG*1箱","6":"10KG*1箱","7":"10KG*1箱","8":"10KG*1箱","9":"10KG*1箱"},"spu_name":{"0":"ProtagxEva乳酸黄油","1":"ProtagxEva乳酸黄油","2":"ProtagxEva乳酸黄油","3":"ProtagxEva乳酸黄油","4":"ProtagxEva乳酸黄油","5":"ProtagxEva乳酸黄油","6":"ProtagxEva乳酸黄油","7":"ProtagxEva乳酸黄油","8":"ProtagxEva乳酸黄油","9":"ProtagxEva乳酸黄油"},"category1":{"0":"乳制品","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品","5":"乳制品","6":"乳制品","7":"乳制品","8":"乳制品","9":"乳制品"},"sku_property":{"0":"常规","1":"常规","2":"常规","3":"常规","4":"常规","5":"常规","6":"常规","7":"常规","8":"常规","9":"常规"},"sku_sale_unit":{"0":"472","1":"472","2":"472","3":"472","4":"472","5":"472","6":"472","7":"472","8":"472","9":"472"},"store_quantity":{"0":"1","1":"2","2":"171","3":"87","4":"2","5":"3","6":"3","7":"4","8":"1","9":"1"},"batch_quantity":{"0":"4","1":"10","2":"0","3":"200","4":"5","5":"10","6":"3","7":"6","8":"2","9":"5"},"batch_self_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_allocate_cost":{"0":"0.01","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_1_quantity":{"0":"200","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_1_self_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_1_allocate_cost":{"0":"11.25","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_2_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_2_self_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_2_allocate_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_3_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_3_self_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_3_allocate_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_4_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_4_self_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_4_allocate_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_5_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_5_self_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_5_allocate_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_6_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_6_self_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"batch_6_allocate_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"total_purchase_cost":{"0":"335","1":"335","2":"350","3":"350","4":"350","5":"350","6":"350","7":"350","8":"350","9":"350"},"total_unit_cost":{"0":"346.26","1":"335","2":"350","3":"350","4":"350","5":"350","6":"350","7":"350","8":"350","9":"350"},"total_cost":{"0":"346.26","1":"670","2":"59850","3":"30450","4":"700","5":"1050","6":"1050","7":"1400","8":"350","9":"350"},"total_self_cost":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"total_allocate_cost":{"0":"11.26","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   store_quantity |   batch_quantity |   batch_1_quantity |   batch_2_quantity |   batch_3_quantity |   batch_4_quantity |   batch_5_quantity |   batch_6_quantity |
|:------|---------------:|-----------------:|-----------------:|-------------------:|-------------------:|-------------------:|-------------------:|-------------------:|-------------------:|
| count |     10000      |        10000     |       10000      |         10000      |        10000       |       10000        |         10000      |         10000      |              10000 |
| mean  |        67.7722 |          205.839 |           5.3461 |             1.9423 |            0.1749  |           0.013    |             0.0001 |             0.0001 |                  0 |
| std   |        45.0748 |         6735.19  |          23.7623 |            17.6987 |            3.55257 |           0.727382 |             0.01   |             0.01   |                  0 |
| min   |         2      |            1     |           0      |             0      |            0       |           0        |             0      |             0      |                  0 |
| 25%   |        29      |            2     |           0      |             0      |            0       |           0        |             0      |             0      |                  0 |
| 50%   |        60      |            6     |           0      |             0      |            0       |           0        |             0      |             0      |                  0 |
| 75%   |       117      |           18     |           3      |             0      |            0       |           0        |             0      |             0      |                  0 |
| max   |       155      |       500560     |        1000      |           604      |          200       |          60        |             1      |             1      |                  0 |