# app_saas_stock_turnover_summary_di
* comment: saas近30天库存周转天数汇总
* last_data_modified_time: 2025-09-18 01:19:28

# schema:
CREATE TABLE summerfarm_tech.`app_saas_stock_turnover_summary_di` (
  `time_tag` STRING COMMENT '日期',
  `tenant_id` BIGINT COMMENT 'sku租户id',
  `sku_id` BIGINT COMMENT 'saas skuId',
  `warehouse_no` BIGINT COMMENT '库存仓ID',
  `warehouse_name` STRING COMMENT '仓库名称',
  `turnover_days` DECIMAL(38,18) COMMENT '近30天库存周转天数',
  `opening_quantity` BIGINT COMMENT '期初库存',
  `ending_quantity` BIGINT COMMENT '期末库存',
  `sale_out_quantity` BIGINT COMMENT '销售出库数量(包含自提)'
)
COMMENT 'saas近30天库存周转天数汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"tenant_id":{"0":"38","1":"38","2":"38","3":"4","4":"4"},"sku_id":{"0":"106073.0","1":"106629.0","2":"106070.0","3":"102320.0","4":"102164.0"},"warehouse_no":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"warehouse_name":{"0":"杭州总仓","1":"杭州总仓","2":"杭州总仓","3":"杭州总仓","4":"杭州总仓"},"turnover_days":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"opening_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ending_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"sale_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   sku_id |   warehouse_no |   opening_quantity |   ending_quantity |   sale_out_quantity |
|:------|------------:|---------:|---------------:|-------------------:|------------------:|--------------------:|
| count |   5939      |   5938   |      5939      |     5939           |    5939           |            5939     |
| mean  |     57.3418 | 113424   |        68.7326 |      578.498       |     586.106       |              12.147 |
| std   |     35.2605 |   7932.9 |        56.7128 |    19263.4         |   19169.4         |             114.016 |
| min   |      2      | 100039   |         1      |        0           |       0           |               0     |
| 25%   |     32      | 106123   |        10      |        0           |       0           |               0     |
| 50%   |     60      | 115045   |        66      |        0           |       0           |               0     |
| 75%   |     85      | 120367   |       119      |       13           |      15           |               0     |
| max   |    118      | 126024   |       176      |        1.06517e+06 |       1.06008e+06 |            5274     |