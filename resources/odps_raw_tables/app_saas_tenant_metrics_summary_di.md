# app_saas_tenant_metrics_summary_di
* comment: saas租户指标汇总
* last_data_modified_time: 2025-09-18 02:35:39

# schema:
CREATE TABLE summerfarm_tech.`app_saas_tenant_metrics_summary_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `no_sale_item_rate_7d` DECIMAL(38,18) COMMENT '7日滞销率（连续未发生付款的商品数与所有上架中的商品数的占比）',
  `no_sale_item_rate_30d` DECIMAL(38,18) COMMENT '30日滞销率',
  `store_purchase_rate_7d` DECIMAL(38,18) COMMENT '7日采购活跃率（有过下单的门店数 / 经营中的总门店数）',
  `store_purchase_rate_30d` DECIMAL(38,18) COMMENT '30日采购活跃率',
  `refund_and_returns_rate_1d` DECIMAL(38,18) COMMENT '昨天已到货退款率（已到货退款&退货退款金额占比）',
  `refund_and_returns_rate_7d` DECIMAL(38,18) COMMENT '7日已到货退款率',
  `refund_and_returns_rate_30d` DECIMAL(38,18) COMMENT '30日已到货退款率',
  `order_fulfillment_rate_1d` DECIMAL(38,18) COMMENT '次日履约率（近30天订单次日发货数 / 30天总订单数）',
  `order_fulfillment_rate_3d` DECIMAL(38,18) COMMENT '3日履约率',
  `order_fulfillment_rate_7d` DECIMAL(38,18) COMMENT '7日履约率',
  `stock_turnover_days_30d` DECIMAL(38,18) COMMENT '近30天库存周转天数',
  `no_sale_goods_num_15d` BIGINT COMMENT '近15天滞销货品数',
  `near_deadline_goods_num_15d` BIGINT COMMENT '近15天临期货品数',
  `expired_goods_num_30d` BIGINT COMMENT '近30天过期货品数',
  `supplier_on_time_delivery_rate_30d` DECIMAL(38,18) COMMENT '近30天供应商到仓准时率',
  `supplier_to_warehouse_accuracy_30d` DECIMAL(38,18) COMMENT '近30天供应商到仓准确率',
  `damaged_goods_out_of_warehouse_num_7d` BIGINT COMMENT '近7天货损出库的商品件数',
  `stock_accuracy_7d` DECIMAL(38,18) COMMENT '近7天库存准确率',
  `outbound_accuracy_7d` DECIMAL(38,18) COMMENT '近7天出库准确率',
  `outbound_timeliness_rate_7d` DECIMAL(38,18) COMMENT '近7天出库及时率',
  `inbound_timeliness_rate_7d` DECIMAL(38,18) COMMENT '近7天入库及时率'
)
COMMENT 'saas租户指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"95","1":"12","2":"109","3":"35","4":"94"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"no_sale_item_rate_7d":{"0":"21.05263157894737","1":"100","2":"51.57232704402516","3":"83.01886792452831","4":"0"},"no_sale_item_rate_30d":{"0":"21.05263157894737","1":"100","2":"38.9937106918239","3":"77.35849056603774","4":"0"},"store_purchase_rate_7d":{"0":"41.0958904109589","1":"0","2":"19.64285714285714","3":"22.46376811594203","4":"0"},"store_purchase_rate_30d":{"0":"41.0958904109589","1":"0","2":"19.64285714285714","3":"28.26086956521739","4":"0"},"refund_and_returns_rate_1d":{"0":"5.2582489328783207","1":"0","2":"0","3":"0","4":"0"},"refund_and_returns_rate_7d":{"0":"4.0636342050812388","1":"0","2":"0","3":"2.0143480472200659","4":"0"},"refund_and_returns_rate_30d":{"0":"36.4155482115959623","1":"0","2":"0","3":"2.9483435809907668","4":"0"},"order_fulfillment_rate_1d":{"0":"85.07462686567165","1":"0","2":"49.65277777777778","3":"100","4":"0"},"order_fulfillment_rate_3d":{"0":"85.07462686567165","1":"0","2":"50.34722222222222","3":"100","4":"0"},"order_fulfillment_rate_7d":{"0":"85.07462686567165","1":"0","2":"50.34722222222222","3":"100","4":"0"},"stock_turnover_days_30d":{"0":"1076.84","1":"None","2":"311.25","3":"None","4":"None"},"no_sale_goods_num_15d":{"0":"45","1":"97","2":"184","3":"0","4":"95"},"near_deadline_goods_num_15d":{"0":"0","1":"0","2":"2","3":"0","4":"0"},"expired_goods_num_30d":{"0":"0","1":"0","2":"4","3":"0","4":"0"},"supplier_on_time_delivery_rate_30d":{"0":"97.3","1":"None","2":"None","3":"None","4":"None"},"supplier_to_warehouse_accuracy_30d":{"0":"99.06","1":"None","2":"None","3":"None","4":"None"},"damaged_goods_out_of_warehouse_num_7d":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_accuracy_7d":{"0":"132.41","1":"100","2":"100","3":"100","4":"100"},"outbound_accuracy_7d":{"0":"100","1":"None","2":"100","3":"None","4":"None"},"outbound_timeliness_rate_7d":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"inbound_timeliness_rate_7d":{"0":"96.97","1":"None","2":"100","3":"None","4":"None"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   no_sale_goods_num_15d |   near_deadline_goods_num_15d |   expired_goods_num_30d |   damaged_goods_out_of_warehouse_num_7d |
|:------|------------:|------------------------:|------------------------------:|------------------------:|----------------------------------------:|
| count |     90      |                 90      |                     90        |               90        |                                      90 |
| mean  |     66.6222 |                 39.7    |                      0.2      |                0.455556 |                                       0 |
| std   |     35.4542 |                 73.6615 |                      0.863752 |                2.99873  |                                       0 |
| min   |      2      |                  0      |                      0        |                0        |                                       0 |
| 25%   |     40.5    |                  0      |                      0        |                0        |                                       0 |
| 50%   |     68.5    |                  0      |                      0        |                0        |                                       0 |
| 75%   |     96.75   |                 57.25   |                      0        |                0        |                                       0 |
| max   |    123      |                367      |                      7        |               28        |                                       0 |