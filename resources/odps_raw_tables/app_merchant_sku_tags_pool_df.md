# app_merchant_sku_tags_pool_df
* comment: 圈人平台用户标签池表
* last_data_modified_time: 2025-09-18 03:52:58

# schema:
CREATE TABLE summerfarm_tech.`app_merchant_sku_tags_pool_df` (
  `cust_id` BIGINT COMMENT '客户ID',
  `tag_value` STRING COMMENT '未购买sku_id'
)
COMMENT '圈人平台用户标签池表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc
LIFECYCLE 60

# head data:
{"cust_id":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"tag_value":{"0":"1006626538755","1":"1009737314863","2":"1030126080653","3":"10378145870","4":"1038153030722"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |    cust_id |
|:------|-----------:|
| count | 10000      |
| mean  |    26.9605 |
| std   |    15.084  |
| min   |     1      |
| 25%   |    14      |
| 50%   |    27      |
| 75%   |    40      |
| max   |    53      |