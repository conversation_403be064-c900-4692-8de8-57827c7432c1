# app_sale_large_area_category_kpi_trade_wi
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:48:03

# schema:
CREATE TABLE summerfarm_tech.`app_sale_large_area_category_kpi_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `large_area_name` STRING COMMENT '运营服务大区',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"large_area_name":{"0":"昆明大区","1":"福州大区","2":"长沙大区","3":"广东一点点快递区域","4":"广州大区"},"category":{"0":"乳制品","1":"鲜果","2":"乳制品","3":"乳制品","4":"乳制品"},"order_origin_total_amt":{"0":"101664","1":"107662.87","2":"609135.72","3":"942","4":"1528120.9"},"order_real_total_amt":{"0":"98359.82","1":"99875.78","2":"585392.42","3":"942","4":"1490407.42"},"order_cust_cnt":{"0":"65","1":"503","2":"636","3":"1","4":"1543"},"order_cust_arpu":{"0":"1564.061538461538461538","1":"214.041491053677932406","2":"957.760566037735849057","3":"942","4":"990.357031756318859365"},"order_cnt":{"0":"78","1":"654","2":"760","3":"1","4":"1827"},"delivery_origin_total_amt":{"0":"94929","1":"113190.95","2":"666581.4","3":"0","4":"1464287.97"},"delivery_real_total_amt":{"0":"92781.89","1":"107160.370000000000000001","2":"644603.146666666666666668","3":"0","4":"1423988.85111111111111112"},"delivery_cust_cnt":{"0":"66","1":"519","2":"782","3":"0","4":"1514"},"delivery_origin_profit":{"0":"5331.51","1":"25328.12","2":"42793.35","3":"0","4":"95026.04"},"delivery_real_profit":{"0":"3184.4","1":"19297.540000000000000001","2":"20815.096666666666666668","3":"0","4":"54726.92111111111111112"},"delivery_after_profit":{"0":"507.852741028724758338","1":"5441.520740026392334887","2":"-43832.583738304706103691","3":"0","4":"-32755.009075880898954724"},"delivery_days_avg":{"0":"1.045454545454545","1":"1.157996146435453","2":"1.09079283887468","3":"0","4":"1.097093791281374"},"delivery_point_cnt":{"0":"70","1":"625","2":"859","3":"0","4":"1719"},"delivery_amt":{"0":"2676.547258971275241662","1":"13856.019259973607665114","2":"64647.680404971372770359","3":"0","4":"87481.930186992010065844"},"new_delivery_origin_total_amt":{"0":"317","1":"5412","2":"29689","3":"0","4":"32654.64"},"new_delivery_real_total_amt":{"0":"295","1":"5211.19","2":"28723.060000000000000001","3":"0","4":"31483.520000000000000001"},"new_delivery_cust_cnt":{"0":"1","1":"29","2":"39","3":"0","4":"51"},"new_delivery_real_profit":{"0":"14.83","1":"876.21","2":"1117.870000000000000001","3":"0","4":"2247.280000000000000001"},"old_delivery_origin_total_amt":{"0":"94612","1":"107778.95","2":"636892.4","3":"0","4":"1431633.33"},"old_delivery_real_total_amt":{"0":"92486.89","1":"101949.180000000000000001","2":"615880.086666666666666667","3":"0","4":"1392505.331111111111111119"},"old_delivery_cust_cnt":{"0":"65","1":"490","2":"743","3":"0","4":"1463"},"old_delivery_real_profit":{"0":"3169.57","1":"18421.330000000000000001","2":"19697.226666666666666667","3":"0","4":"52479.641111111111111119"},"order_sku_cnt":{"0":"27","1":"99","2":"86","3":"1","4":"108"},"order_sku_weight":{"0":"3359.69","1":"8329.73","2":"21066.87","3":"25.4","4":"49240.60000000001"},"delivery_sku_cnt":{"0":"31","1":"102","2":"88","3":"0","4":"103"},"delivery_sku_weight":{"0":"2436.71","1":"8365.800000000003","2":"22801.14999999998","3":"0","4":"50030.17000000003"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   order_cust_cnt |   order_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|---------------:|-----------------:|------------:|--------------------:|---------------------:|------------------------:|------------------------:|----------------:|-------------------:|
| count |             44 |           44     |      44     |              44     |               44     |                 44      |                  44     |         44      |            44      |
| mean  |             38 |          588.159 |     753.295 |             604.091 |              717.341 |                 24.5    |                 579.591 |        120.295  |           119.636  |
| std   |              0 |          600.58  |     810.492 |             623.803 |              765.91  |                 27.4078 |                 597.756 |         86.7537 |            88.4647 |
| min   |             38 |            1     |       1     |               0     |                0     |                  0      |                   0     |          1      |             0      |
| 25%   |             38 |          194.5   |     231.5   |             174.5   |              187.5   |                  9.25   |                 163.75  |         59      |            53.5    |
| 50%   |             38 |          422.5   |     532     |             438.5   |              510     |                 13.5    |                 415     |         96.5    |            99.5    |
| 75%   |             38 |          638     |     838     |             741.5   |              868.5   |                 31.25   |                 705.5   |        177.75   |           173      |
| max   |             38 |         2596     |    3772     |            2632     |             3448     |                120      |                2526     |        356      |           359      |