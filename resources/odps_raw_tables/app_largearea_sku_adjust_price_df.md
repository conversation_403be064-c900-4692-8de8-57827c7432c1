# app_largearea_sku_adjust_price_df
* comment: 调价表
* last_data_modified_time: 2025-09-18 02:16:04

# schema:
CREATE TABLE summerfarm_tech.`app_largearea_sku_adjust_price_df` (
  `large_area_name` STRING COMMENT '大区名称',
  `sku_id` STRING COMMENT 'sku_id',
  `spu_name` STRING COMMENT '商品名称',
  `adjust_price_time` DATETIME COMMENT '调价时间',
  `original_price` DECIMAL(38,18) COMMENT '调价前价格',
  `price` DECIMAL(38,18) COMMENT '调价后价格',
  `max_sale_price` DECIMAL(38,18) COMMENT '近30天最高价格',
  `min_sale_price` DECIMAL(38,18) COMMENT '近30天最低价格'
)
COMMENT '调价表'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"large_area_name":{"0":"上海大区","1":"上海大区","2":"杭州大区","3":"上海大区","4":"上海大区"},"sku_id":{"0":"N001S01R002","1":"15074843338","2":"106306","3":"106306","4":"N001G01R005"},"spu_name":{"0":"爱乐薇(铁塔)淡奶油","1":"NZMP马苏里拉干酪(澳洲)","2":"安佳奶油奶酪20kg","3":"安佳奶油奶酪20kg","4":"安佳奶油奶酪5KG"},"adjust_price_time":{"0":"2025-09-10 13:09:10","1":"2025-09-10 14:02:32","2":"2025-09-12 13:56:43","3":"2025-09-12 13:56:43","4":"2025-09-12 13:57:32"},"original_price":{"0":"550","1":"970","2":"900","3":"900","4":"245"},"price":{"0":"535","1":"930","2":"880","3":"880","4":"235"},"max_sale_price":{"0":"550","1":"970","2":"940","3":"940","4":"260"},"min_sale_price":{"0":"535","1":"930","2":"880","3":"880","4":"235"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | adjust_price_time             |
|:------|:------------------------------|
| count | 272                           |
| mean  | 2025-08-19 01:08:26.944852992 |
| min   | 2025-07-20 16:03:45           |
| 25%   | 2025-08-08 11:06:13           |
| 50%   | 2025-08-16 10:01:33           |
| 75%   | 2025-09-02 11:51:44           |
| max   | 2025-09-17 20:41:24           |