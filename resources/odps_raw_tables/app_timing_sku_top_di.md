# app_timing_sku_top_di
* comment: 省心送top表
* last_data_modified_time: 2025-09-18 02:24:35

# schema:
CREATE TABLE summerfarm_tech.`app_timing_sku_top_di` (
  `sku` STRING COMMENT 'sku',
  `area_no` BIGINT COMMENT '运营服务城市编号',
  `order_sku_cnt` BIGINT COMMENT '上周商品销量',
  `order` BIGINT COMMENT '排序',
  `date_flag` STRING COMMENT '信号表时间'
)
COMMENT '省心送top表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"sku":{"0":"N001S01R002","1":"605386061640","2":"607164503701","3":"661210374451","4":"N001Q01C001","5":"L001A01A001","6":"776581274350","7":"777887102254","8":"58566471004","9":"699172462827"},"area_no":{"0":"1001","1":"1001","2":"1001","3":"1001","4":"1001","5":"1001","6":"1001","7":"1001","8":"1001","9":"1001"},"order_sku_cnt":{"0":"215","1":"50","2":"15","3":"10","4":"10","5":"10","6":"10","7":"10","8":"10","9":"10"},"order":{"0":"1","1":"2","2":"3","3":"4","4":"5","5":"6","6":"7","7":"8","8":"9","9":"10"},"date_flag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   area_no |   order_sku_cnt |     order |
|:------|----------:|----------------:|----------:|
| count |    468    |        468      | 468       |
| mean  |  23391.8  |         22.7564 |   6.56624 |
| std   |  15403.5  |         42.3537 |   7.57672 |
| min   |   1001    |          3      |   1       |
| 25%   |   9600.75 |          5      |   2       |
| 50%   |  19828.5  |         10      |   4       |
| 75%   |  43807    |         20      |   8       |
| max   |  44228    |        609      |  44       |