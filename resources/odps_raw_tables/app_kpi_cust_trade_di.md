# app_kpi_cust_trade_di
* comment: 交易口径kpi指标日汇总
* last_data_modified_time: 2025-09-18 02:52:23

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_cust_trade_di` (
  `date` STRING COMMENT '日期',
  `cust_class` STRING COMMENT '客户类型:大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917"},"cust_class":{"0":"Mars大客户","1":"平台客户","2":"集团大客户（茶百道）"},"origin_total_amt":{"0":"41115.46","1":"3754297.07","2":"3199.72"},"real_total_amt":{"0":"41096.46","1":"3618659.35","2":"3171.97"},"cust_cnt":{"0":"152","1":"7122","2":"12"},"cust_arpu":{"0":"270.496447368421052632","1":"527.140841055883178882","2":"266.643333333333333333"},"order_cnt":{"0":"156","1":"8437","2":"18"},"order_avg":{"0":"263.560641025641025641","1":"444.980096005689226028","2":"177.762222222222222222"},"after_sale_noreceived_amt":{"0":"912.12","1":"189822.78","2":"180"},"after_sale_rate":{"0":"0.022184355957588703","1":"0.050561470352691083","2":"0.056254922305701749"},"dire_origin_total_amt":{"0":"0","1":"0","2":"0"},"delivery_amt":{"0":"280","1":"14701.18","2":"20"},"timing_origin_total_amt":{"0":"0","1":"209173","2":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |
|:------|-----------:|------------:|
| count |       3    |        3    |
| mean  |    2428.67 |     2870.33 |
| std   |    4065.15 |     4821.37 |
| min   |      12    |       18    |
| 25%   |      82    |       87    |
| 50%   |     152    |      156    |
| 75%   |    3637    |     4296.5  |
| max   |    7122    |     8437    |