# app_saas_product_detail_sales_di
* comment: saas商品销售数据表
* last_data_modified_time: 2025-09-18 02:35:59

# schema:
CREATE TABLE summerfarm_tech.`app_saas_product_detail_sales_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `item_id` BIGINT COMMENT '商品编码',
  `sku_id` STRING COMMENT '商品skuid',
  `delivery_type` BIGINT COMMENT '仓储类型 0 自营仓 1 第三方仓',
  `warehouse_type` BIGINT COMMENT '商品类型 0 自营 2 经销',
  `title` STRING COMMENT '商品名称',
  `specification` STRING COMMENT '规格',
  `category` STRING COMMENT '类目',
  `brand_name` STRING COMMENT '品牌名称',
  `supply_price` DECIMAL(38,18) COMMENT '供应价',
  `price` DECIMAL(38,18) COMMENT '售价',
  `category_id` BIGINT COMMENT '三级类目id',
  `store_type` BIGINT COMMENT '门店类型：0、直营店 1、加盟店 2、托管店',
  `store_id` BIGINT COMMENT '门店id',
  `store_name` STRING COMMENT '门店名',
  `province` STRING COMMENT '省',
  `city` STRING COMMENT '市',
  `address` STRING COMMENT '地址信息:省+市',
  `sales_num` BIGINT COMMENT '销售数量',
  `sales_price` DECIMAL(38,18) COMMENT '销售额',
  `after_sale_num` BIGINT COMMENT '售后数量',
  `after_sale_price` DECIMAL(38,18) COMMENT '售后金额',
  `goods_type` BIGINT COMMENT '商品类型 0无货商品 1报价货品 2自营货品',
  `supplier_name` STRING COMMENT '供应商名称',
  `after_sale_unit` STRING COMMENT '售后单位',
  `after_sale_apply_price` DECIMAL(38,18) COMMENT '供应商退款申请金额',
  `after_sale_total_price` DECIMAL(38,18) COMMENT '供应商退款实际金额'
)
COMMENT 'saas商品销售数据表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"7","1":"7","2":"7","3":"7","4":"7"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"item_id":{"0":"32963","1":"32963","2":"32963","3":"32963","4":"32963"},"sku_id":{"0":"118049","1":"118049","2":"118049","3":"118049","4":"118049"},"delivery_type":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"warehouse_type":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"title":{"0":"生牛乳厚乳","1":"生牛乳厚乳","2":"生牛乳厚乳","3":"生牛乳厚乳","4":"生牛乳厚乳"},"specification":{"0":"1KG*12瓶","1":"1KG*12瓶","2":"1KG*12瓶","3":"1KG*12瓶","4":"1KG*12瓶"},"category":{"0":"乳制品\/液体乳\/常温牛奶","1":"乳制品\/液体乳\/常温牛奶","2":"乳制品\/液体乳\/常温牛奶","3":"乳制品\/液体乳\/常温牛奶","4":"乳制品\/液体乳\/常温牛奶"},"brand_name":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"supply_price":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"price":{"0":"240","1":"240","2":"240","3":"240","4":"240"},"category_id":{"0":"607.0","1":"607.0","2":"607.0","3":"607.0","4":"607.0"},"store_type":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"store_id":{"0":"390607","1":"390664","2":"402740","3":"409604","4":"421833"},"store_name":{"0":"WX浙江台州椒江宝龙店","1":"WX浙江嘉兴桐乡洲泉湘溪大道店","2":"WX温州平阳腾蛟店","3":"WX嘉兴秀洲马厍汇历史街区店","4":"WX浙江台州黄岩新前街店"},"province":{"0":"浙江","1":"浙江","2":"浙江","3":"浙江","4":"浙江"},"city":{"0":"台州市","1":"嘉兴市","2":"温州市","3":"嘉兴市","4":"台州市"},"address":{"0":"浙江省台州市","1":"浙江省嘉兴市","2":"浙江省温州市","3":"浙江省嘉兴市","4":"浙江省台州市"},"sales_num":{"0":"3","1":"5","2":"2","3":"3","4":"5"},"sales_price":{"0":"720","1":"1200","2":"480","3":"720","4":"1200"},"after_sale_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"after_sale_price":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"goods_type":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"supplier_name":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"after_sale_unit":{"0":"箱","1":"箱","2":"箱","3":"箱","4":"箱"},"after_sale_apply_price":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"after_sale_total_price":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   item_id |   warehouse_type |   category_id |   store_type |   store_id |   sales_num |   after_sale_num |   goods_type |
|:------|------------:|----------:|-----------------:|--------------:|-------------:|-----------:|------------:|-----------------:|-------------:|
| count |   2266      |    2266   |      2266        |      2017     |  2266        |       2266 |  2266       |        2266      |  2266        |
| mean  |     57.9254 |   30857.2 |         1.02295  |       660.721 |     0.689762 |     362801 |     2.36584 |          18.2701 |     1.30494  |
| std   |     38.3859 |   13990.6 |         0.492239 |       161.984 |     0.482315 |     205469 |     5.32045 |         200.88   |     0.657201 |
| min   |      7      |     279   |         0        |       526     |     0        |        410 |     0       |           0      |     0        |
| 25%   |     14      |   23353   |         1        |       536     |     0        |     359772 |     1       |           0      |     1        |
| 50%   |     58      |   35473   |         1        |       575     |     1        |     458160 |     1       |           0      |     1        |
| 75%   |     95      |   43100   |         1        |       780     |     1        |     518899 |     2       |           0      |     2        |
| max   |    123      |   44880   |         2        |      1127     |     2        |     543362 |   102       |        4368      |     2        |