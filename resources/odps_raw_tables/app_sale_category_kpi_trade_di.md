# app_sale_category_kpi_trade_di
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:47:30

# schema:
CREATE TABLE summerfarm_tech.`app_sale_category_kpi_trade_di` (
  `date` STRING COMMENT '日期',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917"},"category":{"0":"其他","1":"鲜果","2":"乳制品"},"order_origin_total_amt":{"0":"668073.34","1":"938576.36","2":"2147647.37"},"order_real_total_amt":{"0":"635835.64","1":"901398.17","2":"2081425.54"},"order_cust_cnt":{"0":"2354","1":"4815","2":"2680"},"order_cust_arpu":{"0":"283.803457943925233645","1":"194.927592938733125649","2":"801.360958955223880597"},"order_cnt":{"0":"2582","1":"5435","2":"2885"},"delivery_origin_total_amt":{"0":"656624.15","1":"1013098.88","2":"2253212.57"},"delivery_real_total_amt":{"0":"627009.606666666666666672","1":"973718.520000000000000046","2":"2182362.985238095238095271"},"delivery_cust_cnt":{"0":"2496","1":"5090","2":"2681"},"delivery_origin_profit":{"0":"118959.15","1":"263748.71","2":"154118.31"},"delivery_real_profit":{"0":"89344.606666666666666672","1":"224368.350000000000000046","2":"83268.725238095238095271"},"delivery_after_profit":{"0":"-16076.386077236169314833","1":"67639.341888292396432271","2":"-61525.308973578035397703"},"delivery_days_avg":{"0":"1","1":"1","2":"1"},"delivery_point_cnt":{"0":"2546","1":"5266","2":"2741"},"delivery_amt":{"0":"105420.992743902835981505","1":"156729.008111707603567775","2":"144794.034211673273492974"},"new_delivery_origin_total_amt":{"0":"19450.96","1":"38170.25","2":"82751.26"},"new_delivery_real_total_amt":{"0":"18451.880000000000000002","1":"36626.549999999999999999","2":"80581.230000000000000001"},"new_delivery_cust_cnt":{"0":"109","1":"219","2":"104"},"new_delivery_real_profit":{"0":"2532.330000000000000002","1":"7280.419999999999999999","2":"3565.870000000000000001"},"old_delivery_origin_total_amt":{"0":"637173.19","1":"974928.63","2":"2170461.31"},"old_delivery_real_total_amt":{"0":"608557.72666666666666667","1":"937091.970000000000000047","2":"2101781.75523809523809527"},"old_delivery_cust_cnt":{"0":"2387","1":"4871","2":"2577"},"old_delivery_real_profit":{"0":"86812.27666666666666667","1":"217087.930000000000000047","2":"79702.85523809523809527"},"order_sku_cnt":{"0":"419","1":"504","2":"136"},"order_sku_weight":{"0":"59035.33000000001","1":"78960.68000000001","2":"75671.06999999996"},"delivery_sku_cnt":{"0":"431","1":"486","2":"131"},"delivery_sku_weight":{"0":"57291.26000000002","1":"85573.88","2":"77815.3900000001"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   order_cust_cnt |   order_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|-----------------:|------------:|--------------------:|---------------------:|------------------------:|------------------------:|----------------:|-------------------:|
| count |             3    |        3    |                3    |                 3    |                     3   |                    3    |           3     |              3     |
| mean  |          3283    |     3634    |             3422.33 |              3517.67 |                   144   |                 3278.33 |         353     |            349.333 |
| std   |          1336.73 |     1567.05 |             1447.2  |              1517.24 |                    65   |                 1382.56 |         192.673 |            191.072 |
| min   |          2354    |     2582    |             2496    |              2546    |                   104   |                 2387    |         136     |            131     |
| 25%   |          2517    |     2733.5  |             2588.5  |              2643.5  |                   106.5 |                 2482    |         277.5   |            281     |
| 50%   |          2680    |     2885    |             2681    |              2741    |                   109   |                 2577    |         419     |            431     |
| 75%   |          3747.5  |     4160    |             3885.5  |              4003.5  |                   164   |                 3724    |         461.5   |            458.5   |
| max   |          4815    |     5435    |             5090    |              5266    |                   219   |                 4871    |         504     |            486     |