# app_pcs_direct_category_warehouse_purchase_kpi_di
* comment: 直采kpi
* last_data_modified_time: 2025-09-18 03:21:30

# schema:
CREATE TABLE summerfarm_tech.`app_pcs_direct_category_warehouse_purchase_kpi_di` (
  `date` STRING COMMENT '日期',
  `warehouse_no` BIGINT COMMENT '库存仓号',
  `warehouse_name` STRING COMMENT '库存仓',
  `category4` STRING COMMENT '四级类目',
  `direct_purchase_amt` DECIMAL(38,18) COMMENT '直采金额',
  `purchases_amt` DECIMAL(38,18) COMMENT '采购金额（非直采）',
  `cost_flow_amt` DECIMAL(38,18) COMMENT '成本浮动金额',
  `direct_delivery_origin_amt` DECIMAL(38,18) COMMENT '直采履约应付金额',
  `direct_delivery_real_amt` DECIMAL(38,18) COMMENT '直采履约实付金额',
  `direct_delivery_market_amt` DECIMAL(38,18) COMMENT '直采营销费用',
  `direct_delivery_cost_amt` DECIMAL(38,18) COMMENT '直采成本费用',
  `direct_init_amt` DECIMAL(38,18) COMMENT '直采期末库存金额',
  `direct_after_sale_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责售后金额',
  `direct_damage_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责货损金额'
)
COMMENT '直采kpi'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"warehouse_no":{"0":"62","1":"125","2":"62","3":"155","4":"150"},"warehouse_name":{"0":"苏州总仓","1":"南京总仓","2":"苏州总仓","3":"武汉总仓","4":"嘉兴水果批发总仓"},"category4":{"0":"西瓜","1":"柚","2":"奇异果丨猕猴桃","3":"橙","4":"恐龙蛋李"},"direct_purchase_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchases_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"cost_flow_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"direct_delivery_origin_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"direct_delivery_real_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"direct_delivery_market_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"direct_delivery_cost_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"direct_init_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"direct_after_sale_pcs_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"direct_damage_pcs_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |
|:------|---------------:|
| count |       273      |
| mean  |        79.2015 |
| std   |        53.9099 |
| min   |         2      |
| 25%   |        38      |
| 50%   |        64      |
| 75%   |       150      |
| max   |       155      |