# app_sku_area_cust_category_gross_margin_wi
* comment: 毛利数据sku维周表
* last_data_modified_time: 2025-09-18 03:35:35

# schema:
CREATE TABLE summerfarm_tech.`app_sku_area_cust_category_gross_margin_wi` (
  `monday` STRING COMMENT '周一',
  `sunday` STRING COMMENT '周天',
  `large_area_name` STRING COMMENT '服务大区',
  `warehouse_name` STRING COMMENT '库存仓名',
  `cust_class` STRING COMMENT '客户大类:大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
  `sku_id` STRING COMMENT 'sku编号',
  `sku_type` STRING COMMENT '自营 ， 代仓',
  `spu_name` STRING COMMENT '商品名称',
  `category_1` STRING COMMENT '一级类目',
  `category_2` STRING COMMENT '二级类目',
  `category_3` STRING COMMENT '三级类目',
  `category_4` STRING COMMENT '四级类目',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '总成本',
  `gross_margin` DECIMAL(38,18) COMMENT '毛利润',
  `sku_cnt` BIGINT COMMENT '配送数量',
  `origin_amt_beforew` DECIMAL(38,18) COMMENT '上周应付',
  `real_amt_beforew` DECIMAL(38,18) COMMENT '上周实付',
  `cost_amt_beforew` DECIMAL(38,18) COMMENT '上周总成本',
  `gross_margin_beforew` DECIMAL(38,18) COMMENT '上周毛利润',
  `sku_cnt_beforew` DECIMAL(38,18) COMMENT '上周配送数量'
)
COMMENT '毛利数据sku维周表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915","5":"20250915","6":"20250915","7":"20250915","8":"20250915","9":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921","5":"20250921","6":"20250921","7":"20250921","8":"20250921","9":"20250921"},"large_area_name":{"0":"武汉大区","1":"重庆大区","2":"广州大区","3":"重庆大区","4":"重庆大区","5":"上海大区","6":"重庆大区","7":"广州大区","8":"成都大区","9":"杭州大区"},"warehouse_name":{"0":"武汉总仓","1":"重庆总仓","2":"东莞总仓","3":"重庆总仓","4":"重庆总仓","5":"嘉兴总仓","6":"重庆总仓","7":"东莞总仓","8":"华西总仓","9":"嘉兴总仓"},"cust_class":{"0":"大客户","1":"大客户","2":"大客户","3":"大客户","4":"大客户","5":"大客户","6":"大客户","7":"大客户","8":"大客户","9":"大客户"},"sku_id":{"0":"1256524478","1":"1235674148","2":"N001G01R002","3":"N001G01R002","4":"106306","5":"Q001L01S001","6":"Q001L01S001","7":"607164503701","8":"814153876","9":"608083408143"},"sku_type":{"0":"自营","1":"自营","2":"自营","3":"自营","4":"自营","5":"自营","6":"自营","7":"自营","8":"自营","9":"自营"},"spu_name":{"0":"安佳再制切达奶酪_橙色","1":"百瑞酪车达芝士片(黄色)","2":"安佳奶油奶酪1KG","3":"安佳奶油奶酪1KG","4":"安佳奶油奶酪20kg","5":"琪雷萨马斯卡彭","6":"琪雷萨马斯卡彭","7":"Protag纯牛奶","8":"雀巢纯牛奶","9":"Protag冷藏鲜牛奶"},"category_1":{"0":"乳制品","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品","5":"乳制品","6":"乳制品","7":"乳制品","8":"乳制品","9":"乳制品"},"category_2":{"0":"乳制品","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品","5":"乳制品","6":"乳制品","7":"乳制品","8":"乳制品","9":"乳制品"},"category_3":{"0":"奶酪丨芝士","1":"奶酪丨芝士","2":"奶酪丨芝士","3":"奶酪丨芝士","4":"奶酪丨芝士","5":"奶酪丨芝士","6":"奶酪丨芝士","7":"液体乳","8":"液体乳","9":"液体乳"},"category_4":{"0":"切达再制干酪","1":"切达再制干酪","2":"奶油奶酪","3":"奶油奶酪","4":"奶油奶酪","5":"马斯卡彭","6":"马斯卡彭","7":"常温牛奶","8":"常温牛奶","9":"鲜牛奶"},"origin_total_amt":{"0":"130","1":"58","2":"224","3":"108","4":"940","5":"238","6":"500","7":"225","8":"129","9":"200"},"real_total_amt":{"0":"130","1":"54.22","2":"224","3":"108","4":"940","5":"238","6":"495.77","7":"225","8":"129","9":"200"},"cost_amt":{"0":"117.6","1":"49.9","2":"196.68","3":"98.34","4":"798","5":"216","6":"432","7":"177","8":"97","9":"172.36"},"gross_margin":{"0":"12.4","1":"8.1","2":"27.32","3":"9.66","4":"142","5":"22","6":"68","7":"48","8":"32","9":"27.64"},"sku_cnt":{"0":"2","1":"1","2":"4","3":"2","4":"1","5":"1","6":"2","7":"3","8":"1","9":"2"},"origin_amt_beforew":{"0":"130","1":"0","2":"1570","3":"165","4":"0","5":"0","6":"500","7":"300","8":"387","9":"0"},"real_amt_beforew":{"0":"130","1":"0","2":"1570","3":"165","4":"0","5":"0","6":"500","7":"300","8":"387","9":"0"},"cost_amt_beforew":{"0":"117.6","1":"0","2":"1376.76","3":"147.51","4":"0","5":"0","6":"486","7":"236","8":"291","9":"0"},"gross_margin_beforew":{"0":"12.4","1":"0","2":"193.24","3":"17.49","4":"0","5":"0","6":"14","7":"64","8":"96","9":"0"},"sku_cnt_beforew":{"0":"2","1":"0","2":"28","3":"3","4":"0","5":"0","6":"2","7":"4","8":"3","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   sku_cnt |
|:------|----------:|
| count | 5985      |
| mean  |   17.9168 |
| std   |   55.0156 |
| min   |    0      |
| 25%   |    2      |
| 50%   |    4      |
| 75%   |   14      |
| max   | 2105      |