# app_kpi_cust_category_trade_di
* comment: 交易口径kpi指标日汇总
* last_data_modified_time: 2025-09-18 02:52:43

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_cust_category_trade_di` (
  `date` STRING COMMENT '日期',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"cust_team":{"0":"Mars大客户","1":"Mars大客户","2":"平台客户","3":"平台客户","4":"平台客户"},"category":{"0":"其他","1":"鲜果","2":"乳制品","3":"其他","4":"鲜果"},"origin_total_amt":{"0":"60","1":"41055.46","2":"2147647.37","3":"668073.34","4":"938576.36"},"real_total_amt":{"0":"59","1":"41037.46","2":"2081425.54","3":"635835.64","4":"901398.17"},"cust_cnt":{"0":"1","1":"152","2":"2680","3":"2354","4":"4815"},"cust_arpu":{"0":"60","1":"270.101710526315789474","2":"801.360958955223880597","3":"283.803457943925233645","4":"194.927592938733125649"},"order_cnt":{"0":"1","1":"156","2":"2885","3":"2582","4":"5435"},"order_avg":{"0":"60","1":"263.176025641025641026","2":"744.418499133448873484","3":"258.742579395817195972","4":"172.691142594296228151"},"after_sale_noreceived_amt":{"0":"0","1":"912.12","2":"139164.9","3":"33103.96","4":"17553.92"},"after_sale_rate":{"0":"0","1":"0.022216777013337568","2":"0.064798766289085903","3":"0.04955138607985764","4":"0.018702708429605024"},"dire_origin_total_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"delivery_amt":{"0":"0","1":"280","2":"4317.93","3":"1461.39","4":"8921.86"},"timing_origin_total_amt":{"0":"0","1":"0","2":"172592","3":"36581","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |
|:------|-----------:|------------:|
| count |        6   |        6    |
| mean  |     1669   |     1846.17 |
| std   |     1960.5 |     2195.63 |
| min   |        1   |        1    |
| 25%   |       47   |       52.5  |
| 50%   |     1253   |     1369    |
| 75%   |     2598.5 |     2809.25 |
| max   |     4815   |     5435    |