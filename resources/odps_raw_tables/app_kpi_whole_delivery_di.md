# app_kpi_whole_delivery_di
* comment: 履约kpi汇总
* last_data_modified_time: 2025-09-18 10:40:55

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_whole_delivery_di` (
  `date` STRING COMMENT '日期',
  `cust_group` STRING COMMENT '客户类型:大客户、平台客户、批发客户、ALL',
  `sku_type` STRING COMMENT '商品类型:自营、代仓、全品类、SAAS客户自营、ALL',
  `category` STRING COMMENT '商品类目:鲜果、乳制品、其他、ALL',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `dlv_cost_amt` DECIMAL(38,18) COMMENT '履约总成本',
  `dlv_origin_gross_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `dlv_real_gross_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `dlv_cust_cnt` BIGINT COMMENT '履约客户数',
  `dlv_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（含精准送，超时加单费，去除优惠券）',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `offine_delivey_amt` DECIMAL(38,18) COMMENT '履约费用',
  `offine_no_delivey_amt` DECIMAL(38,18) COMMENT '非履约费用',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额',
  `timing_cost_amt` DECIMAL(38,18) COMMENT '省心送履约总成本',
  `special_amt` DECIMAL(38,18) COMMENT '特价活动营销费用',
  `temporary_amt` DECIMAL(38,18) COMMENT '临保活动营销费用',
  `step_amt` DECIMAL(38,18) COMMENT '阶梯价营销费用',
  `cream_amt` DECIMAL(38,18) COMMENT '奶油卡营销费用',
  `fresh_milk_amt` DECIMAL(38,18) COMMENT '鲜奶卡营销费用',
  `after_sale_compensate_amt` DECIMAL(38,18) COMMENT '售后补偿营销费用',
  `industry_activities_amt` DECIMAL(38,18) COMMENT '行业活动营销费用',
  `sale_store_amt` DECIMAL(38,18) COMMENT '销售囤货券营销费用',
  `sale_spots_amt` DECIMAL(38,18) COMMENT '销售现货券营销费用',
  `sales_customer_amt` DECIMAL(38,18) COMMENT '销售客情券营销费用',
  `sales_activity_amt` DECIMAL(38,18) COMMENT '销售月活券营销费用',
  `sales_category_amt` DECIMAL(38,18) COMMENT '销售品类券营销费用',
  `area_new_amt` DECIMAL(38,18) COMMENT '区域拉新券营销费用',
  `area_recall_amt` DECIMAL(38,18) COMMENT '区域召回券营销费用',
  `full_reduction_amt` DECIMAL(38,18) COMMENT '满减活动营销费用',
  `platform_activity_amt` DECIMAL(38,18) COMMENT '平台活动券营销费用',
  `qst_amount` DECIMAL(38,18) COMMENT '全生态用车'
)
COMMENT '履约kpi汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"cust_group":{"0":"ALL","1":"ALL","2":"平台客户","3":"ALL","4":"ALL"},"sku_type":{"0":"ALL","1":"代仓","2":"代仓","3":"全品类","4":"自营"},"category":{"0":"鲜果","1":"ALL","2":"ALL","3":"ALL","4":"其他"},"dlv_origin_total_amt":{"0":"1134938.05","1":"667948.09","2":"158005.59","3":"332517.66","4":"728310.7"},"dlv_real_total_amt":{"0":"1094649.420000000000000048","1":"667948.09","2":"158005.59","3":"326857.464126984126984125","4":"698696.156666666666666672"},"dlv_cost_amt":{"0":"814849.31","1":"213863.63","2":"138557.43","3":"301962.686666666666666332","4":"607232.81"},"dlv_origin_gross_profit":{"0":"320088.74","1":"441647.34","2":"9071.14","3":"138473.36","4":"121077.89"},"dlv_real_gross_profit":{"0":"279800.110000000000000048","1":"441647.34","2":"9071.14","3":"24894.777460317460317793","4":"91463.346666666666666672"},"dlv_cust_cnt":{"0":"5426","1":"186","2":"118","3":"1382","4":"2501"},"dlv_point_cnt":{"0":"5607","1":"187","2":"119","3":"1404","4":"2551"},"delivery_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"after_sale_received_amt":{"0":"16504.37","1":"47.92","2":"47.92","3":"3288.67","4":"3993.31"},"damage_amt":{"0":"4999.44","1":"39.5","2":"0","3":"187","4":"922.34"},"offine_delivey_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"offine_no_delivey_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"timing_origin_total_amt":{"0":"0","1":"0","2":"0","3":"15323","4":"61242"},"timing_real_total_amt":{"0":"0","1":"0","2":"0","3":"14374.984126984126984127","4":"56600.666666666666666667"},"timing_cost_amt":{"0":"0","1":"0","2":"0","3":"5458","4":"50222.59"},"special_amt":{"0":"10952.4","1":"0","2":"0","3":"2202.54000000000000045","4":"16891.569999999999974"},"temporary_amt":{"0":"11237","1":"0","2":"0","3":"88.0000000000000024","4":"7203.509999999999999"},"step_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"cream_amt":{"0":"0","1":"0","2":"0","3":"0","4":"254.9999999999999965"},"fresh_milk_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"after_sale_compensate_amt":{"0":"7038.02","1":"0","2":"0","3":"706.8","4":"776.55"},"industry_activities_amt":{"0":"9421.93","1":"0","2":"0","3":"359.44","4":"1342.05"},"sale_store_amt":{"0":"0","1":"0","2":"0","3":"332.5714285714285576","4":"191"},"sale_spots_amt":{"0":"217","1":"0","2":"0","3":"333","4":"874"},"sales_customer_amt":{"0":"416.14","1":"0","2":"0","3":"90.89","4":"267.77"},"sales_activity_amt":{"0":"0","1":"0","2":"0","3":"9","4":"65"},"sales_category_amt":{"0":"181","1":"0","2":"0","3":"946.44444444444444","4":"1208.9999999999999954"},"area_new_amt":{"0":"602.77","1":"0","2":"0","3":"467.29","4":"395.82"},"area_recall_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"full_reduction_amt":{"0":"0","1":"0","2":"0","3":"0","4":"10"},"platform_activity_amt":{"0":"116","1":"0","2":"0","3":"86.79","4":"52.3333333333333333"},"qst_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   dlv_cust_cnt |   dlv_point_cnt |
|:------|---------------:|----------------:|
| count |          53    |           53    |
| mean  |        1549.17 |         1595.81 |
| std   |        2273.84 |         2354.35 |
| min   |           1    |            1    |
| 25%   |          31    |           31    |
| 50%   |         186    |          187    |
| 75%   |        2637    |         2696    |
| max   |        8175    |         8475    |