# app_kpi_operate_large_area_delivery_di
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:39:28

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_large_area_delivery_di` (
  `date` STRING COMMENT '日期',
  `large_area_name` STRING COMMENT '运营服务大区',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"large_area_name":{"0":"上海大区","1":"南宁大区","2":"成都大区","3":"昆明大区","4":"昆明快递大区"},"origin_total_amt":{"0":"310369.66","1":"27282.58","2":"116943.73","3":"40691","4":"788"},"real_total_amt":{"0":"301263.300000000000000005","1":"26462.78","2":"113507.76","3":"38904.900000000000000001","4":"788"},"marketing_amt":{"0":"9106.359999999999999995","1":"819.8","2":"3435.97","3":"1786.099999999999999999","4":"0"},"cost_amt":{"0":"267189.56","1":"25032.94","2":"101778.26","3":"36291.02","4":"746.17"},"origin_gross":{"0":"43180.1","1":"2249.64","2":"15165.47","3":"4399.98","4":"41.83"},"real_gross":{"0":"34073.740000000000000005","1":"1429.84","2":"11729.5","3":"2613.880000000000000001","4":"41.83"},"origin_gross_margin":{"0":"0.139124745633964351","1":"0.082457011030481721","2":"0.129681770882457743","3":"0.108131527856282716","4":"0.053083756345177665"},"real_gross_margin":{"0":"0.113102857201657155","1":"0.054032116051299221","2":"0.103336547210516708","3":"0.067186395544006025","4":"0.053083756345177665"},"cust_cnt":{"0":"615","1":"48","2":"279","3":"35","4":"1"},"point_cnt":{"0":"674","1":"49","2":"289","3":"36","4":"1"},"origin_pre_cust_price":{"0":"504.666113821138211382","1":"568.387083333333333333","2":"419.153154121863799283","3":"1162.6","4":"788"},"real_pre_cust_price":{"0":"489.859024390243902439","1":"551.307916666666666667","2":"406.837849462365591398","3":"1111.568571428571428571","4":"788"},"timing_origin_amt":{"0":"24950","1":"0","2":"6960","3":"0","4":"0"},"timing_real_amt":{"0":"23186.5","1":"0","2":"6449","3":"0","4":"0"},"consign_origin_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_marketing_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_origin_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_cust_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"storage_amt":{"0":"9103.066792356550735956","1":"577.021062342971648474","2":"1452.105292358391784136","3":"588.137194271439846049","4":"0"},"arterial_roads_amt":{"0":"1571.370039055861930543","1":"0","2":"0","3":"0","4":"0"},"self_picked_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_amt":{"0":"156.492986943881709561","1":"332.705637760783853133","2":"1413.723639853521716745","3":"1437.107833388333937368","4":"0"},"other_amt":{"0":"34.798265765513825418","1":"62.720030302504265958","2":"0","3":"152.523237034139215384","4":"0"},"deliver_amt":{"0":"18952.799184533364688245","1":"1026.357073067380240294","2":"5894.036986659572687678","3":"1000.574324516947891053","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |     15     |      15     |                 15 |
| mean  |    491.6   |     510.533 |                  0 |
| std   |    494.203 |     510.936 |                  0 |
| min   |      1     |       1     |                  0 |
| 25%   |    106.5   |     108.5   |                  0 |
| 50%   |    279     |     289     |                  0 |
| 75%   |    646.5   |     685     |                  0 |
| max   |   1572     |    1631     |                  0 |