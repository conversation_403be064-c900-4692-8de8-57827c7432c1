# app_sku_sale_statistics_df
* comment: 转换仓库+sku销量数据
* last_data_modified_time: 2025-09-18 02:45:01

# schema:
CREATE TABLE summerfarm_tech.`app_sku_sale_statistics_df` (
  `warehouse_no` BIGINT COMMENT '库存仓号',
  `warehouse_name` STRING COMMENT '库存仓名',
  `transfer_out_sku` STRING COMMENT '转出sku',
  `out_sale_cnt_b15d` DECIMAL(38,18) COMMENT '转出sku过去15天平均销量',
  `out_sale_cnt_b7d` DECIMAL(38,18) COMMENT '转出sku过去7天平均销量',
  `out_max_sale_cnt` BIGINT COMMENT '转出sku过去七天销量峰值',
  `out_min_sale_cnt` BIGINT COMMENT '转出sku前一天单个订单最小销量',
  `transfer_in_sku` STRING COMMENT '转入sku',
  `in_sale_cnt_b15d` DECIMAL(38,18) COMMENT '转入sku过去15天平均销量',
  `in_sale_cnt_b7d` DECIMAL(38,18) COMMENT '转入sku过去7天平均销量',
  `in_max_sale_cnt` BIGINT COMMENT '转入sku过去七天销量峰值',
  `in_min_sale_cnt` BIGINT COMMENT '转入sku前一天单个订单最小销量',
  `date_flag` STRING COMMENT '时间字段标识',
  `in_max_sale_thirty` DECIMAL(38,18) COMMENT '转入sku前三十天每日总销量峰值',
  `in_max_sale_sixty` DECIMAL(38,18) COMMENT '转入sku前六十天每日总销量峰值',
  `in_sell_out_num_one` BIGINT COMMENT '转入sku前一天售罄次数',
  `in_sell_out_time_one` DECIMAL(38,18) COMMENT '转入sku前一天售罄时长',
  `in_sell_out_num_seven` BIGINT COMMENT '转入sku前七天售罄次数',
  `in_sell_out_time_seven` DECIMAL(38,18) COMMENT '转入sku前七天售罄时长',
  `in_sell_out_num_thirty` BIGINT COMMENT '转入sku前三十天售罄次数',
  `in_sell_out_time_thirty` DECIMAL(38,18) COMMENT '转入sku前三十天售罄时长',
  `in_sell_out_num_sixty` BIGINT COMMENT '转入sku前六十天售罄次数',
  `in_sell_out_time_sixty` DECIMAL(38,18) COMMENT '转入sku前六十天售罄时长',
  `out_max_sale_thirty` DECIMAL(38,18) COMMENT '转出sku前三十天每日总销量峰值',
  `out_max_sale_sixty` DECIMAL(38,18) COMMENT '转出sku前六十天每日总销量峰值',
  `out_sell_out_num_one` BIGINT COMMENT '转出sku前一天售罄次数',
  `out_sell_out_time_one` DECIMAL(38,18) COMMENT '转出sku前一天售罄时长',
  `out_sell_out_num_seven` BIGINT COMMENT '转出sku前七天售罄次数',
  `out_sell_out_time_seven` DECIMAL(38,18) COMMENT '转出sku前七天售罄时长',
  `out_sell_out_num_thirty` BIGINT COMMENT '转出sku前三十天售罄次数',
  `out_sell_out_time_thirty` DECIMAL(38,18) COMMENT '转出sku前三十天售罄时长',
  `out_sell_out_num_sixty` BIGINT COMMENT '转出sku前六十天售罄次数',
  `out_sell_out_time_sixty` DECIMAL(38,18) COMMENT '转出sku前六十天售罄时长'
)
COMMENT '转换仓库+sku销量数据'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"warehouse_no":{"0":"1","1":"1","2":"1","3":"2","4":"2","5":"2","6":"10","7":"10","8":"10","9":"14"},"warehouse_name":{"0":"杭州总仓","1":"杭州总仓","2":"杭州总仓","3":"上海总仓","4":"上海总仓","5":"上海总仓","6":"嘉兴总仓","7":"嘉兴总仓","8":"嘉兴总仓","9":"广州总仓"},"transfer_out_sku":{"0":"687321000746","1":"780484374027","2":"N001S01R005","3":"16618316468","4":"663446743406","5":"831541460083","6":"1043235473177","7":"588544101226","8":"852414437806","9":"653872876"},"out_sale_cnt_b15d":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"4.133333333333334","9":"0"},"out_sale_cnt_b7d":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"4.428571428571429","9":"0"},"out_max_sale_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"7","9":"0"},"out_min_sale_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"1","9":"0"},"transfer_in_sku":{"0":"687321000044","1":"780484374288","2":"56143","3":"16618316018","4":"663446743507","5":"831541460831","6":"1043235473005","7":"588544101753","8":"852414437460","9":"653872426"},"in_sale_cnt_b15d":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"7.2","9":"0"},"in_sale_cnt_b7d":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"7.142857142857143","9":"0"},"in_max_sale_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"11","9":"0"},"in_min_sale_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"1","9":"0"},"date_flag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"in_max_sale_thirty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"15","9":"0"},"in_max_sale_sixty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"15","9":"0"},"in_sell_out_num_one":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_sell_out_time_one":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_sell_out_num_seven":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_sell_out_time_seven":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_sell_out_num_thirty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_sell_out_time_thirty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_sell_out_num_sixty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_sell_out_time_sixty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"out_max_sale_thirty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"16","9":"0"},"out_max_sale_sixty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"16","9":"0"},"out_sell_out_num_one":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"out_sell_out_time_one":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"out_sell_out_num_seven":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"out_sell_out_time_seven":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"out_sell_out_num_thirty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"out_sell_out_time_thirty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"out_sell_out_num_sixty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"out_sell_out_time_sixty":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   out_max_sale_cnt |   out_min_sale_cnt |   in_max_sale_cnt |   in_min_sale_cnt |   in_sell_out_num_one |   in_sell_out_num_seven |   in_sell_out_num_thirty |   in_sell_out_num_sixty |   out_sell_out_num_one |   out_sell_out_num_seven |   out_sell_out_num_thirty |   out_sell_out_num_sixty |
|:------|---------------:|-------------------:|-------------------:|------------------:|------------------:|----------------------:|------------------------:|-------------------------:|------------------------:|-----------------------:|-------------------------:|--------------------------:|-------------------------:|
| count |      5478      |         5478       |        5478        |        5478       |       5478        |        5478           |            5478         |             5478         |            5478         |          5478          |             5478         |              5478         |              5478        |
| mean  |        63.1126 |            2.20957 |           0.120847 |           2.98028 |          0.354691 |           0.000912742 |               0.0120482 |                0.0469149 |               0.0786783 |             0.00292077 |                0.0175246 |                 0.0757576 |                 0.137276 |
| std   |        47.7738 |           17.6514  |           0.4307   |          15.8303  |          4.78605  |           0.0357385   |               0.117179  |                0.25457   |               0.367844  |             0.0572533  |                0.143203  |                 0.29652   |                 0.407338 |
| min   |         1      |            0       |           0        |           0       |          0        |           0           |               0         |                0         |               0         |             0          |                0         |                 0         |                 0        |
| 25%   |        24      |            0       |           0        |           0       |          0        |           0           |               0         |                0         |               0         |             0          |                0         |                 0         |                 0        |
| 50%   |        59      |            0       |           0        |           0       |          0        |           0           |               0         |                0         |               0         |             0          |                0         |                 0         |                 0        |
| 75%   |       117      |            0       |           0        |           2       |          0        |           0           |               0         |                0         |               0         |             0          |                0         |                 0         |                 0        |
| max   |       155      |          670       |          10        |         670       |        300        |           2           |               2         |                6         |               8         |             2          |                3         |                 4         |                 8        |