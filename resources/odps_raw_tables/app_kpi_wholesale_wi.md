# app_kpi_wholesale_wi
* comment: 交易口径kpi指标日汇总
* last_data_modified_time: 2025-09-18 03:15:18

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_wholesale_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `sku_type` STRING COMMENT '商品类型; 自营/代仓',
  `order_gmv_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `deliver_cust_cnt` BIGINT COMMENT '履约客户数',
  `deliver_cust_arpu` DECIMAL(38,18) COMMENT '履约ARPU(应付总金额/客户数)',
  `deliver_order_cnt` BIGINT COMMENT '履约订单数',
  `deliver_order_avg` DECIMAL(38,18) COMMENT '履约订单均价(应付总金额/订单数)',
  `deliver_cost_amt` DECIMAL(38,18) COMMENT '履约总成本',
  `deliver_gross_profit` DECIMAL(38,18) COMMENT '履约毛利润(应付总金额-总成本)',
  `deliver_gross_profit_rate` DECIMAL(38,18) COMMENT '履约毛利率'
)
COMMENT '交易口径kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025"},"week_of_year":{"0":"38"},"monday":{"0":"20250915"},"sunday":{"0":"20250921"},"sku_type":{"0":"自营"},"order_gmv_amt":{"0":"1155024.55"},"deliver_gmv_amt":{"0":"582503.65"},"deliver_cust_cnt":{"0":"10"},"deliver_cust_arpu":{"0":"58250.365"},"deliver_order_cnt":{"0":"35"},"deliver_order_avg":{"0":"16642.961428571428571429"},"deliver_cost_amt":{"0":"581491.05"},"deliver_gross_profit":{"0":"1012.6"},"deliver_gross_profit_rate":{"0":"0.001738358205995791"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   deliver_cust_cnt |   deliver_order_cnt |
|:------|---------------:|-------------------:|--------------------:|
| count |              1 |                  1 |                   1 |
| mean  |             38 |                 10 |                  35 |
| std   |            nan |                nan |                 nan |
| min   |             38 |                 10 |                  35 |
| 25%   |             38 |                 10 |                  35 |
| 50%   |             38 |                 10 |                  35 |
| 75%   |             38 |                 10 |                  35 |
| max   |             38 |                 10 |                  35 |