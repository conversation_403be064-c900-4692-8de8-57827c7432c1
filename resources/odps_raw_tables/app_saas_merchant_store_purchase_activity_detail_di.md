# app_saas_merchant_store_purchase_activity_detail_di
* comment: saas门店采购活跃明细
* last_data_modified_time: 2025-09-18 02:18:57

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_purchase_activity_detail_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `store_id` BIGINT COMMENT '门店id',
  `purchased_amount_7d` BIGINT COMMENT '前7日采购订单数',
  `purchased_amount_30d` BIGINT COMMENT '前30日采购订单数'
)
COMMENT 'saas门店采购活跃明细'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"store_id":{"0":"1","1":"2","2":"3","3":"4","4":"5"},"purchased_amount_7d":{"0":"0","1":"1","2":"0","3":"0","4":"0"},"purchased_amount_30d":{"0":"0","1":"10","2":"0","3":"2","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   store_id |   purchased_amount_7d |   purchased_amount_30d |
|:------|------------:|-----------:|----------------------:|-----------------------:|
| count |  10000      |      10000 |           10000       |            10000       |
| mean  |     47.2515 |     336216 |               0.4036  |                1.6761  |
| std   |     34.1798 |     174331 |               1.25238 |                4.98372 |
| min   |      2      |          1 |               0       |                0       |
| 25%   |     13      |     371577 |               0       |                0       |
| 50%   |     59      |     388116 |               0       |                0       |
| 75%   |     64      |     445022 |               0       |                0       |
| max   |     99      |     543490 |              17       |               74       |