# app_stc_location_use_di
* comment: 仓配库位使用记录表
* last_data_modified_time: 2025-09-18 01:49:21

# schema:
CREATE TABLE summerfarm_tech.`app_stc_location_use_di` (
  `warehouse_no` STRING COMMENT '库存仓编号',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `zone_type` STRING COMMENT '库区',
  `cabinet_type` STRING COMMENT '库位类型',
  `use_volume` DECIMAL(38,18) COMMENT '使用体积',
  `use_code` BIGINT COMMENT '使用库位数',
  `logic_volume` DECIMAL(38,18) COMMENT '理论体积',
  `total_volume` DECIMAL(38,18) COMMENT '实际体积',
  `total_code` BIGINT COMMENT '总库位数'
)
COMMENT '仓配库位使用记录表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"warehouse_no":{"0":"10","1":"10","2":"10","3":"10","4":"10","5":"69","6":"69","7":"69","8":"69","9":"117"},"warehouse_name":{"0":"嘉兴总仓","1":"嘉兴总仓","2":"嘉兴总仓","3":"嘉兴总仓","4":"嘉兴总仓","5":"东莞总仓","6":"东莞总仓","7":"东莞总仓","8":"东莞总仓","9":"东莞冷冻总仓"},"zone_type":{"0":"冷藏","1":"冷藏","2":"常温","3":"常温","4":"常温","5":"冷藏","6":"常温","7":"常温","8":"常温","9":"常温"},"cabinet_type":{"0":"地堆","1":"高位货架","2":"地位货架","3":"地堆","4":"高位货架","5":"高位货架","6":"地位货架","7":"地堆","8":"高位货架","9":"地堆"},"use_volume":{"0":"136.889667436","1":"371.5507451199997","2":"5.994413828000002","3":"4469.144894511001","4":"1390.654897250999","5":"317.45507954","6":"0.3264630000000001","7":"9326.386057651","8":"893.4719426709999","9":"None"},"use_code":{"0":"167","1":"433","2":"127","3":"140","4":"1410","5":"212","6":"1","7":"7","8":"660","9":"0"},"logic_volume":{"0":"1142.6592","1":"709.44","2":"36.384","3":"10237.5872","4":"2029.824","5":"2161.44","6":"9.4207848","7":"11.904","8":"13947.12","9":"1.248"},"total_volume":{"0":"1696.32","1":"1024.8","2":"67.68","3":"13617.72","4":"3006.48","5":"3062.04","6":"23.552001","7":"16.56","8":"19612.5","9":"1.8"},"total_code":{"0":"439","1":"575","2":"306","3":"255","4":"1955","5":"1501","6":"369","7":"15","8":"3180","9":"13"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   use_code |   total_code |
|:------|-----------:|-------------:|
| count |     16     |       16     |
| mean  |    197.312 |      724.688 |
| std   |    373.888 |      873.787 |
| min   |      0     |        1     |
| 25%   |      0     |      203.5   |
| 50%   |      4     |      337.5   |
| 75%   |    178.25  |     1129.75  |
| max   |   1410     |     3180     |