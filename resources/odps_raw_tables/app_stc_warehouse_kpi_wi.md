# app_stc_warehouse_kpi_wi
* comment: 仓配kpi库存数据
* last_data_modified_time: 2025-09-18 03:42:12

# schema:
CREATE TABLE summerfarm_tech.`app_stc_warehouse_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `warehouse_no` BIGINT COMMENT '库存仓号',
  `warehouse_name` STRING COMMENT '库存仓',
  `check_sku_cnt` BIGINT COMMENT '抽检数量',
  `in_bound_sku_cnt` BIGINT COMMENT '入库数量',
  `back_order_cnt` BIGINT COMMENT '退货总单数',
  `finish_order_cnt` BIGINT COMMENT '已完成单数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责',
  `sale_amt` DECIMAL(38,18) COMMENT '销售金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额',
  `after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责',
  `after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责',
  `after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责',
  `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责',
  `after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付GMV',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
)
COMMENT '仓配kpi库存数据'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025","5":"2025","6":"2025","7":"2025","8":"2025","9":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38","5":"38","6":"38","7":"38","8":"38","9":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915","5":"20250915","6":"20250915","7":"20250915","8":"20250915","9":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921","5":"20250921","6":"20250921","7":"20250921","8":"20250921","9":"20250921"},"warehouse_no":{"0":"48","1":"2","2":"64","3":"121","4":"155","5":"117","6":"63","7":"10","8":"24","9":"60"},"warehouse_name":{"0":"长沙总仓","1":"上海总仓","2":"青岛总仓","3":"嘉兴海盐总仓","4":"武汉总仓","5":"东莞冷冻总仓","6":"贵阳总仓","7":"嘉兴总仓","8":"华西总仓","9":"昆明总仓"},"check_sku_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"in_bound_sku_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"back_order_cnt":{"0":"22","1":"2","2":"4","3":"12","4":"11","5":"4","6":"1","7":"84","8":"2","9":"0"},"finish_order_cnt":{"0":"6","1":"2","2":"0","3":"8","4":"1","5":"0","6":"1","7":"18","8":"1","9":"0"},"damage_amt":{"0":"1499.43","1":"0","2":"375.32","3":"12.16","4":"1827.71","5":"0","6":"440.2","7":"7690.58","8":"2369.96","9":"327"},"damage_amt_wah":{"0":"285.9","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"71.99","9":"0"},"sale_amt":{"0":"940924.77","1":"337453.47","2":"205286.41","3":"408967.23","4":"653766.53","5":"345878.25","6":"76871.08","7":"3142693.16","8":"538666.8","9":"113964.13"},"after_sale_amt":{"0":"5981.82","1":"470.99","2":"2018.94","3":"1554.36","4":"2696.07","5":"792.87","6":"431.79","7":"32361.6","8":"3087.16","9":"30"},"after_sale_amt_wah":{"0":"209.5","1":"0","2":"10.67","3":"1080.9","4":"127.78","5":"163.79","6":"0","7":"4120.35","8":"38.72","9":"0"},"after_sale_amt_pur":{"0":"771.36","1":"0","2":"168.39","3":"0","4":"486.79","5":"0","6":"0","7":"4037.03","8":"142.75","9":"0"},"after_sale_amt_che":{"0":"4296.42","1":"470.99","2":"1603.33","3":"86.46","4":"1783.12","5":"237.49","6":"431.79","7":"19215.74","8":"2545.55","9":"0"},"after_sale_amt_pur_che":{"0":"5067.78","1":"470.99","2":"1771.72","3":"86.46","4":"2269.91","5":"237.49","6":"431.79","7":"23252.77","8":"2688.3","9":"0"},"after_sale_amt_oth":{"0":"704.54","1":"0","2":"236.55","3":"387","4":"298.38","5":"391.59","6":"0","7":"4988.48","8":"360.14","9":"30"},"delivery_total_amt":{"0":"1323251.189999999999994174","1":"827011.15","2":"229257.69000000000000101","3":"740812.979999999999982028","4":"754590.239999999999986666","5":"403723.639999999999999866","6":"83367.32","7":"7093104.059999999999962977","8":"585767.759999999999997503","9":"116784.490000000000000001"},"coupon_amt":{"0":"14737.2933333333333275","1":"704.21","2":"2927.360000000000001","3":"4251.3447619047618868","4":"7740.08333333333332","5":"2676.7733333333333332","6":"506.62","7":"44364.89679487179483472","8":"5938.6199999999999975","9":"295"},"origin_total_amt":{"0":"1423057.81","1":"275043.62","2":"235538.05","3":"453913.55","4":"770681.37","5":"413010.2","6":"84125.31","7":"3715842.42","8":"637474.69","9":"113445.2"},"real_total_amt":{"0":"1381069.636666666666666673","1":"272229.91","2":"226330.33000000000000001","3":"443984.345238095238095228","4":"746424.066666666666666666","5":"402770.556666666666666666","6":"82718.3","7":"3601968.493205128205128257","8":"624230.090000000000000003","9":"110018.490000000000000001"},"storage_amt":{"0":"31600.10367022412148752","1":"2800.064572473817191528","2":"4352.943232909674117923","3":"4094.156890085000301405","4":"20980.205305029857398695","5":"4201.32048551106284913","6":"1967.152129009849768351","7":"77597.161159246872119153","8":"10593.495327447903167954","9":"1555.818222269785650243"},"arterial_roads_amt":{"0":"41848.36578801101100004","1":"303.802619288298778739","2":"2563.471918531825702121","3":"2458.045049767008179137","4":"34464.891794292724091381","5":"3143.83927017663520907","6":"0","7":"44003.815194360612538608","8":"0","9":"0"},"deliver_amt":{"0":"154279.735297698051002163","1":"5904.549928146676162747","2":"9862.92742517352132511","3":"12412.175837113914997261","4":"78378.136957132483113752","5":"9058.030342539107481579","6":"3290.329020323640607492","7":"225201.876770238697580804","8":"37223.922394182675664729","9":"3040.646731926732516799"},"self_picked_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"other_amt":{"0":"5937.524593956132696749","1":"4.893081513187328581","2":"481.132019001626496563","3":"887.105685292458719725","4":"2658.725352756311247057","5":"0","6":"599.01882539141373905","7":"16769.040958184641581348","8":"0","9":"403.474620902429306055"},"allocation_amt":{"0":"4543.688956163429426743","1":"47.756475566789900833","2":"2390.975155073948379802","3":"45.741807988869648585","4":"2798.767477787821752701","5":"734.400366136809292573","6":"0","7":"930.43883773154722486","8":"9072.653476615916331803","9":"3801.627539169556104065"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   warehouse_no |   check_sku_cnt |   in_bound_sku_cnt |   back_order_cnt |   finish_order_cnt |
|:------|---------------:|---------------:|----------------:|-------------------:|-----------------:|-------------------:|
| count |             17 |        17      |              17 |                 17 |          17      |           17       |
| mean  |             38 |        70.0588 |               0 |                  0 |          11.8235 |            3.17647 |
| std   |              0 |        46.4926 |               0 |                  0 |          20.3077 |            4.57213 |
| min   |             38 |         2      |               0 |                  0 |           0      |            0       |
| 25%   |             38 |        38      |               0 |                  0 |           2      |            0       |
| 50%   |             38 |        62      |               0 |                  0 |           4      |            1       |
| 75%   |             38 |       117      |               0 |                  0 |          12      |            5       |
| max   |             38 |       155      |               0 |                  0 |          84      |           18       |