# app_log_recommend_transform_cust_di
* comment: 商城推荐转化汇总表
* last_data_modified_time: 2025-09-18 02:52:12

# schema:
CREATE TABLE summerfarm_tech.`app_log_recommend_transform_cust_di` (
  `date` STRING COMMENT '日期',
  `cust_id` BIGINT COMMENT '客户id',
  `scene` STRING COMMENT '场景：首页、商品详情页、购物车页',
  `abexperiments_experiment_id` STRING COMMENT 'AB实验解析字段-experiment_id',
  `abexperiments_experiment_place` STRING COMMENT 'AB实验解析字段-experiment_place',
  `abexperiments_variant_id` STRING COMMENT 'AB实验解析字段-variant_id',
  `is_new` STRING COMMENT '是否当日注册（是/否）',
  `sku_impression_uv` BIGINT COMMENT '商品曝光UV',
  `sku_impression_pv` BIGINT COMMENT '商品曝光PV',
  `sku_click_uv` BIGINT COMMENT '商品点击UV',
  `sku_click_pv` BIGINT COMMENT '商品点击PV',
  `sku_cart_uv` BIGINT COMMENT '商品加购UV',
  `sku_cart_pv` BIGINT COMMENT '商品加购PV',
  `order_cnt` BIGINT COMMENT '下单数（订单数量）',
  `order_amt` DECIMAL(38,18) COMMENT '下单金额',
  `order_paid_cnt` BIGINT COMMENT '支付订单数',
  `order_paid_amt` DECIMAL(38,18) COMMENT '支付金额',
  `order_paid_amt_avg` DECIMAL(38,18) COMMENT '平均支付金额'
)
COMMENT '商城推荐转化汇总表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"cust_id":{"0":"nan","1":"nan","2":"26.0","3":"123.0","4":"143.0"},"scene":{"0":"商品详情页","1":"首页","2":"分类页","3":"分类页","4":"首页"},"abexperiments_experiment_id":{"0":"无","1":"无","2":"无","3":"无","4":"无"},"abexperiments_experiment_place":{"0":"无","1":"无","2":"无","3":"无","4":"无"},"abexperiments_variant_id":{"0":"无","1":"无","2":"无","3":"无","4":"无"},"is_new":{"0":"None","1":"None","2":"否","3":"否","4":"否"},"sku_impression_uv":{"0":"0","1":"0","2":"1","3":"1","4":"1"},"sku_impression_pv":{"0":"0","1":"0","2":"138","3":"69","4":"27"},"sku_click_uv":{"0":"0","1":"0","2":"1","3":"1","4":"1"},"sku_click_pv":{"0":"0","1":"0","2":"2","3":"1","4":"1"},"sku_cart_uv":{"0":"0","1":"0","2":"1","3":"1","4":"0"},"sku_cart_pv":{"0":"0","1":"0","2":"3","3":"1","4":"0"},"order_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_paid_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_paid_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_paid_amt_avg":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_id |   sku_impression_uv |   sku_impression_pv |   sku_click_uv |   sku_click_pv |   sku_cart_uv |   sku_cart_pv |    order_cnt |   order_paid_cnt |
|:------|----------:|--------------------:|--------------------:|---------------:|---------------:|--------------:|--------------:|-------------:|-----------------:|
| count |      9998 |         10000       |          10000      |   10000        |    10000       |  10000        |   10000       | 10000        |     10000        |
| mean  |    266050 |             0.9962  |             38.3117 |       0.44     |        1.3625  |      0.2667   |       0.5539  |     0.0948   |         0.0908   |
| std   |    144993 |             0.06153 |             72.4056 |       0.496412 |        2.55232 |      0.442256 |       1.16646 |     0.338562 |         0.327973 |
| min   |        26 |             0       |              0      |       0        |        0       |      0        |       0       |     0        |         0        |
| 25%   |    142442 |             1       |              4      |       0        |        0       |      0        |       0       |     0        |         0        |
| 50%   |    280178 |             1       |             13      |       0        |        0       |      0        |       0       |     0        |         0        |
| 75%   |    401226 |             1       |             40      |       1        |        2       |      1        |       1       |     0        |         0        |
| max   |    473346 |             1       |           1073      |       1        |       34       |      1        |      14       |     5        |         5        |