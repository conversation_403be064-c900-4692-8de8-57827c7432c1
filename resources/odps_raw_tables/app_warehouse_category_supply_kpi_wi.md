# app_warehouse_category_supply_kpi_wi
* comment: 供应链kpi
* last_data_modified_time: 2025-09-18 03:14:47

# schema:
CREATE TABLE summerfarm_tech.`app_warehouse_category_supply_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `warehouse_no` BIGINT COMMENT '库存仓',
  `warehouse_name` STRING COMMENT '库存仓',
  `category` STRING COMMENT '鲜果，标品',
  `sale_out_time` DECIMAL(38,18) COMMENT '售罄时长',
  `on_sale_time` DECIMAL(38,18) COMMENT '上架时长',
  `store_cost_amt` DECIMAL(38,18) COMMENT '期末库存成本',
  `sale_amt` DECIMAL(38,18) COMMENT '销售出库成本',
  `temporary_store_amt` DECIMAL(38,18) COMMENT '临保成本',
  `damage_amt` DECIMAL(38,18) COMMENT '滞销过期货损出库成本',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV'
)
COMMENT '供应链kpi'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025","5":"2025","6":"2025","7":"2025","8":"2025","9":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38","5":"38","6":"38","7":"38","8":"38","9":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915","5":"20250915","6":"20250915","7":"20250915","8":"20250915","9":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921","5":"20250921","6":"20250921","7":"20250921","8":"20250921","9":"20250921"},"warehouse_no":{"0":"59","1":"91","2":"116","3":"59","4":"60","5":"150","6":"24","7":"69","8":"125","9":"7"},"warehouse_name":{"0":"南宁总仓","1":"美团虚拟代下单总仓","2":"美团代加工虚拟仓","3":"南宁总仓","4":"昆明总仓","5":"嘉兴水果批发总仓","6":"华西总仓","7":"东莞总仓","8":"南京总仓","9":"山东总仓"},"category":{"0":"标品","1":"标品","2":"鲜果","3":"鲜果","4":"标品","5":"鲜果","6":"标品","7":"标品","8":"标品","9":"标品"},"sale_out_time":{"0":"7379.5397222222221969","1":"1080","2":"0","3":"818.7447222222222239","4":"4523.09472222222223","5":"1407.620833333333329334","6":"13808.13555555555539155","7":"53241.300000000000031578","8":"13683.013888888888904686","9":"0"},"on_sale_time":{"0":"38152.679444444444449","1":"1656","2":"0","3":"4791.587222222222222","4":"24377.67944444444445","5":"20354.148333333333318","6":"57495.371944444444443667","7":"134779.406388888888877666","8":"71680.438055555555548","9":"0"},"store_cost_amt":{"0":"2463546.36","1":"199521","2":"12744.48","3":"59675.67","4":"2639136.86","5":"0","6":"8802386.71","7":"27873828.34","8":"10451315.64","9":"1860"},"sale_amt":{"0":"208731.58","1":"0","2":"0","3":"16465.46","4":"113262.3","5":"0","6":"329878.93","7":"1477801.21","8":"543269.43","9":"0"},"temporary_store_amt":{"0":"37","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"174.26","9":"0"},"damage_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"355","7":"0","8":"464","9":"0"},"origin_total_amt":{"0":"175571.96","1":"0","2":"0","3":"13559.26","4":"111795.2","5":"0","6":"340447.05","7":"1726079.53","8":"631862.98","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   warehouse_no |
|:------|---------------:|---------------:|
| count |             46 |        46      |
| mean  |             38 |        70.6087 |
| std   |              0 |        48.1384 |
| min   |             38 |         1      |
| 25%   |             38 |        31.25   |
| 50%   |             38 |        63      |
| 75%   |             38 |       114.25   |
| max   |             38 |       155      |