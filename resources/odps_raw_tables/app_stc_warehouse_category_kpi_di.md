# app_stc_warehouse_category_kpi_di
* comment: 品控kpi
* last_data_modified_time: 2025-09-18 03:03:14

# schema:
CREATE TABLE summerfarm_tech.`app_stc_warehouse_category_kpi_di` (
  `date` STRING COMMENT '日期',
  `warehouse_no` BIGINT COMMENT '库存仓',
  `warehouse_name` STRING COMMENT '库存仓名',
  `category` STRING COMMENT '类目：鲜果，标品',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后金额',
  `after_sale_amt_check` DECIMAL(38,18) COMMENT '售后金额品控责',
  `damage_cnt` BIGINT COMMENT '货损数量',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `damage_cnt_wah` BIGINT COMMENT '货损数量_仓配责',
  `damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额_仓配责',
  `damage_cnt_pur` BIGINT COMMENT '货损数量_采购责',
  `damage_amt_pur` DECIMAL(38,18) COMMENT '货损金额_采购责',
  `damage_cnt_opr` BIGINT COMMENT '货损数量_运营责',
  `damage_amt_opr` DECIMAL(38,18) COMMENT '货损金额_运营责',
  `damage_cnt_oth` BIGINT COMMENT '货损数量_其他责',
  `damage_amt_oth` DECIMAL(38,18) COMMENT '货损金额_其他责',
  `sale_cnt` BIGINT COMMENT '销售数量',
  `sale_amt` DECIMAL(38,18) COMMENT '销售金额',
  `test_cnt` BIGINT COMMENT '抽检数量',
  `qualified_cnt` BIGINT COMMENT '合格数量',
  `check_cnt` BIGINT COMMENT '货检数量',
  `inbound_cnt` BIGINT COMMENT '入库数量'
)
COMMENT '品控kpi'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"warehouse_no":{"0":"145","1":"150","2":"145","3":"63","4":"24","5":"69","6":"59","7":"64","8":"125","9":"63"},"warehouse_name":{"0":"济南总仓","1":"嘉兴水果批发总仓","2":"济南总仓","3":"贵阳总仓","4":"华西总仓","5":"东莞总仓","6":"南宁总仓","7":"青岛总仓","8":"南京总仓","9":"贵阳总仓"},"category":{"0":"鲜果","1":"鲜果","2":"标品","3":"标品","4":"标品","5":"标品","6":"鲜果","7":"鲜果","8":"鲜果","9":"鲜果"},"origin_total_amt":{"0":"2497.35","1":"0","2":"30060","3":"16754.4","4":"104664.56","5":"604323.3","6":"3907.98","7":"13508.3","8":"61764.45","9":"1906"},"real_total_amt":{"0":"2443.01","1":"0","2":"29859.8","3":"16428.4","4":"101943.62","5":"584428.708888888888888893","6":"3743.38","7":"13020.240000000000000012","8":"59008.230000000000000009","9":"1906"},"after_sale_amt":{"0":"88.12","1":"0","2":"0","3":"0","4":"322.33","5":"851.51","6":"320","7":"383.51","8":"551.85","9":"3.1"},"after_sale_amt_check":{"0":"43.49","1":"0","2":"0","3":"0","4":"308.33","5":"339.27","6":"43.79","7":"215.47","8":"210.07","9":"3.1"},"damage_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"2","9":"5"},"damage_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"122","9":"146.2"},"damage_cnt_wah":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"damage_amt_wah":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"damage_cnt_pur":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"damage_amt_pur":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"damage_cnt_opr":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"damage_amt_opr":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"damage_cnt_oth":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"2","9":"5"},"damage_amt_oth":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"122","9":"146.2"},"sale_cnt":{"0":"51","1":"43","2":"39","3":"69","4":"647","5":"2906","6":"193","7":"357","8":"1117","9":"58"},"sale_amt":{"0":"2049.5","1":"0","2":"29373.45","3":"15499.25","4":"86180.45","5":"523897.09","6":"8155.26","7":"10581.93","8":"46278.58","9":"1561.01"},"test_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"qualified_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"check_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"inbound_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   damage_cnt |   damage_cnt_wah |   damage_cnt_pur |   damage_cnt_opr |   damage_cnt_oth |   sale_cnt |   test_cnt |   qualified_cnt |   check_cnt |   inbound_cnt |
|:------|---------------:|-------------:|-----------------:|-----------------:|-----------------:|-----------------:|-----------:|-----------:|----------------:|------------:|--------------:|
| count |        30      |      30      |       30         |               30 |        30        |          30      |      30    |         30 |              30 |          30 |            30 |
| mean  |        73.3667 |       5.6    |        0.0333333 |                0 |         0.366667 |           5.2    |    1534.07 |          0 |               0 |           0 |             0 |
| std   |        46.9963 |      14.2408 |        0.182574  |                0 |         2.00832  |          14.2233 |    2432.94 |          0 |               0 |           0 |             0 |
| min   |         2      |       0      |        0         |                0 |         0        |           0      |      13    |          0 |               0 |           0 |             0 |
| 25%   |        40.5    |       0      |        0         |                0 |         0        |           0      |     151    |          0 |               0 |           0 |             0 |
| 50%   |        62.5    |       0      |        0         |                0 |         0        |           0      |     645    |          0 |               0 |           0 |             0 |
| 75%   |       120      |       4.25   |        0         |                0 |         0        |           2      |    1406.5  |          0 |               0 |           0 |             0 |
| max   |       155      |      75      |        1         |                0 |        11        |          75      |   11544    |          0 |               0 |           0 |             0 |