# app_kpi_sku_manage_honour_mi
* comment: 履约口径kpi指标月汇总
* last_data_modified_time: 2025-09-18 03:41:49

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_sku_manage_honour_mi` (
  `month` STRING COMMENT '日期',
  `manage_type` STRING COMMENT '商品经营类型：自营，代仓',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额(gmv)',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `after_sale_received_order_cnt` BIGINT COMMENT '已到货售后订单数',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `point_out_rate` DECIMAL(38,18) COMMENT '外区点位占比',
  `point_in_rate` DECIMAL(38,18) COMMENT '内区点位占比',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
  `damage_rate` DECIMAL(38,18) COMMENT '货损率',
  `replenish_out_amt` DECIMAL(38,18) COMMENT '补发出库总金额',
  `return_in_amt` DECIMAL(38,18) COMMENT '退货入库总金额',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `point_day_cnt` BIGINT COMMENT '均日点位数',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费'
)
COMMENT '履约口径kpi指标月汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509"},"manage_type":{"0":"自营","1":"代仓","2":"代售","3":"SAAS鲜沐自营"},"origin_total_amt":{"0":"65033372.36","1":"16502675.8","2":"5833638.06","3":"1828632.14"},"real_total_amt":{"0":"63026424.56","1":"16502675.8","2":"5685417.64","3":"1828632.14"},"origin_pay_rate":{"0":"0.13337264031128279","1":"0.985542064033033964","2":"0.450774719472397299","3":"0.187658770998086034"},"real_pay_rate":{"0":"0.105776661559682801","1":"0.985542064033033964","2":"0.436456263571870157","3":"0.187658770998086034"},"preferential_amt":{"0":"2006947.8","1":"0","2":"148220.42","3":"0"},"refund_amt":{"0":"2502689.55","1":"0","2":"246560.87","3":"0"},"after_sale_received_amt":{"0":"133870.33","1":"0","2":"38746.82","3":"28726.53"},"after_sale_received_order_cnt":{"0":"2127","1":"0","2":"330","3":"811"},"cust_cnt":{"0":"44095","1":"249","2":"12472","3":"1507"},"cust_unit_amt":{"0":"1474.846861548928449938","1":"66275.80642570281124498","2":"467.738779666452854394","3":"1213.425441274054412741"},"order_cnt":{"0":"146720","1":"2233","2":"24471","3":"7890"},"point_cnt":{"0":"47938","1":"253","2":"13162","3":"1534"},"point_out_rate":{"0":"0.02446910592849097","1":"0","2":"0.0208175049384592","3":"0"},"point_in_rate":{"0":"0.7566857190537778","1":"0.7391304347826086","2":"0.7745783315605531","3":"0"},"inventory_loss_amt":{"0":"6828.02","1":"5450","2":"0","3":"0"},"inventory_profit_amt":{"0":"6410.19","1":"700","2":"0","3":"0"},"damage_amt":{"0":"129538.82","1":"14905.14","2":"0","3":"0"},"damage_rate":{"0":"0.001991882249053953","1":"0.000903195347266048","2":"0","3":"0"},"replenish_out_amt":{"0":"53636.35","1":"1948.34","2":"0","3":"0"},"return_in_amt":{"0":"193280.45","1":"20562.99","2":"0","3":"0"},"sku_cnt":{"0":"584143","1":"29550","2":"59097","3":"30339"},"storage_amt":{"0":"1281578.998891913905403296","1":"38728.597502830242693713","2":"136068.55086259722483665","3":"0"},"arterial_roads_amt":{"0":"1016001.87759266149202856","1":"59547.071137949006214472","2":"108739.190974439455048103","3":"0"},"deliver_amt":{"0":"3819865.105540082958785428","1":"146731.266424153972936409","2":"411441.302328671169803074","3":"0"},"self_picked_amt":{"0":"0","1":"0","2":"0","3":"0"},"other_amt":{"0":"161137.100217753998606108","1":"10372.127340309522150372","2":"17556.694800555585313152","3":"0"},"point_day_cnt":{"0":"129174","1":"1469","2":"23712","3":"6820"},"allocation_amt":{"0":"251004.987403417649681662","1":"610.590566345683090675","2":"17509.480224588286540093","3":"0"},"delivery_amt":{"0":"135121.14","1":"0","2":"25078.67","3":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   after_sale_received_order_cnt |   cust_cnt |   order_cnt |   point_cnt |   sku_cnt |   point_day_cnt |
|:------|--------------------------------:|-----------:|------------:|------------:|----------:|----------------:|
| count |                           4     |        4   |        4    |        4    |       4   |            4    |
| mean  |                         817     |    14580.8 |    45328.5  |    15721.8  |  175782   |        40293.8  |
| std   |                         934.665 |    20427.6 |    68249.9  |    22248.7  |  272587   |        60007    |
| min   |                           0     |      249   |     2233    |      253    |   29550   |         1469    |
| 25%   |                         247.5   |     1192.5 |     6475.75 |     1213.75 |   30141.8 |         5482.25 |
| 50%   |                         570.5   |     6989.5 |    16180.5  |     7348    |   44718   |        15266    |
| 75%   |                        1140     |    20377.8 |    55033.2  |    21856    |  190358   |        50077.5  |
| max   |                        2127     |    44095   |   146720    |    47938    |  584143   |       129174    |