# app_operate_kpi_trade_mi
* comment: 交易（订单）运营KPI指标汇总
* last_data_modified_time: 2025-09-18 02:41:34

# schema:
CREATE TABLE summerfarm_tech.`app_operate_kpi_trade_mi` (
  `month` STRING COMMENT '月份',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `cust_cnt` BIGINT COMMENT '交易客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `consign_origin_total_amt` DECIMAL(38,18) COMMENT '代售应付总金额',
  `consign_real_total_amt` DECIMAL(38,18) COMMENT '代售实付总金额',
  `consign_preferential_amt` DECIMAL(38,18) COMMENT '代售营销金额',
  `consign_cust_cnt` BIGINT COMMENT '代售客户数',
  `consign_order_cnt` BIGINT COMMENT '代售订单数',
  `register_cust_cnt` BIGINT COMMENT '注册客户数',
  `new_active_cust_cnt` BIGINT COMMENT '有效拉新客户数（注册且首日下单金额>=15）'
)
COMMENT '交易（订单）运营KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509"},"origin_total_amt":{"0":"67642705.1"},"real_total_amt":{"0":"65476143.81"},"cust_cnt":{"0":"44797"},"cust_arpu":{"0":"1509.982925195883652923"},"order_cnt":{"0":"151718"},"order_avg":{"0":"445.84495643232839874"},"consign_origin_total_amt":{"0":"0"},"consign_real_total_amt":{"0":"0"},"consign_preferential_amt":{"0":"0"},"consign_cust_cnt":{"0":"0"},"consign_order_cnt":{"0":"0"},"register_cust_cnt":{"0":"5001"},"new_active_cust_cnt":{"0":"1655"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   consign_cust_cnt |   consign_order_cnt |   register_cust_cnt |   new_active_cust_cnt |
|:------|-----------:|------------:|-------------------:|--------------------:|--------------------:|----------------------:|
| count |          1 |           1 |                  1 |                   1 |                   1 |                     1 |
| mean  |      44797 |      151718 |                  0 |                   0 |                5001 |                  1655 |
| std   |        nan |         nan |                nan |                 nan |                 nan |                   nan |
| min   |      44797 |      151718 |                  0 |                   0 |                5001 |                  1655 |
| 25%   |      44797 |      151718 |                  0 |                   0 |                5001 |                  1655 |
| 50%   |      44797 |      151718 |                  0 |                   0 |                5001 |                  1655 |
| 75%   |      44797 |      151718 |                  0 |                   0 |                5001 |                  1655 |
| max   |      44797 |      151718 |                  0 |                   0 |                5001 |                  1655 |