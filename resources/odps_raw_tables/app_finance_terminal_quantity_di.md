# app_finance_terminal_quantity_di
* comment: 财务口径期末库存表
* last_data_modified_time: 2025-09-18 02:12:09

# schema:
CREATE TABLE summerfarm_tech.`app_finance_terminal_quantity_di` (
  `service_area` STRING COMMENT '大区',
  `warehouse_no` BIGINT COMMENT '库存仓ID',
  `warehouse_name` STRING COMMENT '库存仓名',
  `sku` STRING COMMENT 'SKU',
  `pd_name` STRING COMMENT '商品名',
  `quantity` BIGINT COMMENT '期末库存量',
  `cost_amt` DECIMAL(38,18) COMMENT '期末总金额',
  `date_flag` STRING COMMENT '日期标识',
  `category1` STRING COMMENT '商品一级类目',
  `cost_amt_notax` DECIMAL(38,18) COMMENT '期末总金额(不含税)',
  `sub_type` BIGINT COMMENT 'bigint 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓',
  `settle_type` STRING COMMENT '结算类型'
)
COMMENT '财务口径期末库存表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"service_area":{"0":"华东","1":"华东","2":"华东","3":"华东","4":"华东"},"warehouse_no":{"0":2,"1":2,"2":2,"3":2,"4":2},"warehouse_name":{"0":"上海总仓","1":"上海总仓","2":"上海总仓","3":"上海总仓","4":"上海总仓"},"sku":{"0":"*********","1":"*********","2":"*********","3":"Q001L01S001","4":"*********"},"pd_name":{"0":"大台农芒果冻肉","1":"大台农芒果冻肉","2":"精品冻树莓单盒","3":"琪雷萨马斯卡彭","4":"大台农芒果冻肉"},"quantity":{"0":0,"1":0,"2":0,"3":0,"4":0},"cost_amt":{"0":0.0,"1":0.0,"2":0.0,"3":0.0,"4":0.0},"date_flag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"category1":{"0":"其他","1":"其他","2":"其他","3":"乳制品","4":"其他"},"cost_amt_notax":{"0":0.0,"1":0.0,"2":0.0,"3":0.0,"4":0.0},"sub_type":{"0":3,"1":3,"2":3,"3":3,"4":3},"settle_type":{"0":"","1":"","2":"","3":"","4":""},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   quantity |    sub_type |
|:------|---------------:|-----------:|------------:|
| count |     10000      | 10000      | 10000       |
| mean  |        24.6471 |    30.2971 |     2.6598  |
| std   |        17.426  |   805.599  |     0.68199 |
| min   |         2      |     0      |     1       |
| 25%   |        10      |     0      |     3       |
| 50%   |        24      |     0      |     3       |
| 75%   |        38      |     0      |     3       |
| max   |        59      | 57816      |     3       |