# app_xianmu_sale_purchase_item_change_order_df
* comment: 销转采采购单明细
* last_data_modified_time: 2025-09-18 02:41:54

# schema:
CREATE TABLE summerfarm_tech.`app_xianmu_sale_purchase_item_change_order_df` (
  `purchase_no` STRING COMMENT '采购批次',
  `sku` STRING COMMENT 'sku',
  `pd_name` STRING COMMENT '商品名称',
  `weight` STRING COMMENT '规格',
  `supplier` STRING COMMENT '供货商',
  `quantity` BIGINT COMMENT '采购数量',
  `actual_quantity` BIGINT COMMENT '实收数量',
  `price_type` STRING COMMENT '价格形式',
  `cost` DECIMAL(38,18) COMMENT '单个成本',
  `total_cost` DECIMAL(38,18) COMMENT '总成本'
)
COMMENT '销转采采购单明细'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"purchase_no":{"0":"ZC0125KUYKTA0328141557","1":"ZC0125OWJCTJ0328142568","2":"ZC0125LQ3OK20328141478","3":"ZC0125OWJCTJ0328142568"},"sku":{"0":"81737321777","1":"81737321814","2":"848435782088","3":"81737321485"},"pd_name":{"0":"性能测试品","1":"性能测试品","2":"乔治测试支付","3":"性能测试品"},"weight":{"0":"None","1":"None","2":"None","3":"None"},"supplier":{"0":"杭州鲜沐科技有限公司","1":"杭州鲜沐科技有限公司","2":"杭州鲜沐科技有限公司","3":"杭州鲜沐科技有限公司"},"quantity":{"0":"1","1":"1","2":"1","3":"1"},"actual_quantity":{"0":"1","1":"1","2":"1","3":"1"},"price_type":{"0":"指定价","1":"指定价","2":"指定价","3":"指定价"},"cost":{"0":"1.02","1":"1.025","2":"1020","3":"1.02"},"total_cost":{"0":"1.02","1":"1.025","2":"1020","3":"1.02"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   quantity |   actual_quantity |
|:------|-----------:|------------------:|
| count |          4 |                 4 |
| mean  |          1 |                 1 |
| std   |          0 |                 0 |
| min   |          1 |                 1 |
| 25%   |          1 |                 1 |
| 50%   |          1 |                 1 |
| 75%   |          1 |                 1 |
| max   |          1 |                 1 |