# app_log_cust_route_di
* comment: 用户路径数据
* last_data_modified_time: 2025-09-18 11:41:36

# schema:
CREATE TABLE summerfarm_tech.`app_log_cust_route_di` (
  `date` STRING COMMENT '日期',
  `page_one` STRING COMMENT '页面一',
  `page_two` STRING COMMENT '页面二',
  `page_three` STRING COMMENT '页面三',
  `page_four` STRING COMMENT '页面四',
  `page_five` STRING COMMENT '页面五',
  `page_six` STRING COMMENT '页面六',
  `pv` BIGINT COMMENT 'pv数',
  `uv` BIGINT COMMENT 'uv数'
)
COMMENT '用户路径数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"page_one":{"0":"\/home","1":"\/home","2":"\/home","3":"\/home","4":"\/home"},"page_two":{"0":"\/timing\/details","1":"\/search","2":"\/goods","3":"\/search","4":"\/self"},"page_three":{"0":"\/home","1":"\/search\/goods-new","2":"加入购物车","3":"\/search\/goods-new","4":"\/self\/order\/details"},"page_four":{"0":"banner","1":"","2":"\/goods","3":"\/search","4":"\/self\/order"},"page_five":{"0":"\/timing\/details","1":"","2":"\/goods","3":"\/search\/goods-new","4":"\/self\/order"},"page_six":{"0":"\/timing\/pay","1":"","2":"\/public-goods-details","3":"\/home","4":"\/self\/order\/details"},"pv":{"0":"48","1":"714","2":"331","3":"855","4":"610"},"uv":{"0":"1","1":"238","2":"6","3":"32","4":"25"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |        pv |         uv |
|:------|----------:|-----------:|
| count | 5062      | 5062       |
| mean  |   74.5421 |    3.08277 |
| std   |  244.931  |   10.8768  |
| min   |    2      |    1       |
| 25%   |   12      |    1       |
| 50%   |   27      |    1       |
| 75%   |   59      |    2       |
| max   | 9482      |  376       |