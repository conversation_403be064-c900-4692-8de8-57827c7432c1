# app_kpi_all_trade_di
* comment: 交易口径业务线kpi指标日汇总
* last_data_modified_time: 2025-09-18 02:52:28

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_all_trade_di` (
  `date` STRING COMMENT '日期',
  `manage_type` STRING COMMENT '业务线：自营，代仓，代售，批发，SAAS鲜沐自营，SAAS鲜沐代仓，SAAS品牌方自营',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `tenant_cnt` BIGINT COMMENT '租户数（saas才有）',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_order_cnt` BIGINT COMMENT '未到货售后订单数',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)'
)
COMMENT '交易口径业务线kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"manage_type":{"0":"SAAS品牌方自营","1":"SAAS鲜沐代仓","2":"SAAS鲜沐自营","3":"代仓","4":"代售"},"origin_total_amt":{"0":"282388.56","1":"183845.36","2":"90754.72","3":"198966","4":"338583.51"},"real_total_amt":{"0":"282388.56","1":"183845.36","2":"90754.72","3":"198966","4":"329342.82"},"cust_cnt":{"0":"128","1":"101","2":"389","3":"100","4":"1329"},"order_cnt":{"0":"155","1":"125","2":"441","3":"196","4":"1468"},"tenant_cnt":{"0":"20","1":"14","2":"26","3":"0","4":"0"},"delivery_amt":{"0":"6430.36","1":"2606.79","2":"238.85","3":"0","4":"2837.67"},"cust_arpu":{"0":"2206.160625","1":"1820.251089108910891089","2":"233.302622107969151671","3":"1989.66","4":"254.765620767494356659"},"order_avg":{"0":"1821.86167741935483871","1":"1470.76288","2":"205.793015873015873016","3":"1015.132653061224489796","4":"230.642717983651226158"},"after_sale_noreceived_order_cnt":{"0":"5","1":"6","2":"13","3":"0","4":"43"},"after_sale_noreceived_amt":{"0":"2640","1":"3673.54","2":"1426.17","3":"0","4":"17353.61"},"after_sale_rate":{"0":"0.009348820646275472","1":"0.019981684607106755","2":"0.015714554570825628","3":"0","4":"0.05125355927700082"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   tenant_cnt |   after_sale_noreceived_order_cnt |
|:------|-----------:|------------:|-------------:|----------------------------------:|
| count |       7    |        7    |      7       |                            7      |
| mean  |    1278.86 |     1509.71 |      8.57143 |                           44      |
| std   |    2519.08 |     2978.31 |     11.2377  |                           88.1363 |
| min   |       8    |       13    |      0       |                            0      |
| 25%   |     100.5  |      140    |      0       |                            2.5    |
| 50%   |     128    |      196    |      0       |                            6      |
| 75%   |     859    |      954.5  |     17       |                           28      |
| max   |    6897    |     8170    |     26       |                          241      |