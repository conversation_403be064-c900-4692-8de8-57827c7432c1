# app_sku_cust_delivery_mi
* comment: 人群偏好商品明细
* last_data_modified_time: 2025-09-18 03:05:32

# schema:
CREATE TABLE summerfarm_tech.`app_sku_cust_delivery_mi` (
  `month` STRING COMMENT '月份',
  `sku_id` STRING COMMENT 'sku编号',
  `category1` STRING COMMENT '一级类目',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品描述',
  `cust_cnt` BIGINT COMMENT '人群数量',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `other_spu_name` STRING COMMENT '连带商品名称',
  `other_cust_cnt` BIGINT COMMENT '连带客户数',
  `other_cdlv_origin_total_amt` DECIMAL(38,18) COMMENT '连带履约应付GMV'
)
COMMENT '人群偏好商品明细'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509","5":"202509","6":"202509","7":"202509","8":"202509","9":"202509"},"sku_id":{"0":"1003074364015","1":"1003074364015","2":"1003074364015","3":"1003074364015","4":"1003074364015","5":"1003074364015","6":"1003074364015","7":"1003074364015","8":"1003074364015","9":"1003074364015"},"category1":{"0":"乳制品","1":"乳制品","2":"乳制品","3":"乳制品","4":"乳制品","5":"乳制品","6":"乳制品","7":"乳制品","8":"乳制品","9":"乳制品"},"spu_name":{"0":"ProtagxEva乳酸黄油","1":"ProtagxEva乳酸黄油","2":"ProtagxEva乳酸黄油","3":"ProtagxEva乳酸黄油","4":"ProtagxEva乳酸黄油","5":"ProtagxEva乳酸黄油","6":"ProtagxEva乳酸黄油","7":"ProtagxEva乳酸黄油","8":"ProtagxEva乳酸黄油","9":"ProtagxEva乳酸黄油"},"sku_disc":{"0":"10KG*1箱","1":"10KG*1箱","2":"10KG*1箱","3":"10KG*1箱","4":"10KG*1箱","5":"10KG*1箱","6":"10KG*1箱","7":"10KG*1箱","8":"10KG*1箱","9":"10KG*1箱"},"cust_cnt":{"0":"81","1":"81","2":"81","3":"81","4":"81","5":"81","6":"81","7":"81","8":"81","9":"81"},"dlv_origin_total_amt":{"0":"79768","1":"79768","2":"79768","3":"79768","4":"79768","5":"79768","6":"79768","7":"79768","8":"79768","9":"79768"},"other_spu_name":{"0":"7英寸慕玛星厨妙可蓝多联名款披萨","1":"Crop's芒果果茸","2":"Crop's蓝莓果茸","3":"C味冷冻黑糖珍珠（快煮）","4":"C味原味波波晶球","5":"C味原味波波晶球（轻盈版）","6":"C味圆形小芋圆","7":"C味荔浦原味芋泥","8":"C味荔浦芋头块","9":"C味血糯米罐头"},"other_cust_cnt":{"0":"1","1":"2","2":"1","3":"1","4":"3","5":"1","6":"2","7":"1","8":"7","9":"2"},"other_cdlv_origin_total_amt":{"0":"21.8","1":"142","2":"180","3":"31","4":"115.4","5":"5.5","6":"205","7":"10","8":"951","9":"22"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   other_cust_cnt |
|:------|-----------:|-----------------:|
| count |  10000     |       10000      |
| mean  |    478.559 |           5.7307 |
| std   |    524.571 |          18.1319 |
| min   |     17     |           0      |
| 25%   |    173     |           1      |
| 50%   |    264     |           2      |
| 75%   |    500     |           4      |
| max   |   1905     |         776      |