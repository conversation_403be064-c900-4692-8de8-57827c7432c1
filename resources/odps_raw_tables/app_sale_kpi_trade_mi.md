# app_sale_kpi_trade_mi
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:48:07

# schema:
CREATE TABLE summerfarm_tech.`app_sale_kpi_trade_mi` (
  `month` STRING COMMENT '月份',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `all_cust_ratio` DECIMAL(38,18) COMMENT '市占率',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509"},"order_origin_total_amt":{"0":"67642705.1"},"order_real_total_amt":{"0":"65476143.81"},"order_cust_cnt":{"0":"44797"},"order_cust_arpu":{"0":"1509.982925195883652923"},"order_cnt":{"0":"151718"},"all_cust_ratio":{"0":"1.109760729957622"},"delivery_origin_total_amt":{"0":"63935800.24"},"delivery_real_total_amt":{"0":"61932315.833454545454546037"},"delivery_cust_cnt":{"0":"44738"},"delivery_origin_profit":{"0":"8764859.05"},"delivery_real_profit":{"0":"6761374.643454545454546037"},"delivery_after_profit":{"0":"488872.553083161430849151"},"delivery_days_avg":{"0":"2.854888461710403"},"delivery_point_cnt":{"0":"132653"},"delivery_amt":{"0":"6272502.090371384023696886"},"new_delivery_origin_total_amt":{"0":"1226734.02"},"new_delivery_real_total_amt":{"0":"1171788.04000000000000005"},"new_delivery_cust_cnt":{"0":"2261"},"new_delivery_real_profit":{"0":"126784.01000000000000005"},"old_delivery_origin_total_amt":{"0":"62709066.22"},"old_delivery_real_total_amt":{"0":"60760527.793454545454545987"},"old_delivery_cust_cnt":{"0":"42477"},"old_delivery_real_profit":{"0":"6634590.633454545454545987"},"order_sku_cnt":{"0":"1509"},"order_sku_weight":{"0":"3763554.74999998"},"delivery_sku_cnt":{"0":"1518"},"delivery_sku_weight":{"0":"3624436.480000047"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   order_cust_cnt |   order_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|-----------------:|------------:|--------------------:|---------------------:|------------------------:|------------------------:|----------------:|-------------------:|
| count |                1 |           1 |                   1 |                    1 |                       1 |                       1 |               1 |                  1 |
| mean  |            44797 |      151718 |               44738 |               132653 |                    2261 |                   42477 |            1509 |               1518 |
| std   |              nan |         nan |                 nan |                  nan |                     nan |                     nan |             nan |                nan |
| min   |            44797 |      151718 |               44738 |               132653 |                    2261 |                   42477 |            1509 |               1518 |
| 25%   |            44797 |      151718 |               44738 |               132653 |                    2261 |                   42477 |            1509 |               1518 |
| 50%   |            44797 |      151718 |               44738 |               132653 |                    2261 |                   42477 |            1509 |               1518 |
| 75%   |            44797 |      151718 |               44738 |               132653 |                    2261 |                   42477 |            1509 |               1518 |
| max   |            44797 |      151718 |               44738 |               132653 |                    2261 |                   42477 |            1509 |               1518 |