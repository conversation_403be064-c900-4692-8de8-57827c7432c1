# app_self_delivery_warehouse_kpi_mi
* comment: 履约口径kpi指标日汇总(自营)
* last_data_modified_time: 2025-09-18 03:39:37

# schema:
CREATE TABLE summerfarm_tech.`app_self_delivery_warehouse_kpi_mi` (
  `month` STRING COMMENT '月份',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `day_point_cnt` BIGINT COMMENT '日均点位',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费',
  `cust_runoff` DECIMAL(38,18) COMMENT '客户流失率',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
  `damage_rate` DECIMAL(38,18) COMMENT '货损占比',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶仓储成本',
  `heytea_arterial_roads_amt` DECIMAL(38,18) COMMENT '喜茶调拨成本',
  `heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送成本',
  `heytea_way_deliver_amt` DECIMAL(38,18) COMMENT '喜茶专配成本',
  `saas_point_cnt` BIGINT COMMENT 'SaaS点位数',
  `sample_point_cnt` BIGINT COMMENT '出样点位数',
  `after_sale_point_cnt` BIGINT COMMENT '补货点位数',
  `wholesale_point_cnt` BIGINT COMMENT '批发客户点位数',
  `heytea_point_cnt` BIGINT COMMENT '喜茶共配点位数',
  `heytea_way_point_cnt` BIGINT COMMENT '喜茶专配点位数',
  `delivery_out_times_amt` DECIMAL(38,18) COMMENT '运费+超时加单费',
  `deliver_coupon_amt` DECIMAL(38,18) COMMENT '优惠券费',
  `total_point_cnt` BIGINT COMMENT '总配送点位',
  `out_times_amt` DECIMAL(38,18) COMMENT '超时加单费',
  `precision_delivery_fee` DECIMAL(38,18) COMMENT '精准送费用'
)
COMMENT '履约口径kpi指标日汇总(自营)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509"},"origin_total_amt":{"0":"65056920.03"},"real_total_amt":{"0":"63049972.233454545454546043"},"cost_amt":{"0":"56064150.91"},"timing_origin_total_amt":{"0":"3507383"},"timing_real_total_amt":{"0":"3272386.263454545454545426"},"cust_cnt":{"0":"45584"},"order_cnt":{"0":"154411"},"point_cnt":{"0":"49444"},"day_point_cnt":{"0":"135892"},"sku_cnt":{"0":"606356"},"delivery_amt":{"0":"133791.14"},"cust_runoff":{"0":"0.1144761851520572"},"after_sale_received_amt":{"0":"162596.86"},"inventory_loss_amt":{"0":"6828.02"},"inventory_profit_amt":{"0":"6410.19"},"damage_amt":{"0":"129538.82"},"damage_rate":{"0":"0.001991161277543806"},"storage_amt":{"0":"1327926.142704809887349893"},"arterial_roads_amt":{"0":"1042439.169236149527764213"},"deliver_amt":{"0":"3981839.403346344850876726"},"self_picked_amt":{"0":"0"},"other_amt":{"0":"166121.217839971055634826"},"allocation_amt":{"0":"258667.969234109989238129"},"heytea_storage_amt":{"0":"0"},"heytea_arterial_roads_amt":{"0":"0"},"heytea_deliver_amt":{"0":"0"},"heytea_way_deliver_amt":{"0":"0"},"saas_point_cnt":{"0":"7245"},"sample_point_cnt":{"0":"823"},"after_sale_point_cnt":{"0":"131"},"wholesale_point_cnt":{"0":"100"},"heytea_point_cnt":{"0":"0"},"heytea_way_point_cnt":{"0":"0"},"delivery_out_times_amt":{"0":"257671.63"},"deliver_coupon_amt":{"0":"123880.49"},"total_point_cnt":{"0":"146643"},"out_times_amt":{"0":"280"},"precision_delivery_fee":{"0":"2040"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   point_cnt |   day_point_cnt |   sku_cnt |   saas_point_cnt |   sample_point_cnt |   after_sale_point_cnt |   wholesale_point_cnt |   heytea_point_cnt |   heytea_way_point_cnt |   total_point_cnt |
|:------|-----------:|------------:|------------:|----------------:|----------:|-----------------:|-------------------:|-----------------------:|----------------------:|-------------------:|-----------------------:|------------------:|
| count |          1 |           1 |           1 |               1 |         1 |                1 |                  1 |                      1 |                     1 |                  1 |                      1 |                 1 |
| mean  |      45584 |      154411 |       49444 |          135892 |    606356 |             7245 |                823 |                    131 |                   100 |                  0 |                      0 |            146643 |
| std   |        nan |         nan |         nan |             nan |       nan |              nan |                nan |                    nan |                   nan |                nan |                    nan |               nan |
| min   |      45584 |      154411 |       49444 |          135892 |    606356 |             7245 |                823 |                    131 |                   100 |                  0 |                      0 |            146643 |
| 25%   |      45584 |      154411 |       49444 |          135892 |    606356 |             7245 |                823 |                    131 |                   100 |                  0 |                      0 |            146643 |
| 50%   |      45584 |      154411 |       49444 |          135892 |    606356 |             7245 |                823 |                    131 |                   100 |                  0 |                      0 |            146643 |
| 75%   |      45584 |      154411 |       49444 |          135892 |    606356 |             7245 |                823 |                    131 |                   100 |                  0 |                      0 |            146643 |
| max   |      45584 |      154411 |       49444 |          135892 |    606356 |             7245 |                823 |                    131 |                   100 |                  0 |                      0 |            146643 |