# app_saas_goods_near_deadline_summary_di
* comment: saas近15天临期货品汇总
* last_data_modified_time: 2025-09-18 02:03:36

# schema:
CREATE TABLE summerfarm_tech.`app_saas_goods_near_deadline_summary_di` (
  `time_tag` STRING COMMENT '日期',
  `tenant_id` BIGINT COMMENT 'sku租户id',
  `sku_id` BIGINT COMMENT 'saas skuId',
  `warehouse_no` BIGINT COMMENT '库存仓ID',
  `warehouse_name` STRING COMMENT '仓库名称',
  `batch` STRING COMMENT '批次',
  `expiration_date` DATETIME COMMENT '有效期',
  `enter_deadline_date` DATETIME COMMENT '进入临期的日期',
  `enter_deadline_batch_stock` BIGINT COMMENT '进入临期批次库存',
  `ending_batch_stock` BIGINT COMMENT '期末库存',
  `item_id` BIGINT COMMENT '商品id',
  `sale_price` DECIMAL(38,18) COMMENT '售价'
)
COMMENT 'saas近15天临期货品汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"tenant_id":{"0":"109","1":"96","2":"38","3":"22","4":"109"},"sku_id":{"0":"121078","1":"120709","2":"109953","3":"102813","4":"121076"},"warehouse_no":{"0":"172","1":"10","2":"38","3":"92","4":"173"},"warehouse_name":{"0":"普冷武汉仓","1":"嘉兴总仓","2":"福州总仓","3":"总仓","4":"普冷长沙仓"},"batch":{"0":"0120250604065434","1":"20250312106680161","2":"20250621109260091","3":"20240510104853004","4":"0120250814762313"},"expiration_date":{"0":"2025-10-10","1":"2025-10-13","2":"2025-12-09","3":"2025-10-26","4":"2025-09-18"},"enter_deadline_date":{"0":"2025-09-04","1":"2025-09-05","2":"2025-09-10","3":"2025-09-03","4":"2025-09-15"},"enter_deadline_batch_stock":{"0":"1650","1":"1","2":"1","3":"420","4":"30"},"ending_batch_stock":{"0":"1650","1":"1","2":"0","3":"420","4":"30"},"item_id":{"0":"38500","1":"37736","2":"19074","3":"1884","4":"38584"},"sale_price":{"0":"26.5","1":"0","2":"None","3":"38","4":"270"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |    sku_id |   warehouse_no | expiration_date               | enter_deadline_date           |   enter_deadline_batch_stock |   ending_batch_stock |   item_id |
|:------|------------:|----------:|---------------:|:------------------------------|:------------------------------|-----------------------------:|---------------------:|----------:|
| count |     22      |     22    |        22      | 22                            | 22                            |                        22    |                22    |      22   |
| mean  |     48.0909 | 112208    |        84.3182 | 2025-11-12 10:54:32.727272704 | 2025-09-09 10:54:32.727272704 |                      1214.91 |              1214.32 |   22281.5 |
| min   |      2      | 100590    |         2      | 2025-09-18 00:00:00           | 2025-09-03 00:00:00           |                         1    |                 0    |     591   |
| 25%   |     22      | 102774    |        45.75   | 2025-10-10 06:00:00           | 2025-09-05 00:00:00           |                         4    |                 3.5  |    1969.5 |
| 50%   |     35      | 113346    |        92      | 2025-10-17 00:00:00           | 2025-09-09 00:00:00           |                        69    |                67    |   30313   |
| 75%   |     87.25   | 120984    |        92      | 2025-12-03 06:00:00           | 2025-09-14 00:00:00           |                       391.75 |               391.75 |   38309   |
| max   |    109      | 121662    |       173      | 2026-05-29 00:00:00           | 2025-09-17 00:00:00           |                     20000    |             20000    |   38813   |
| std   |     36.4573 |   8452.94 |        52.0068 | nan                           | nan                           |                      4232.91 |              4233.09 |   16030.8 |