# app_operate_kpi_trade_wi
* comment: 交易（订单）运营KPI指标汇总
* last_data_modified_time: 2025-09-18 02:46:30

# schema:
CREATE TABLE summerfarm_tech.`app_operate_kpi_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `cust_cnt` BIGINT COMMENT '交易客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `consign_origin_total_amt` DECIMAL(38,18) COMMENT '代售应付总金额',
  `consign_real_total_amt` DECIMAL(38,18) COMMENT '代售实付总金额',
  `consign_preferential_amt` DECIMAL(38,18) COMMENT '代售营销金额',
  `consign_cust_cnt` BIGINT COMMENT '代售客户数',
  `consign_order_cnt` BIGINT COMMENT '代售订单数',
  `register_cust_cnt` BIGINT COMMENT '注册客户数',
  `new_active_cust_cnt` BIGINT COMMENT '有效拉新客户数（注册且首日下单金额>=15）'
)
COMMENT '交易（订单）运营KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025"},"week_of_year":{"0":"38"},"monday":{"0":"20250915"},"sunday":{"0":"20250921"},"origin_total_amt":{"0":"11324368.66"},"real_total_amt":{"0":"10928100.35"},"cust_cnt":{"0":"17816"},"cust_arpu":{"0":"635.629134485855410867"},"order_cnt":{"0":"25674"},"order_avg":{"0":"441.083144815766923736"},"consign_origin_total_amt":{"0":"0"},"consign_real_total_amt":{"0":"0"},"consign_preferential_amt":{"0":"0"},"consign_cust_cnt":{"0":"0"},"consign_order_cnt":{"0":"0"},"register_cust_cnt":{"0":"949"},"new_active_cust_cnt":{"0":"201"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   order_cnt |   consign_cust_cnt |   consign_order_cnt |   register_cust_cnt |   new_active_cust_cnt |
|:------|---------------:|-----------:|------------:|-------------------:|--------------------:|--------------------:|----------------------:|
| count |              1 |          1 |           1 |                  1 |                   1 |                   1 |                     1 |
| mean  |             38 |      17816 |       25674 |                  0 |                   0 |                 949 |                   201 |
| std   |            nan |        nan |         nan |                nan |                 nan |                 nan |                   nan |
| min   |             38 |      17816 |       25674 |                  0 |                   0 |                 949 |                   201 |
| 25%   |             38 |      17816 |       25674 |                  0 |                   0 |                 949 |                   201 |
| 50%   |             38 |      17816 |       25674 |                  0 |                   0 |                 949 |                   201 |
| 75%   |             38 |      17816 |       25674 |                  0 |                   0 |                 949 |                   201 |
| max   |             38 |      17816 |       25674 |                  0 |                   0 |                 949 |                   201 |