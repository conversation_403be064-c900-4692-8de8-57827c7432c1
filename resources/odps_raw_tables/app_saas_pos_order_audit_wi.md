# app_saas_pos_order_audit_wi
* comment: saas门店进销稽核表(周)
* last_data_modified_time: 2025-09-18 04:17:24

# schema:
CREATE TABLE summerfarm_tech.`app_saas_pos_order_audit_wi` (
  `channel_type` BIGINT COMMENT '渠道类型：1=美团',
  `tenant_id` BIGINT COMMENT '租户id',
  `report_week` STRING COMMENT '稽核自然周-周一日期',
  `out_store_code` STRING COMMENT '外部系统门店code',
  `out_store_name` STRING COMMENT '外部系统门店名称',
  `merchant_store_id` BIGINT COMMENT '帆台门店id',
  `merchant_store_code` STRING COMMENT '帆台门店code',
  `out_item_code` STRING COMMENT '外部系统物料编码',
  `out_item_name` STRING COMMENT '外部系统物料名称',
  `market_item_id` BIGINT COMMENT '帆台商品id',
  `specification` STRING COMMENT '规格',
  `specification_unit` STRING COMMENT '规格单位',
  `use_count` DECIMAL(38,18) COMMENT '销用总量',
  `need_buy_count` DECIMAL(38,18) COMMENT '应进货总量',
  `real_buy_count` BIGINT COMMENT '实际帆台进货总量'
)
COMMENT 'saas门店进销稽核表(周)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"channel_type":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"tenant_id":{"0":"32","1":"32","2":"32","3":"32","4":"32"},"report_week":{"0":"20240226","1":"20240226","2":"20240226","3":"20240226","4":"20240226"},"out_store_code":{"0":"870753507","1":"870753507","2":"870753507","3":"870753507","4":"870753507"},"out_store_name":{"0":"新加坡斯味洛鲜奶茶（罗村店）","1":"新加坡斯味洛鲜奶茶（罗村店）","2":"新加坡斯味洛鲜奶茶（罗村店）","3":"新加坡斯味洛鲜奶茶（罗村店）","4":"新加坡斯味洛鲜奶茶（罗村店）"},"merchant_store_id":{"0":"354819","1":"354819","2":"354819","3":"354819","4":"354819"},"merchant_store_code":{"0":"152","1":"152","2":"152","3":"152","4":"152"},"out_item_code":{"0":"WL0004","1":"WL0007","2":"CY0003","3":"WL0001","4":"WL0002"},"out_item_name":{"0":"糖浆","1":"草莓酱","2":"锡兰红茶茶叶","3":"纯牛奶","4":"咖奶"},"market_item_id":{"0":"2123","1":"2119","2":"2116","3":"14456","4":"2200"},"specification":{"0":"1.2KG*15瓶","1":"1.3KG*12桶","2":"500G*20箱","3":"0_1L*12盒","4":"0_1L*12盒"},"specification_unit":{"0":"箱","1":"箱","2":"箱","3":"箱","4":"箱"},"use_count":{"0":"0.054451","1":"0.003846","2":"0.0024","3":"0.088337","4":"0.028337"},"need_buy_count":{"0":"0.054451","1":"0.003846","2":"0.0024","3":"0.088337","4":"0.028337"},"real_buy_count":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20240303","1":"20240303","2":"20240303","3":"20240303","4":"20240303"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   channel_type |   tenant_id |   merchant_store_id |   market_item_id |   real_buy_count |
|:------|---------------:|------------:|--------------------:|-----------------:|-----------------:|
| count |           1837 |        1837 |                1837 |          1837    |       1837       |
| mean  |              2 |          32 |              144508 |          4551.88 |          0.28797 |
| std   |              0 |           0 |              173539 |          5338.65 |          1.82196 |
| min   |              2 |          32 |                2873 |          2109    |          0       |
| 25%   |              2 |          32 |                2919 |          2116    |          0       |
| 50%   |              2 |          32 |                3368 |          2121    |          0       |
| 75%   |              2 |          32 |              352720 |          2200    |          0       |
| max   |              2 |          32 |              374955 |         18105    |         40       |