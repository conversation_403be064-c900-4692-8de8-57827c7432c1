# app_saas_merchant_store_purchase_day_di
* comment: saas门店采购概况表(日维度)
* last_data_modified_time: 2025-09-18 02:19:10

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_purchase_day_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `pay_type` BIGINT COMMENT '支付方式：1 现结 2 账期',
  `store_in_operation_num` BIGINT COMMENT '经营中门店数',
  `direct_store_in_operation_num` BIGINT COMMENT '经营中直营门店数',
  `join_store_in_operation_num` BIGINT COMMENT '经营中加盟门店数',
  `managed_store_in_operation_num` BIGINT COMMENT '经营中托管门店数',
  `purchased_store_num` BIGINT COMMENT '采购门店数',
  `purchased_direct_store_num` BIGINT COMMENT '采购直营门店数',
  `purchased_join_store_num` BIGINT COMMENT '采购加盟门店数',
  `purchased_managed_store_num` BIGINT COMMENT '采购托管门店数'
)
COMMENT 'saas门店采购概况表(日维度)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"4","3":"4","4":"6"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"pay_type":{"0":"1","1":"2","2":"1","3":"2","4":"1"},"store_in_operation_num":{"0":"1339","1":"1339","2":"232","3":"232","4":"56"},"direct_store_in_operation_num":{"0":"914","1":"914","2":"12","3":"12","4":"16"},"join_store_in_operation_num":{"0":"418","1":"418","2":"220","3":"220","4":"39"},"managed_store_in_operation_num":{"0":"7","1":"7","2":"0","3":"0","4":"1"},"purchased_store_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchased_direct_store_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchased_join_store_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchased_managed_store_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   pay_type |   store_in_operation_num |   direct_store_in_operation_num |   join_store_in_operation_num |   managed_store_in_operation_num |   purchased_store_num |   purchased_direct_store_num |   purchased_join_store_num |   purchased_managed_store_num |
|:------|------------:|-----------:|-------------------------:|--------------------------------:|------------------------------:|---------------------------------:|----------------------:|-----------------------------:|---------------------------:|------------------------------:|
| count |    180      | 180        |                  180     |                         180     |                      180      |                        180       |             180       |                   180        |                  180       |                   180         |
| mean  |     66.6222 |   1.5      |                  143.856 |                          53.1   |                       84.1556 |                          1.67778 |               3.03889 |                     0.677778 |                    2.33333 |                     0.0277778 |
| std   |     35.355  |   0.501395 |                  320.675 |                         208.143 |                      234.659  |                          7.5084  |              10.7003  |                     2.898    |                   10.203   |                     0.195781  |
| min   |      2      |   1        |                    1     |                           0     |                        0      |                          0       |               0       |                     0        |                    0       |                     0         |
| 25%   |     40      |   1        |                    7     |                           1     |                        0      |                          0       |               0       |                     0        |                    0       |                     0         |
| 50%   |     68.5    |   1.5      |                   14     |                           4     |                        6      |                          0       |               0       |                     0        |                    0       |                     0         |
| 75%   |     97      |   2        |                  112     |                          11     |                       47      |                          1       |               0       |                     0        |                    0       |                     0         |
| max   |    123      |   2        |                 1740     |                        1452     |                     1738      |                         70       |              76       |                    31        |                   76       |                     2         |