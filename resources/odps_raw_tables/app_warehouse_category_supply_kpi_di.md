# app_warehouse_category_supply_kpi_di
* comment: 供应链kpi
* last_data_modified_time: 2025-09-18 03:04:51

# schema:
CREATE TABLE summerfarm_tech.`app_warehouse_category_supply_kpi_di` (
  `date` STRING COMMENT '日期',
  `warehouse_no` BIGINT COMMENT '库存仓',
  `warehouse_name` STRING COMMENT '库存仓',
  `category` STRING COMMENT '鲜果，标品',
  `sale_out_time` DECIMAL(38,18) COMMENT '售罄时长',
  `on_sale_time` DECIMAL(38,18) COMMENT '上架时长',
  `store_cost_amt` DECIMAL(38,18) COMMENT '期末库存成本',
  `sale_amt` DECIMAL(38,18) COMMENT '销售出库成本',
  `temporary_store_amt` DECIMAL(38,18) COMMENT '临保成本',
  `damage_amt` DECIMAL(38,18) COMMENT '滞销过期货损出库成本',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV'
)
COMMENT '供应链kpi'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"warehouse_no":{"0":"59","1":"59","2":"69","3":"7","4":"60","5":"125","6":"125","7":"2","8":"10","9":"10"},"warehouse_name":{"0":"南宁总仓","1":"南宁总仓","2":"东莞总仓","3":"山东总仓","4":"昆明总仓","5":"南京总仓","6":"南京总仓","7":"上海总仓","8":"嘉兴总仓","9":"嘉兴总仓"},"category":{"0":"标品","1":"鲜果","2":"鲜果","3":"标品","4":"标品","5":"标品","6":"鲜果","7":"鲜果","8":"标品","9":"鲜果"},"sale_out_time":{"0":"2481.306111111111096","1":"268.4572222222222244","2":"1366.36333333333333161","3":"0","4":"1556.93527777777778","5":"4451.6783333333333391","6":"2098.449722222222215","7":"0","8":"28755.386111111111130668","9":"3968.09222222222222411"},"on_sale_time":{"0":"12713.628333333333339","1":"1621.837222222222222","2":"4888.19055555555556","3":"0","4":"8112","5":"23914.410555555555551","6":"5505.581388888888883","7":"0","8":"79172.62583333333334","9":"10873.0922222222222156"},"store_cost_amt":{"0":"918513.9","1":"18387.74","2":"301193.83","3":"620","4":"849016.79","5":"3610322.5","6":"153472.76","7":"0","8":"15778907.95","9":"604969.82"},"sale_amt":{"0":"85145.82","1":"8155.26","2":"137324.38","3":"0","4":"34247.3","5":"174223.83","6":"46278.58","7":"0","8":"886300.03","9":"391118.66"},"temporary_store_amt":{"0":"37","1":"0","2":"0","3":"0","4":"0","5":"174.26","6":"0","7":"0","8":"971.95","9":"0"},"damage_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"origin_total_amt":{"0":"25060.6","1":"3907.98","2":"196092.67","3":"0","4":"41063","5":"201352.48","6":"61764.45","7":"0","8":"852464.45","9":"437749.12"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |
|:------|---------------:|
| count |        46      |
| mean  |        70.6087 |
| std   |        48.1384 |
| min   |         1      |
| 25%   |        31.25   |
| 50%   |        63      |
| 75%   |       114.25   |
| max   |       155      |