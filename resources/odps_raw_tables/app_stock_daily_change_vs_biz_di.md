# app_stock_daily_change_vs_biz_di
* comment: 库存每日变更对比业务单
* last_data_modified_time: 2025-09-18 03:34:16

# schema:
CREATE TABLE summerfarm_tech.`app_stock_daily_change_vs_biz_di` (
  `pt` STRING COMMENT 'pt',
  `change_date` DATETIME COMMENT '变更日期',
  `warehouse_no` BIGINT COMMENT '仓库编码',
  `warehouse_name` STRING COMMENT '仓库名称',
  `sku` STRING COMMENT 'sku编码',
  `pd_name` STRING COMMENT '商品名称',
  `specifications` STRING COMMENT '商品规格',
  `change_type` STRING COMMENT '变更类型',
  `stock_change_quantity` BIGINT COMMENT '流水变更数量',
  `biz_change_quantity` BIGINT COMMENT '业务变更数量'
)
COMMENT '库存每日变更对比业务单'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"pt":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"change_date":{"0":"2025-09-17","1":"2025-09-17","2":"2025-09-17","3":"2025-09-17","4":"2025-09-17","5":"2025-09-17","6":"2025-09-17","7":"2025-09-17","8":"2025-09-17","9":"2025-09-17"},"warehouse_no":{"0":"10","1":"10","2":"10","3":"10","4":"10","5":"10","6":"10","7":"10","8":"10","9":"10"},"warehouse_name":{"0":"嘉兴总仓","1":"嘉兴总仓","2":"嘉兴总仓","3":"嘉兴总仓","4":"嘉兴总仓","5":"嘉兴总仓","6":"嘉兴总仓","7":"嘉兴总仓","8":"嘉兴总仓","9":"嘉兴总仓"},"sku":{"0":"1007801326018","1":"1007801326057","2":"1007873317477","3":"1007873317528","4":"1012310116172","5":"1043576870313","6":"1043683426715","7":"1082314281652","8":"1082481322642","9":"1120453602288"},"pd_name":{"0":"速拓厚椰乳","1":"速拓厚椰乳","2":"肆饮厚椰乳","3":"肆饮厚椰乳","4":"杯套","5":"臻行者海苔酥脆松","6":"臻行者原味松松一号","7":"璞季冻干草莓丁","8":"璞季冻干草莓碎","9":"PET冷饮杯盖"},"specifications":{"0":"1L*1盒(有盖)","1":"1L*12盒(有盖)","2":"1L*12盒","3":"1L*1盒","4":"50个*1包\/125*110*60mm","5":"2KG*1包","6":"1KG*1包","7":"100g*1包\/含糖\/5*5mm","8":"100g*1包\/含糖\/1-3mm","9":"100个*10条\/98mm\/98直饮盖"},"change_type":{"0":"销售出库","1":"销售出库","2":"销售出库","3":"销售出库","4":"销售出库","5":"销售出库","6":"销售出库","7":"销售出库","8":"销售出库","9":"销售出库"},"stock_change_quantity":{"0":"4","1":"3","2":"3","3":"2","4":"8","5":"4","6":"4","7":"5","8":"2","9":"2"},"biz_change_quantity":{"0":"4","1":"3","2":"3","3":"2","4":"8","5":"4","6":"4","7":"5","8":"2","9":"2"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | change_date         |   warehouse_no |   stock_change_quantity |   biz_change_quantity |
|:------|:--------------------|---------------:|------------------------:|----------------------:|
| count | 629                 |       629      |               629       |             629       |
| mean  | 2025-09-17 00:00:00 |        71.8951 |                 4.28458 |               4.28458 |
| min   | 2025-09-17 00:00:00 |        10      |                 1       |               1       |
| 25%   | 2025-09-17 00:00:00 |        24      |                 1       |               1       |
| 50%   | 2025-09-17 00:00:00 |        69      |                 2       |               2       |
| 75%   | 2025-09-17 00:00:00 |       121      |                 4       |               4       |
| max   | 2025-09-17 00:00:00 |       155      |               102       |             102       |
| std   | nan                 |        47.7196 |                 7.72023 |               7.72023 |