# app_kpi_all_trade_wi
* comment: 交易口径业务线kpi指标周汇总
* last_data_modified_time: 2025-09-18 02:55:43

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_all_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `manage_type` STRING COMMENT '业务线：自营，代仓，代售，批发，SAAS鲜沐自营，SAAS鲜沐代仓，SAAS品牌方自营',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `tenant_cnt` BIGINT COMMENT '租户数（saas才有）',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_order_cnt` BIGINT COMMENT '未到货售后订单数',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)'
)
COMMENT '交易口径业务线kpi指标周汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"manage_type":{"0":"SAAS品牌方自营","1":"SAAS鲜沐代仓","2":"SAAS鲜沐自营","3":"代仓","4":"代售"},"origin_total_amt":{"0":"816300.39","1":"743125.34","2":"327947.65","3":"3611276.76","4":"1022570.79"},"real_total_amt":{"0":"816300.39","1":"743125.34","2":"327947.65","3":"3611276.76","4":"998940.87"},"cust_cnt":{"0":"364","1":"252","2":"1013","3":"230","4":"3617"},"order_cnt":{"0":"514","1":"421","2":"1663","3":"535","4":"4322"},"tenant_cnt":{"0":"24","1":"17","2":"28","3":"0","4":"0"},"delivery_amt":{"0":"13532.37","1":"10925.09","2":"809.55","3":"0","4":"8161.11"},"cust_arpu":{"0":"2242.583489010989010989","1":"2948.910079365079365079","2":"323.739042448173741362","3":"15701.203304347826086957","4":"282.712410837710810064"},"order_avg":{"0":"1588.133054474708171206","1":"1765.14332541567695962","2":"197.20243535778713169","3":"6750.050018691588785047","4":"236.596665895418787598"},"after_sale_noreceived_order_cnt":{"0":"22","1":"13","2":"24","3":"0","4":"124"},"after_sale_noreceived_amt":{"0":"25467.5","1":"9792.04","2":"4370","3":"0","4":"37222.64"},"after_sale_rate":{"0":"0.031198686552140444","1":"0.013176835014130994","2":"0.013325297497939077","3":"0","4":"0.036401039775446744"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   cust_cnt |   order_cnt |   tenant_cnt |   after_sale_noreceived_order_cnt |
|:------|---------------:|-----------:|------------:|-------------:|----------------------------------:|
| count |              7 |       7    |        7    |      7       |                             7     |
| mean  |             38 |    3237.71 |     4578.86 |      9.85714 |                           138.429 |
| std   |              0 |    6271.08 |     8929.91 |     12.7073  |                           288.751 |
| min   |             38 |      13    |       40    |      0       |                             0     |
| 25%   |             38 |     241    |      467.5  |      0       |                             6.5   |
| 50%   |             38 |     364    |      535    |      0       |                            22     |
| 75%   |             38 |    2315    |     2992.5  |     20.5     |                            74     |
| max   |             38 |   17175    |    24557    |     28       |                           786     |