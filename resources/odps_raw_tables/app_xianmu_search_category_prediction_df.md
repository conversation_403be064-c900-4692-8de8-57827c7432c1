# app_xianmu_search_category_prediction_df
* comment: 根据用户的点击历史记录做的query-类目的预测结果
* last_data_modified_time: 2025-09-18 05:02:04

# schema:
CREATE TABLE summerfarm_tech.`app_xianmu_search_category_prediction_df` (
  `query` STRING COMMENT '搜索词',
  `category4` STRING COMMENT '四级类目名称',
  `category4_id` STRING COMMENT '四级类目ID',
  `category3` STRING COMMENT '三级类目名称',
  `click_cnt` BIGINT COMMENT '点击次数',
  `category_rank` DOUBLE COMMENT '该四级类目的商品在该query的搜索结果页的点击数排名',
  `category_percentile` DOUBLE COMMENT '该四级类目百分位',
  `create_time` STRING COMMENT '创建时间'
)
COMMENT '根据用户的点击历史记录做的query-类目的预测结果'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc
LIFECYCLE 365

# head data:
{"query":{"0":"芒果","1":"牛奶","2":"蓝莓","3":"草莓","4":"柠檬","5":"西瓜","6":"奶油","7":"牛油果","8":"安佳","9":"鸡蛋"},"category4":{"0":"芒果","1":"常温牛奶","2":"蓝莓","3":"草莓","4":"柠檬","5":"西瓜","6":"搅打型稀奶油","7":"牛油果","8":"搅打型稀奶油","9":"新鲜蛋类"},"category4_id":{"0":"533","1":"607","2":"567","3":"555","4":"527","5":"550","6":"605","7":"536","8":"605","9":"871"},"category3":{"0":"核果类","1":"液体乳","2":"浆果类","3":"浆果类","4":"柑果类","5":"瓠果类","6":"稀奶油","7":"核果类","8":"稀奶油","9":"蛋类"},"click_cnt":{"0":"40192","1":"20194","2":"19401","3":"18181","4":"16694","5":"11600","6":"9518","7":"9477","8":"9133","9":"6388"},"category_rank":{"0":"1.0","1":"1.0","2":"1.0","3":"1.0","4":"1.0","5":"1.0","6":"1.0","7":"1.0","8":"1.0","9":"1.0"},"category_percentile":{"0":"0.9502328770361964","1":"0.9536267472610502","2":"0.9522897953173317","3":"0.9366821226172076","4":"0.9824623352165726","5":"0.9812214515310438","6":"0.9950862519602718","7":"0.9161832946635731","8":"0.8483975847654436","9":"0.9648089412475457"},"create_time":{"0":"2025-09-18 05:01:57","1":"2025-09-18 05:01:57","2":"2025-09-18 05:01:57","3":"2025-09-18 05:01:57","4":"2025-09-18 05:01:57","5":"2025-09-18 05:01:57","6":"2025-09-18 05:01:57","7":"2025-09-18 05:01:57","8":"2025-09-18 05:01:57","9":"2025-09-18 05:01:57"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   click_cnt |   category_rank |   category_percentile |
|:------|------------:|----------------:|----------------------:|
| count |    3585     |    3585         |        3585           |
| mean  |     171.358 |       0.876987  |           0.753975    |
| std   |    1060.93  |       0.229678  |           0.377597    |
| min   |       7     |       0.0769231 |           0.000212781 |
| 25%   |      11     |       1         |           0.492344    |
| 50%   |      23     |       1         |           1           |
| 75%   |      69     |       1         |           1           |
| max   |   40192     |       1         |           1           |