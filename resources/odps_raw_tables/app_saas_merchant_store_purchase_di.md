# app_saas_merchant_store_purchase_di
* comment: saas门店采购概况表
* last_data_modified_time: 2025-09-18 02:38:17

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_purchase_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `type` BIGINT COMMENT '时间标签类型：1、日 2、周 3、月',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `pay_type` BIGINT COMMENT '支付方式：1 现结 2 账期',
  `store_in_operation_num` BIGINT COMMENT '经营中门店数',
  `direct_store_in_operation_num` BIGINT COMMENT '经营中直营门店数',
  `join_store_in_operation_num` BIGINT COMMENT '经营中加盟门店数',
  `managed_store_in_operation_num` BIGINT COMMENT '经营中托管门店数',
  `purchased_store_num` BIGINT COMMENT '采购门店数',
  `purchased_direct_store_num` BIGINT COMMENT '采购直营门店数',
  `purchased_join_store_num` BIGINT COMMENT '采购加盟门店数',
  `purchased_managed_store_num` BIGINT COMMENT '采购托管门店数',
  `last_purchased_store_num` BIGINT COMMENT '上周期采购门店数',
  `purchased_store_chain` DECIMAL(38,18) COMMENT '采购门店数环比',
  `last_purchased_direct_store_num` BIGINT COMMENT '上周期采购直营门店数',
  `purchased_direct_store_chain` DECIMAL(38,18) COMMENT '采购直营门店数环比',
  `last_purchased_join_store_num` BIGINT COMMENT '上周期采购加盟门店数',
  `purchased_join_store_chain` DECIMAL(38,18) COMMENT '采购加盟门店数环比',
  `last_purchased_managed_store_num` BIGINT COMMENT '上周期采购托管门店数',
  `purchased_managed_store_chain` DECIMAL(38,18) COMMENT '采购托管门店数环比'
)
COMMENT 'saas门店采购概况表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"4","3":"4","4":"6"},"type":{"0":"3","1":"3","2":"3","3":"3","4":"3"},"time_tag":{"0":"20250901","1":"20250901","2":"20250901","3":"20250901","4":"20250901"},"pay_type":{"0":"1","1":"2","2":"1","3":"2","4":"1"},"store_in_operation_num":{"0":"1339","1":"1339","2":"232","3":"232","4":"56"},"direct_store_in_operation_num":{"0":"914","1":"914","2":"12","3":"12","4":"16"},"join_store_in_operation_num":{"0":"418","1":"418","2":"220","3":"220","4":"39"},"managed_store_in_operation_num":{"0":"7","1":"7","2":"0","3":"0","4":"1"},"purchased_store_num":{"0":"3","1":"4","2":"0","3":"0","4":"0"},"purchased_direct_store_num":{"0":"3","1":"2","2":"0","3":"0","4":"0"},"purchased_join_store_num":{"0":"0","1":"2","2":"0","3":"0","4":"0"},"purchased_managed_store_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"last_purchased_store_num":{"0":"2","1":"1","2":"0","3":"0","4":"0"},"purchased_store_chain":{"0":"50","1":"300","2":"0","3":"0","4":"0"},"last_purchased_direct_store_num":{"0":"1","1":"0","2":"0","3":"0","4":"0"},"purchased_direct_store_chain":{"0":"200","1":"0","2":"0","3":"0","4":"0"},"last_purchased_join_store_num":{"0":"1","1":"1","2":"0","3":"0","4":"0"},"purchased_join_store_chain":{"0":"-100","1":"100","2":"0","3":"0","4":"0"},"last_purchased_managed_store_num":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchased_managed_store_chain":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |       type |   pay_type |   store_in_operation_num |   direct_store_in_operation_num |   join_store_in_operation_num |   managed_store_in_operation_num |   purchased_store_num |   purchased_direct_store_num |   purchased_join_store_num |   purchased_managed_store_num |   last_purchased_store_num |   last_purchased_direct_store_num |   last_purchased_join_store_num |   last_purchased_managed_store_num |
|:------|------------:|-----------:|-----------:|-------------------------:|--------------------------------:|------------------------------:|---------------------------------:|----------------------:|-----------------------------:|---------------------------:|------------------------------:|---------------------------:|----------------------------------:|--------------------------------:|-----------------------------------:|
| count |    540      | 540        | 540        |                  540     |                         540     |                      540      |                        540       |             540       |                    540       |                  540       |                    540        |                  540       |                         540       |                       540       |                        540         |
| mean  |     66.6222 |   2        |   1.5      |                  143.863 |                          53.1   |                       84.1556 |                          1.68519 |               8.11852 |                      1.40741 |                    6.64815 |                      0.062963 |                    7.42037 |                           1.4963  |                         5.85556 |                          0.0685185 |
| std   |     35.2894 |   0.817254 |   0.500464 |                  320.083 |                         207.757 |                      234.223  |                          7.49328 |              35.5004  |                      6.15604 |                   34.8082  |                      0.354822 |                   31.8516  |                           7.03147 |                        30.8753  |                          0.409681  |
| min   |      2      |   1        |   1        |                    1     |                           0     |                        0      |                          0       |               0       |                      0       |                    0       |                      0        |                    0       |                           0       |                         0       |                          0         |
| 25%   |     40      |   1        |   1        |                    7     |                           1     |                        0      |                          0       |               0       |                      0       |                    0       |                      0        |                    0       |                           0       |                         0       |                          0         |
| 50%   |     68.5    |   2        |   1.5      |                   14     |                           4     |                        6      |                          0       |               0       |                      0       |                    0       |                      0        |                    0       |                           0       |                         0       |                          0         |
| 75%   |     97      |   3        |   2        |                  112     |                          11     |                       47      |                          1       |               1       |                      0       |                    0       |                      0        |                    0       |                           0       |                         0       |                          0         |
| max   |    123      |   3        |   2        |                 1740     |                        1452     |                     1738      |                         70       |             385       |                     93       |                  385       |                      4        |                  402       |                          91       |                       400       |                          4         |