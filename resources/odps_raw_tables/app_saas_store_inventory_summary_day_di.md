# app_saas_store_inventory_summary_day_di
* comment: 门店出入库汇总日表
* last_data_modified_time: 2025-09-18 02:13:17

# schema:
CREATE TABLE summerfarm_tech.`app_saas_store_inventory_summary_day_di` (
  `summary_date` DATETIME COMMENT '汇总日期',
  `tenant_id` BIGINT COMMENT '租户id',
  `store_id` BIGINT COMMENT '门店id',
  `item_id` BIGINT COMMENT '商品sku id',
  `specification` STRING COMMENT '商品sku 规格',
  `main_picture` STRING COMMENT '图片',
  `title` STRING COMMENT '商品sku 标题',
  `store_inventory_unit` STRING COMMENT '商品库存单位',
  `initial_quantity` DECIMAL(38,18) COMMENT '期初数量',
  `initial_amount` DECIMAL(38,18) COMMENT '期初金额',
  `initial_unit_price` DECIMAL(38,18) COMMENT '期初库存单位单价',
  `other_in_quantity` DECIMAL(38,18) COMMENT '其他入库数量',
  `other_in_amount` DECIMAL(38,18) COMMENT '其他入库金额',
  `order_in_quantity` DECIMAL(38,18) COMMENT '订货入库数量',
  `order_in_amount` DECIMAL(38,18) COMMENT '订货入库金额',
  `stock_gain_in_quantity` DECIMAL(38,18) COMMENT '盘盈入库数量',
  `stock_gain_in_amount` DECIMAL(38,18) COMMENT '盘盈入库金额',
  `reship_in_quantity` DECIMAL(38,18) COMMENT '补发入库数量',
  `reship_in_amount` DECIMAL(38,18) COMMENT '补发入库金额',
  `total_in_quantity` DECIMAL(38,18) COMMENT '入库合计数量',
  `total_in_amount` DECIMAL(38,18) COMMENT '入库合计金额',
  `other_out_quantity` DECIMAL(38,18) COMMENT '其他出库数量',
  `other_out_amount` DECIMAL(38,18) COMMENT '其他出库金额',
  `sales_out_quantity` DECIMAL(38,18) COMMENT '销售出库数量',
  `sales_out_amount` DECIMAL(38,18) COMMENT '销售出库金额',
  `stock_loss_out_quantity` DECIMAL(38,18) COMMENT '盘亏出库数量',
  `stock_loss_out_amount` DECIMAL(38,18) COMMENT '盘亏出库金额',
  `return_out_quantity` DECIMAL(38,18) COMMENT '退货出库数量',
  `out_loss_amount` DECIMAL(38,18) COMMENT '报损出库金额',
  `out_loss_quantity` DECIMAL(38,18) COMMENT '报损出库数量',
  `return_out_amount` DECIMAL(38,18) COMMENT '退货出库金额',
  `total_out_quantity` DECIMAL(38,18) COMMENT '出库合计数量',
  `total_out_amount` DECIMAL(38,18) COMMENT '出库合计金额',
  `final_quantity` DECIMAL(38,18) COMMENT '期末数量',
  `final_amount` DECIMAL(38,18) COMMENT '期末金额',
  `final_unit_price` DECIMAL(38,18) COMMENT '期末库存单位单价'
)
COMMENT '门店出入库汇总日表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"summary_date":{"0":"2025-09-17","1":"2025-09-17","2":"2025-09-17","3":"2025-09-17","4":"2025-09-17"},"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"store_id":{"0":"4","1":"493","2":"348361","3":"348361","4":"348361"},"item_id":{"0":"27573","1":"32576","2":"27572","3":"35496","4":"35604"},"specification":{"0":"0_1包*100个","1":"0_1箱*12盒","2":"0_1箱*1000个","3":"1盒*10个","4":"1箱*10瓶"},"main_picture":{"0":"upload\/fmolehkxv8ps88ebd.jpg","1":"upload\/639uiuq8dgh6sdvji.png","2":"upload\/fmolehkxv8ps88ebd.jpg","3":"https:\/\/azure.cosfo.cn\/upload\/1ddkryvrlryj5oile.png","4":"upload\/mup0a9m3sno1i9e7z.png"},"title":{"0":"杯套","1":"安佳牛奶(测试商品)","2":"杯套","3":"真茶屋2025手绘日历","4":"奶油"},"store_inventory_unit":{"0":"个","1":"瓶","2":"个","3":"盒","4":"瓶"},"initial_quantity":{"0":"100","1":"32","2":"1000","3":"20","4":"12"},"initial_amount":{"0":"0","1":"0","2":"0","3":"24","4":"0"},"initial_unit_price":{"0":"0","1":"0","2":"0","3":"1.2","4":"0"},"other_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_gain_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_gain_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"reship_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"reship_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"total_in_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"total_in_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"other_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"sales_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"sales_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_loss_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"stock_loss_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"return_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"out_loss_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"out_loss_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"return_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"total_out_quantity":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"total_out_amount":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"final_quantity":{"0":"100","1":"32","2":"1000","3":"60","4":"12"},"final_amount":{"0":"0","1":"0","2":"0","3":"72","4":"0"},"final_unit_price":{"0":"0","1":"0","2":"0","3":"1.2","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | summary_date        |   tenant_id |   store_id |   item_id |
|:------|:--------------------|------------:|-----------:|----------:|
| count | 3257                |   3257      |       3257 |   3257    |
| mean  | 2025-09-17 00:00:00 |     60.8069 |     361198 |  32128.4  |
| min   | 2025-09-17 00:00:00 |      2      |          4 |      1    |
| 25%   | 2025-09-17 00:00:00 |      8      |     409419 |  27654    |
| 50%   | 2025-09-17 00:00:00 |     75      |     409436 |  33361    |
| 75%   | 2025-09-17 00:00:00 |     88      |     455955 |  40067    |
| max   | 2025-09-17 00:00:00 |    100      |     539049 |  44829    |
| std   | nan                 |     36.015  |     175829 |   9997.43 |