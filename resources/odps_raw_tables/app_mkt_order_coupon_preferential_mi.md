# app_mkt_order_coupon_preferential_mi
* comment: 优惠券使用明细月表
* last_data_modified_time: 2025-09-18 03:37:13

# schema:
CREATE TABLE summerfarm_tech.`app_mkt_order_coupon_preferential_mi` (
  `month` STRING COMMENT '月份',
  `city_id` BIGINT COMMENT '运营服务区ID',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细）',
  `coupon_group` STRING COMMENT '优惠券分组',
  `issued_cust_cnt` BIGINT COMMENT '发卷人数（发放+领取）',
  `receive_cust_cnt` BIGINT COMMENT '领取人数',
  `used_cust_cnt` BIGINT COMMENT '使用人数',
  `used_coupon_amt` DECIMAL(38,18) COMMENT '使用优惠券金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '使用优惠券订单应付金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '使用优惠券订单实付金额',
  `issued_used_cust_cnt` BIGINT COMMENT '当月领取且使用人数',
  `issued_used_coupon_amt` DECIMAL(38,18) COMMENT '当月领取且使用优惠券金额',
  `issued_origin_total_amt` DECIMAL(38,18) COMMENT '当月领取且使用优惠券订单应付金额',
  `issued_real_total_amt` DECIMAL(38,18) COMMENT '当月领取且使用优惠券订单实付金额'
)
COMMENT '优惠券使用明细月表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"city_id":{"0":"1001","1":"1001","2":"1001","3":"1001","4":"1001"},"city_name":{"0":"杭州","1":"杭州","2":"杭州","3":"杭州","4":"杭州"},"large_area_id":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"large_area_name":{"0":"杭州大区","1":"杭州大区","2":"杭州大区","3":"杭州大区","4":"杭州大区"},"cust_team":{"0":"Mars大客户","1":"Mars大客户","2":"平台客户","3":"平台客户","4":"平台客户"},"cust_type":{"0":"咖啡","1":"甜品冰淇淋","2":"其他","3":"其他","4":"其他"},"life_cycle_detail":{"0":"S1","1":"S2","2":"A1","3":"A1","4":"A1"},"coupon_group":{"0":"行业活动券","1":"售后补偿券","2":"区域拉新券","3":"平台活动券","4":"行业活动券"},"issued_cust_cnt":{"0":"1","1":"1","2":"0","3":"2","4":"2"},"receive_cust_cnt":{"0":"1","1":"0","2":"0","3":"1","4":"2"},"used_cust_cnt":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"used_coupon_amt":{"0":"25","1":"19.54","2":"10","3":"30","4":"10"},"origin_total_amt":{"0":"1747","1":"502","2":"148","3":"330","4":"594.1"},"real_total_amt":{"0":"1577","1":"482.46","2":"138","3":"300","4":"544.1"},"issued_used_cust_cnt":{"0":"1","1":"1","2":"0","3":"1","4":"1"},"issued_used_coupon_amt":{"0":"25","1":"19.54","2":"0","3":"30","4":"10"},"issued_origin_total_amt":{"0":"1747","1":"502","2":"0","3":"330","4":"594.1"},"issued_real_total_amt":{"0":"1577","1":"482.46","2":"0","3":"300","4":"544.1"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   city_id |   large_area_id |   issued_cust_cnt |   receive_cust_cnt |   used_cust_cnt |   issued_used_cust_cnt |
|:------|----------:|----------------:|------------------:|-------------------:|----------------:|-----------------------:|
| count |     10000 |      10000      |       10000       |        10000       |     10000       |            10000       |
| mean  |     19238 |         29.1869 |           4.4074  |            0.9971  |         1.5874  |                1.4062  |
| std   |     12257 |         32.4805 |           9.28277 |            4.08345 |         4.00617 |                3.86144 |
| min   |      1001 |          1      |           0       |            0       |         0       |                0       |
| 25%   |      9400 |          1      |           1       |            0       |         0       |                0       |
| 50%   |     17574 |         14      |           2       |            0       |         1       |                1       |
| 75%   |     25624 |         72      |           4       |            1       |         2       |                1       |
| max   |     44122 |         84      |         275       |          179       |       151       |              151       |