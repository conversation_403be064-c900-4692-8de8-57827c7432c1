# app_stc_kpi_di
* comment: 仓配KPI汇总表
* last_data_modified_time: 2025-09-18 03:47:34

# schema:
CREATE TABLE summerfarm_tech.`app_stc_kpi_di` (
  `date` STRING COMMENT '日期',
  `check_sku_cnt` BIGINT COMMENT '抽检数量',
  `in_bound_sku_cnt` BIGINT COMMENT '入库数量',
  `check_rate` DECIMAL(38,18) COMMENT '抽检比例',
  `back_order_cnt` BIGINT COMMENT '退货总单数',
  `finish_order_cnt` BIGINT COMMENT '已完成单数',
  `back_finish_rate` DECIMAL(38,18) COMMENT '退货完结率',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责',
  `sale_amt` DECIMAL(38,18) COMMENT '销售金额金额',
  `error_sku_cnt` BIGINT COMMENT '错误件数',
  `error_sku_cnt_wah` BIGINT COMMENT '错误件数_仓配责',
  `error_cust_cnt` BIGINT COMMENT '错误客户数',
  `error_cust_cnt_wah` BIGINT COMMENT '错误客户数_仓配责',
  `cust_cnt` BIGINT COMMENT '活跃客户数',
  `sku_cnt` BIGINT COMMENT '配送件数',
  `total_point_cnt` BIGINT COMMENT '总点位数',
  `point_cnt` BIGINT COMMENT '点位数（不含喜茶）',
  `no_in_time_point_cnt` BIGINT COMMENT '不及时点位数（不含喜茶）',
  `delay_time_point_cnt_2` BIGINT COMMENT '前置2小时不及时点位数（不含喜茶）',
  `out_time` DECIMAL(38,18) COMMENT '配送超时时间（不含喜茶）',
  `delay_time_2` DECIMAL(38,18) COMMENT '前置2小时配送超时时间（不含喜茶）',
  `path_cnt` BIGINT COMMENT '线路数（不含喜茶）',
  `delay_path_cnt` BIGINT COMMENT '出库不及时线路数（不含喜茶）',
  `out_distance_point_cnt` BIGINT COMMENT '超距离点位数（含喜茶）',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额',
  `after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责',
  `after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责',
  `after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责',
  `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责',
  `after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付GMV',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本'
)
COMMENT '仓配KPI汇总表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917"},"check_sku_cnt":{"0":"0"},"in_bound_sku_cnt":{"0":"0"},"check_rate":{"0":"0"},"back_order_cnt":{"0":"45"},"finish_order_cnt":{"0":"3"},"back_finish_rate":{"0":"0.06666666666666667"},"damage_amt":{"0":"5983.13"},"damage_amt_wah":{"0":"11.7"},"sale_amt":{"0":"4018306.59"},"error_sku_cnt":{"0":"22"},"error_sku_cnt_wah":{"0":"22"},"error_cust_cnt":{"0":"14"},"error_cust_cnt_wah":{"0":"14"},"cust_cnt":{"0":"7605"},"sku_cnt":{"0":"39879"},"total_point_cnt":{"0":"8488"},"point_cnt":{"0":"8488"},"no_in_time_point_cnt":{"0":"0"},"delay_time_point_cnt_2":{"0":"550"},"out_time":{"0":"0"},"delay_time_2":{"0":"365.3616666666667"},"path_cnt":{"0":"441"},"delay_path_cnt":{"0":"5"},"out_distance_point_cnt":{"0":"23"},"after_sale_amt":{"0":"20985.61"},"after_sale_amt_wah":{"0":"2147.64"},"after_sale_amt_pur":{"0":"2557.59"},"after_sale_amt_che":{"0":"14181.56"},"after_sale_amt_pur_che":{"0":"16739.15"},"after_sale_amt_oth":{"0":"2098.82"},"delivery_total_amt":{"0":"5120758.069999999999929234"},"coupon_amt":{"0":"45337.68396825396818312"},"origin_total_amt":{"0":"3967014.19"},"real_total_amt":{"0":"3827155.941904761904761989"},"storage_amt":{"0":"82452.731271303317116568"},"arterial_roads_amt":{"0":"69854.363153924175749072"},"deliver_amt":{"0":"258544.825569522321148225"},"self_picked_amt":{"0":"0"},"other_amt":{"0":"10534.089157949077962481"},"allocation_amt":{"0":"13955.847358090349593986"},"cost_amt":{"0":"3420755.59"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   check_sku_cnt |   in_bound_sku_cnt |   back_order_cnt |   finish_order_cnt |   error_sku_cnt |   error_sku_cnt_wah |   error_cust_cnt |   error_cust_cnt_wah |   cust_cnt |   sku_cnt |   total_point_cnt |   point_cnt |   no_in_time_point_cnt |   delay_time_point_cnt_2 |   path_cnt |   delay_path_cnt |   out_distance_point_cnt |
|:------|----------------:|-------------------:|-----------------:|-------------------:|----------------:|--------------------:|-----------------:|---------------------:|-----------:|----------:|------------------:|------------:|-----------------------:|-------------------------:|-----------:|-----------------:|-------------------------:|
| count |               1 |                  1 |                1 |                  1 |               1 |                   1 |                1 |                    1 |          1 |         1 |                 1 |           1 |                      1 |                        1 |          1 |                1 |                        1 |
| mean  |               0 |                  0 |               45 |                  3 |              22 |                  22 |               14 |                   14 |       7605 |     39879 |              8488 |        8488 |                      0 |                      550 |        441 |                5 |                       23 |
| std   |             nan |                nan |              nan |                nan |             nan |                 nan |              nan |                  nan |        nan |       nan |               nan |         nan |                    nan |                      nan |        nan |              nan |                      nan |
| min   |               0 |                  0 |               45 |                  3 |              22 |                  22 |               14 |                   14 |       7605 |     39879 |              8488 |        8488 |                      0 |                      550 |        441 |                5 |                       23 |
| 25%   |               0 |                  0 |               45 |                  3 |              22 |                  22 |               14 |                   14 |       7605 |     39879 |              8488 |        8488 |                      0 |                      550 |        441 |                5 |                       23 |
| 50%   |               0 |                  0 |               45 |                  3 |              22 |                  22 |               14 |                   14 |       7605 |     39879 |              8488 |        8488 |                      0 |                      550 |        441 |                5 |                       23 |
| 75%   |               0 |                  0 |               45 |                  3 |              22 |                  22 |               14 |                   14 |       7605 |     39879 |              8488 |        8488 |                      0 |                      550 |        441 |                5 |                       23 |
| max   |               0 |                  0 |               45 |                  3 |              22 |                  22 |               14 |                   14 |       7605 |     39879 |              8488 |        8488 |                      0 |                      550 |        441 |                5 |                       23 |