# app_log_purchase_main_di
* comment: 采购助手首页流量
* last_data_modified_time: 2025-09-18 02:31:24

# schema:
CREATE TABLE summerfarm_tech.`app_log_purchase_main_di` (
  `date` STRING COMMENT '日期',
  `type` STRING COMMENT '实验分组：V3 、V4、对照组',
  `uv` BIGINT COMMENT '首页曝光客户数',
  `experimental_uv` BIGINT COMMENT '实验客户首次曝光uv',
  `no_first_uv` BIGINT COMMENT '非首次曝光uv',
  `classification_uv` BIGINT COMMENT '分类模块曝光uv',
  `search_uv` BIGINT COMMENT '搜索模块曝光uv',
  `purchase_assistant_uv` BIGINT COMMENT '采购助手曝光uv',
  `purchase_assistant_first_uv` BIGINT COMMENT '采购助手首次曝光uv',
  `purchase_assistant_nofirst_uv` BIGINT COMMENT '采购助手非首次曝光uv',
  `pv` BIGINT COMMENT '首页曝光次数',
  `experimental_pv` BIGINT COMMENT '实验客户首次曝光pv',
  `no_main_pv` BIGINT COMMENT '非首次曝光pv',
  `classification_pv` BIGINT COMMENT '分类模块曝光pv',
  `search_pv` BIGINT COMMENT '搜索模块曝光pv',
  `purchase_assistant_pv` BIGINT COMMENT '采购助手曝光pv',
  `purchase_assistant_first_pv` BIGINT COMMENT '采购助手首次曝光pv',
  `purchase_assistant_nofirst_pv` BIGINT COMMENT '非采购助手首次曝光pv'
)
COMMENT '采购助手首页流量'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917"},"type":{"0":"V3","1":"V4","2":"对照组"},"uv":{"0":"1","1":"16139","2":"38"},"experimental_uv":{"0":"0","1":"651","2":"0"},"no_first_uv":{"0":"1","1":"15488","2":"38"},"classification_uv":{"0":"0","1":"6625","2":"1"},"search_uv":{"0":"1","1":"7741","2":"1"},"purchase_assistant_uv":{"0":"0","1":"1613","2":"0"},"purchase_assistant_first_uv":{"0":"0","1":"568","2":"0"},"purchase_assistant_nofirst_uv":{"0":"0","1":"1045","2":"0"},"pv":{"0":"1","1":"54737","2":"662"},"experimental_pv":{"0":"0","1":"2756","2":"0"},"no_main_pv":{"0":"1","1":"51981","2":"662"},"classification_pv":{"0":"0","1":"83276","2":"17"},"search_pv":{"0":"2","1":"29198","2":"1"},"purchase_assistant_pv":{"0":"0","1":"2669","2":"0"},"purchase_assistant_first_pv":{"0":"0","1":"815","2":"0"},"purchase_assistant_nofirst_pv":{"0":"0","1":"1854","2":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |       uv |   experimental_uv |   no_first_uv |   classification_uv |   search_uv |   purchase_assistant_uv |   purchase_assistant_first_uv |   purchase_assistant_nofirst_uv |      pv |   experimental_pv |   no_main_pv |   classification_pv |   search_pv |   purchase_assistant_pv |   purchase_assistant_first_pv |   purchase_assistant_nofirst_pv |
|:------|---------:|------------------:|--------------:|--------------------:|------------:|------------------------:|------------------------------:|--------------------------------:|--------:|------------------:|-------------:|--------------------:|------------:|------------------------:|------------------------------:|--------------------------------:|
| count |     3    |             3     |          3    |                3    |        3    |                   3     |                         3     |                           3     |     3   |             3     |          3   |                 3   |        3    |                   3     |                         3     |                            3    |
| mean  |  5392.67 |           217     |       5175.67 |             2208.67 |     2581    |                 537.667 |                       189.333 |                         348.333 | 18466.7 |           918.667 |      17548   |             27764.3 |     9733.67 |                 889.667 |                       271.667 |                          618    |
| std   |  9306.62 |           375.855 |       8930.76 |             3824.66 |     4468.69 |                 931.266 |                       327.935 |                         603.331 | 31412.8 |          1591.18  |      29821.7 |             48074.5 |    16856.6  |                1540.95  |                       470.54  |                         1070.41 |
| min   |     1    |             0     |          1    |                0    |        1    |                   0     |                         0     |                           0     |     1   |             0     |          1   |                 0   |        1    |                   0     |                         0     |                            0    |
| 25%   |    19.5  |             0     |         19.5  |                0.5  |        1    |                   0     |                         0     |                           0     |   331.5 |             0     |        331.5 |                 8.5 |        1.5  |                   0     |                         0     |                            0    |
| 50%   |    38    |             0     |         38    |                1    |        1    |                   0     |                         0     |                           0     |   662   |             0     |        662   |                17   |        2    |                   0     |                         0     |                            0    |
| 75%   |  8088.5  |           325.5   |       7763    |             3313    |     3871    |                 806.5   |                       284     |                         522.5   | 27699.5 |          1378     |      26321.5 |             41646.5 |    14600    |                1334.5   |                       407.5   |                          927    |
| max   | 16139    |           651     |      15488    |             6625    |     7741    |                1613     |                       568     |                        1045     | 54737   |          2756     |      51981   |             83276   |    29198    |                2669     |                       815     |                         1854    |