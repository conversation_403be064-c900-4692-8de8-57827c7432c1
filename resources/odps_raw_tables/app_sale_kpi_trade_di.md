# app_sale_kpi_trade_di
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:47:31

# schema:
CREATE TABLE summerfarm_tech.`app_sale_kpi_trade_di` (
  `date` STRING COMMENT '日期',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `lose_cust_cnt` BIGINT COMMENT '交易流失客户数（90天内活跃用户近60天未下单客户数）',
  `lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917"},"order_origin_total_amt":{"0":"3754297.07"},"order_real_total_amt":{"0":"3618659.35"},"order_cust_cnt":{"0":"7122"},"order_cust_arpu":{"0":"527.140841055883178882"},"order_cnt":{"0":"8437"},"lose_cust_cnt":{"0":"7811"},"lose_cust_ratio":{"0":"0.1131799345060422"},"delivery_origin_total_amt":{"0":"3922935.6"},"delivery_real_total_amt":{"0":"3783091.111904761904761989"},"delivery_cust_cnt":{"0":"7374"},"delivery_origin_profit":{"0":"536826.17"},"delivery_real_profit":{"0":"396981.681904761904761989"},"delivery_after_profit":{"0":"-9962.353162521669634797"},"delivery_days_avg":{"0":"1"},"delivery_point_cnt":{"0":"7658"},"delivery_amt":{"0":"406944.035067283574396786"},"new_delivery_origin_total_amt":{"0":"140372.47"},"new_delivery_real_total_amt":{"0":"135659.660000000000000002"},"new_delivery_cust_cnt":{"0":"349"},"new_delivery_real_profit":{"0":"13378.620000000000000002"},"old_delivery_origin_total_amt":{"0":"3782563.13"},"old_delivery_real_total_amt":{"0":"3647431.451904761904761987"},"old_delivery_cust_cnt":{"0":"7025"},"old_delivery_real_profit":{"0":"383603.061904761904761987"},"order_sku_cnt":{"0":"1059"},"order_sku_weight":{"0":"213667.0799999999"},"delivery_sku_cnt":{"0":"1048"},"delivery_sku_weight":{"0":"220680.53"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   order_cust_cnt |   order_cnt |   lose_cust_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|-----------------:|------------:|----------------:|--------------------:|---------------------:|------------------------:|------------------------:|----------------:|-------------------:|
| count |                1 |           1 |               1 |                   1 |                    1 |                       1 |                       1 |               1 |                  1 |
| mean  |             7122 |        8437 |            7811 |                7374 |                 7658 |                     349 |                    7025 |            1059 |               1048 |
| std   |              nan |         nan |             nan |                 nan |                  nan |                     nan |                     nan |             nan |                nan |
| min   |             7122 |        8437 |            7811 |                7374 |                 7658 |                     349 |                    7025 |            1059 |               1048 |
| 25%   |             7122 |        8437 |            7811 |                7374 |                 7658 |                     349 |                    7025 |            1059 |               1048 |
| 50%   |             7122 |        8437 |            7811 |                7374 |                 7658 |                     349 |                    7025 |            1059 |               1048 |
| 75%   |             7122 |        8437 |            7811 |                7374 |                 7658 |                     349 |                    7025 |            1059 |               1048 |
| max   |             7122 |        8437 |            7811 |                7374 |                 7658 |                     349 |                    7025 |            1059 |               1048 |