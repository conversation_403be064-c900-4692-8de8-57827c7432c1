# app_xianmu_search_front_category_prediction_df
* comment: 根据用户的点击历史记录做的query-前端类目的预测结果，来源于app_xianmu_search_category_prediction_df
* last_data_modified_time: 2025-09-18 05:02:24

# schema:
CREATE TABLE summerfarm_tech.`app_xianmu_search_front_category_prediction_df` (
  `query` STRING COMMENT '搜索词',
  `predicated_front_category_name` STRING COMMENT '预测的前端类目名称'
)
COMMENT '根据用户的点击历史记录做的query-前端类目的预测结果，来源于app_xianmu_search_category_prediction_df'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc
LIFECYCLE 180

# head data:
{"query":{"0":"芒果","1":"牛奶","2":"蓝莓","3":"草莓","4":"柠檬","5":"西瓜","6":"奶油","7":"牛油果","8":"安佳","9":"鸡蛋"},"predicated_front_category_name":{"0":"芒果","1":"常温奶,牛奶","2":"莓果","3":"莓果","4":"柠檬丨金桔","5":"应季水果,瓜类","6":"稀奶油,稀奶油丨奶盖","7":"牛油果","8":"稀奶油,稀奶油丨奶盖","9":"蔬菜,蛋类"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|        | query   | predicated_front_category_name   |             ds |
|:-------|:--------|:---------------------------------|---------------:|
| count  | 2672    | 2672                             | 2672           |
| unique | 2672    | 127                              |    1           |
| top    | 芒果    | 稀奶油,稀奶油丨奶盖              |    2.02509e+07 |
| freq   | 1       | 203                              | 2672           |