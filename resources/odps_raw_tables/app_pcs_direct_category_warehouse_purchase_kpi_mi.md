# app_pcs_direct_category_warehouse_purchase_kpi_mi
* comment: 直采kpi
* last_data_modified_time: 2025-09-18 03:26:52

# schema:
CREATE TABLE summerfarm_tech.`app_pcs_direct_category_warehouse_purchase_kpi_mi` (
  `month` STRING COMMENT '日期',
  `warehouse_no` BIGINT COMMENT '库存仓号',
  `warehouse_name` STRING COMMENT '库存仓',
  `category4` STRING COMMENT '四级类目',
  `direct_purchase_amt` DECIMAL(38,18) COMMENT '直采金额',
  `purchases_amt` DECIMAL(38,18) COMMENT '采购金额（非直采）',
  `cost_flow_amt` DECIMAL(38,18) COMMENT '成本浮动金额',
  `direct_delivery_origin_amt` DECIMAL(38,18) COMMENT '直采履约应付金额',
  `direct_delivery_real_amt` DECIMAL(38,18) COMMENT '直采履约实付金额',
  `direct_delivery_market_amt` DECIMAL(38,18) COMMENT '直采营销费用',
  `direct_delivery_cost_amt` DECIMAL(38,18) COMMENT '直采成本费用',
  `direct_init_amt` DECIMAL(38,18) COMMENT '直采期末库存金额',
  `direct_after_sale_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责售后金额',
  `direct_damage_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责货损金额'
)
COMMENT '直采kpi'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"warehouse_no":{"0":"125","1":"38","2":"150","3":"10","4":"10"},"warehouse_name":{"0":"南京总仓","1":"福州总仓","2":"嘉兴水果批发总仓","3":"嘉兴总仓","4":"嘉兴总仓"},"category4":{"0":"柠檬","1":"金桔","2":"其他葡萄","3":"枣","4":"橘子"},"direct_purchase_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchases_amt":{"0":"474372","1":"5570","2":"0","3":"10108.5","4":"2925.39"},"cost_flow_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"direct_delivery_origin_amt":{"0":"36099.43","1":"0","2":"0","3":"0","4":"0"},"direct_delivery_real_amt":{"0":"35402.579999999999999998","1":"0","2":"0","3":"0","4":"0"},"direct_delivery_market_amt":{"0":"696.850000000000000002","1":"0","2":"0","3":"0","4":"0"},"direct_delivery_cost_amt":{"0":"28689.97","1":"0","2":"0","3":"0","4":"0"},"direct_init_amt":{"0":"16919.62","1":"0","2":"0","3":"0","4":"0"},"direct_after_sale_pcs_amt":{"0":"368.3","1":"0","2":"0","3":"0","4":"0"},"direct_damage_pcs_amt":{"0":"337.41","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |
|:------|---------------:|
| count |       394      |
| mean  |        81.5203 |
| std   |        53.3675 |
| min   |         1      |
| 25%   |        38      |
| 50%   |        64      |
| 75%   |       150      |
| max   |       155      |