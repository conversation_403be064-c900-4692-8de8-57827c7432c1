# app_log_channel_sku_di
* comment: 流量分渠道sku转化
* last_data_modified_time: 2025-09-18 02:32:50

# schema:
CREATE TABLE summerfarm_tech.`app_log_channel_sku_di` (
  `date` STRING COMMENT '日期',
  `fsku_id` STRING COMMENT 'sku编号',
  `category1` STRING COMMENT '一级类目',
  `category2` STRING COMMENT '二级类目',
  `category3` STRING COMMENT '三级类目',
  `category4` STRING COMMENT '四级类目',
  `sku_type` STRING COMMENT '自营，代仓，代售',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品描述',
  `model` STRING COMMENT '渠道',
  `uv` BIGINT COMMENT '曝光uv',
  `pv` BIGINT COMMENT '曝光pv',
  `click_uv` BIGINT COMMENT '点击uv',
  `click_pv` BIGINT COMMENT '点击pv',
  `addbug_uv` BIGINT COMMENT '加买uv',
  `addbug_pv` BIGINT COMMENT '加买pv',
  `cust_cnt` BIGINT COMMENT '交易人数'
)
COMMENT '流量分渠道sku转化'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"fsku_id":{"0":"None","1":"1001218743510","2":"1001662557788","3":"1003043034688","4":"1003065575401"},"category1":{"0":"None","1":"其他","2":"其他","3":"乳制品","4":"乳制品"},"category2":{"0":"None","1":"成品原料","2":"成品原料","3":"乳制品","4":"乳制品"},"category3":{"0":"None","1":"方便速食","2":"方便速食","3":"黄油","4":"黄油"},"category4":{"0":"None","1":"速食面","2":"速食面","3":"乳酸黄油","4":"乳酸黄油"},"sku_type":{"0":"None","1":"2","2":"2","3":"2","4":"2"},"spu_name":{"0":"None","1":"水妈妈春卷皮（22cm）","2":"水妈妈春卷皮（16cm）","3":"菲仕兰乳酸发酵黄油","4":"柏札莱阿尔卑黄油"},"sku_disc":{"0":"None","1":"500g*32包","2":"200g*50包","3":"25KG*1箱","4":"400g*10盒\/400g"},"model":{"0":"banner","1":"banner","2":"banner","3":"banner","4":"banner"},"uv":{"0":"75","1":"2","2":"2","3":"10","4":"17"},"pv":{"0":"133","1":"2","2":"2","3":"19","4":"22"},"click_uv":{"0":"175","1":"0","2":"0","3":"0","4":"0"},"click_pv":{"0":"1065","1":"0","2":"0","3":"0","4":"0"},"addbug_uv":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"addbug_pv":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"cust_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |         uv |        pv |   click_uv |   click_pv |   addbug_uv |   addbug_pv |    cust_cnt |
|:------|-----------:|----------:|-----------:|-----------:|------------:|------------:|------------:|
| count | 10000      | 10000     | 10000      | 10000      | 10000       | 10000       | 10000       |
| mean  |    63.7101 |    81.558 |     2.2775 |     4.5577 |     0.6066  |     1.0477  |     0.3801  |
| std   |   170.974  |   253.555 |    10.6324 |    25.7578 |     5.54899 |     9.59194 |     3.97895 |
| min   |     1      |     1     |     0      |     0      |     0       |     0       |     0       |
| 25%   |     7      |     7     |     0      |     0      |     0       |     0       |     0       |
| 50%   |    18      |    20     |     0      |     0      |     0       |     0       |     0       |
| 75%   |    52      |    61     |     1      |     1      |     0       |     0       |     0       |
| max   |  3728      |  6222     |   297      |  1065      |   214       |   341       |   169       |