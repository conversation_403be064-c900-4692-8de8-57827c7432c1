# app_finance_store_mi
* comment: 财务口径收入数据表
* last_data_modified_time: 2025-09-18 02:17:51

# schema:
CREATE TABLE summerfarm_tech.`app_finance_store_mi` (
  `month` STRING COMMENT '月份',
  `service_area` STRING COMMENT '大区',
  `warehouse_no` BIGINT COMMENT '库存仓id',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `category1` STRING COMMENT '商品一级类目:鲜果/乳制品/其他',
  `store_amt` DECIMAL(38,18) COMMENT '含税在库金额',
  `store_amt_notax` DECIMAL(38,18) COMMENT '不含税在库金额',
  `road_amt` DECIMAL(38,18) COMMENT '含税在途金额',
  `road_amt_notax` DECIMAL(38,18) COMMENT '不含税在途金额'
)
COMMENT '财务口径收入数据表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"service_area":{"0":"云南","1":"云南","2":"云南","3":"华东","4":"华东"},"warehouse_no":{"0":60,"1":60,"2":60,"3":2,"4":2},"warehouse_name":{"0":"昆明总仓","1":"昆明总仓","2":"昆明总仓","3":"上海总仓","4":"上海总仓"},"category1":{"0":"乳制品","1":"其他","2":"鲜果","3":"乳制品","4":"其他"},"store_amt":{"0":729979.27,"1":115172.56,"2":6853.51,"3":3104528.7200000002,"4":11827.69},"store_amt_notax":{"0":647171.03,"1":103248.15,"2":6287.63,"3":2747370.54,"4":10475.11},"road_amt":{"0":0.0,"1":0.0,"2":0.0,"3":0.0,"4":0.0},"road_amt_notax":{"0":0.0,"1":0.0,"2":0.0,"3":0.0,"4":0.0},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |
|:------|---------------:|
| count |        52      |
| mean  |        73.4231 |
| std   |        43.5716 |
| min   |         2      |
| 25%   |        45.5    |
| 50%   |        63.5    |
| 75%   |       116.25   |
| max   |       155      |