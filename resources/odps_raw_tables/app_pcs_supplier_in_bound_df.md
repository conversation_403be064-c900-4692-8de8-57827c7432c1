# app_pcs_supplier_in_bound_df
* comment: 竞价供应商历史入库情况
* last_data_modified_time: 2025-09-18 02:01:35

# schema:
CREATE TABLE summerfarm_tech.`app_pcs_supplier_in_bound_df` (
  `date` STRING COMMENT '日期',
  `warehouse_no` BIGINT COMMENT '库存仓号',
  `warehouse_name` STRING COMMENT '库存仓',
  `sku_id` STRING COMMENT 'sku编号',
  `spu_name` STRING COMMENT '商品名称',
  `spu_disc` STRING COMMENT '商品描述',
  `supplier_id` BIGINT COMMENT '供应商编号',
  `supplier` STRING COMMENT '供应商',
  `price_start_time` DATETIME COMMENT '价格生效时间',
  `price_end_time` DATETIME COMMENT '价格失效时间',
  `in_bound_sku_cnt` BIGINT COMMENT '90天内采购入库量',
  `in_bound_amt` DECIMAL(38,18) COMMENT '90天内采购入库金额',
  `in_bound_avg_amt` DECIMAL(38,18) COMMENT '90天内采购入库平均金额',
  `quoted_price` DECIMAL(38,18) COMMENT '报价'
)
COMMENT '竞价供应商历史入库情况'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"date":{"0":"20240428","1":"20240428","2":"20240428","3":"20240428","4":"20240428"},"warehouse_no":{"0":"69","1":"69","2":"48","3":"48","4":"10"},"warehouse_name":{"0":"东莞总仓","1":"东莞总仓","2":"长沙总仓","3":"长沙总仓","4":"嘉兴总仓"},"sku_id":{"0":"104661","1":"104661","2":"14042","3":"14042","4":"16776325223"},"spu_name":{"0":"紫香1号百香果","1":"紫香1号百香果","2":"国产红心火龙果","3":"国产红心火龙果","4":"伦晚橙"},"spu_disc":{"0":"5斤*1包\/二级\/单果约40g+","1":"5斤*1包\/二级\/单果约40g+","2":"30斤*1箱\/普通\/单果约300-400g","3":"30斤*1箱\/普通\/单果约300-400g","4":"毛重32-34斤\/一级\/果径75-80mm"},"supplier_id":{"0":"1574","1":"1603","2":"1504","3":"1619","4":"1880"},"supplier":{"0":"陕果集市（成都）特产有限公司","1":"石云花","2":"湖南省源顺农业科技有限公司","3":"淄博荣福商贸有限公司","4":"嘉兴星徽农业科技有限公司"},"price_start_time":{"0":"2024-04-22 00:00:00","1":"2024-04-22 00:00:00","2":"2024-04-22 00:00:00","3":"2024-04-22 00:00:00","4":"2024-04-22 00:00:00"},"price_end_time":{"0":"2024-04-28 23:59:59","1":"2024-04-28 23:59:59","2":"2024-04-28 23:59:59","3":"2024-04-28 23:59:59","4":"2024-04-28 23:59:59"},"in_bound_sku_cnt":{"0":"1648","1":"3077","2":"21","3":"1428","4":"0"},"in_bound_amt":{"0":"35415.6","1":"73197.9","2":"3810","3":"211790","4":"0"},"in_bound_avg_amt":{"0":"21.490048543689320388","1":"23.788722781930451739","2":"181.428571428571428571","3":"148.312324929971988796","4":"0"},"quoted_price":{"0":"27","1":"26","2":"170","3":"160","4":"500"},"ds":{"0":"20240428","1":"20240428","2":"20240428","3":"20240428","4":"20240428"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   warehouse_no |   supplier_id | price_start_time    | price_end_time                |   in_bound_sku_cnt |
|:------|---------------:|--------------:|:--------------------|:------------------------------|-------------------:|
| count |       216      |       216     | 216                 | 216                           |             216    |
| mean  |        38.625  |      1312.9   | 2024-04-22 00:00:00 | 2024-04-28 23:59:59.000000256 |            1211.06 |
| min   |        10      |       147     | 2024-04-22 00:00:00 | 2024-04-28 23:59:59           |               0    |
| 25%   |        10      |       799     | 2024-04-22 00:00:00 | 2024-04-28 23:59:59           |               0    |
| 50%   |        36      |      1409     | 2024-04-22 00:00:00 | 2024-04-28 23:59:59           |             229    |
| 75%   |        69      |      1833     | 2024-04-22 00:00:00 | 2024-04-28 23:59:59           |            1602.5  |
| max   |        69      |      2284     | 2024-04-22 00:00:00 | 2024-04-28 23:59:59           |           16886    |
| std   |        25.0867 |       601.955 | nan                 | nan                           |            2245.39 |