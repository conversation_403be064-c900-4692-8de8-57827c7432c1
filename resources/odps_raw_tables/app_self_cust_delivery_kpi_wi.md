# app_self_cust_delivery_kpi_wi
* comment: 履约口径kpi指标日汇总(自营)
* last_data_modified_time: 2025-09-18 03:41:31

# schema:
CREATE TABLE summerfarm_tech.`app_self_cust_delivery_kpi_wi` (
  `year` STRING COMMENT '年',
  `week_of_year` STRING COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `day_point_cnt` BIGINT COMMENT '日均点位',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
)
COMMENT '履约口径kpi指标日汇总(自营)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025"},"week_of_year":{"0":"38","1":"38","2":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921"},"cust_team":{"0":"Mars大客户","1":"集团大客户（茶百道）","2":"平台客户"},"origin_total_amt":{"0":"127935.29","1":"13767.14","2":"11022871.06"},"real_total_amt":{"0":"127812.64","1":"13333.89","2":"10667885.0736813186813188"},"cost_amt":{"0":"100680.87","1":"11166.53","2":"9493034.41"},"timing_origin_total_amt":{"0":"0","1":"0","2":"682201"},"timing_real_total_amt":{"0":"0","1":"0","2":"636589.003681318681318679"},"cust_cnt":{"0":"334","1":"48","2":"18210"},"order_cnt":{"0":"499","1":"70","2":"26416"},"point_cnt":{"0":"336","1":"50","2":"19224"},"day_point_cnt":{"0":"476","1":"54","2":"22919"},"sku_cnt":{"0":"2050","1":"266","2":"103789"},"delivery_amt":{"0":"610","1":"50","2":"22190.93"},"after_sale_received_amt":{"0":"3383.81","1":"204","2":"26431.42"},"storage_amt":{"0":"2275.201487945394867343","1":"448.097571305584027131","2":"231351.211393970894607253"},"arterial_roads_amt":{"0":"596.384649714366716389","1":"658.139916998321432755","2":"187208.098477233289328977"},"deliver_amt":{"0":"7255.461069050982500144","1":"1467.194977139767058945","2":"702303.834861723732572616"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"other_amt":{"0":"188.910777636755888087","1":"84.04193183525053816","2":"28999.726326030485286037"},"allocation_amt":{"0":"955.964735270563667299","1":"48.078502439832193121","2":"41834.179435045455892521"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   point_cnt |   day_point_cnt |   sku_cnt |
|:------|-----------:|------------:|------------:|----------------:|----------:|
| count |       3    |         3   |        3    |            3    |       3   |
| mean  |    6197.33 |      8995   |     6536.67 |         7816.33 |   35368.3 |
| std   |   10404.3  |     15088.6 |    10988.5  |        13081    |   59260.7 |
| min   |      48    |        70   |       50    |           54    |     266   |
| 25%   |     191    |       284.5 |      193    |          265    |    1158   |
| 50%   |     334    |       499   |      336    |          476    |    2050   |
| 75%   |    9272    |     13457.5 |     9780    |        11697.5  |   52919.5 |
| max   |   18210    |     26416   |    19224    |        22919    |  103789   |