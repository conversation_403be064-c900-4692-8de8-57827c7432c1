# app_kpi_cust_sku_trade_mi
* comment: 交易（订单）kpi金额汇总
* last_data_modified_time: 2025-09-18 02:23:23

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_cust_sku_trade_mi` (
  `month` STRING COMMENT '月份',
  `cust_class` STRING COMMENT '客户类型:大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
  `sku_type` STRING COMMENT '商品类型:自营、代仓',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `delivery_amt` DECIMAL(38,18) COMMENT '应付运费金额',
  `out_times_amt` DECIMAL(38,18) COMMENT '超时加单费'
)
COMMENT '交易（订单）kpi金额汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"cust_class":{"0":"大客户（茶百道）","1":"大客户（茶百道）","2":"大客户（非茶百道）","3":"大客户（非茶百道）","4":"批发"},"sku_type":{"0":"代仓","1":"自营","2":"代仓","3":"自营","4":"自营"},"origin_total_amt":{"0":"7486.1","1":"81208.82","2":"16945782.89","3":"6056927.11","4":"12308874.67"},"real_total_amt":{"0":"7347.29","1":"78353.16","2":"16945166.54","3":"6024719.22","4":"12308874.67"},"cust_cnt":{"0":"18","1":"141","2":"457","3":"3459","4":"21"},"order_cnt":{"0":"45","1":"386","2":"2953","3":"16226","4":"210"},"delivery_amt":{"0":"220","1":"300","2":"789.44","3":"29804.56","4":"1360"},"out_times_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |
|:------|-----------:|------------:|
| count |       9    |         9   |
| mean  |    6322.56 |     19392.6 |
| std   |   13377.1  |     42445.9 |
| min   |      12    |        12   |
| 25%   |      21    |       210   |
| 50%   |     141    |       386   |
| 75%   |    3459    |     16226   |
| max   |   40330    |    130131   |