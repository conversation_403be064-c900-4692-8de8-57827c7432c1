# app_saas_brand_sku_trade_wi
* comment: saas品牌品类结构数据表
* last_data_modified_time: 2025-09-18 02:23:50

# schema:
CREATE TABLE summerfarm_tech.`app_saas_brand_sku_trade_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `brand_alias` STRING COMMENT '品牌名称',
  `title` STRING COMMENT '商品标题',
  `specification` STRING COMMENT '商品规格',
  `category1` STRING COMMENT '后台一级类目',
  `sku_type` STRING COMMENT '商品类型：商城下单、鲜沐自营、代仓、代售、客户自营',
  `total_gmv` DECIMAL(38,18) COMMENT '总交易GMV',
  `sku_cnt` BIGINT COMMENT '商品销量'
)
COMMENT 'saas品牌品类结构数据表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"brand_alias":{"0":"肯豆","1":"肯豆","2":"新加坡斯味洛鲜奶茶 ","3":"新加坡斯味洛鲜奶茶 ","4":"曾小白"},"title":{"0":"6寸单层打包盒","1":"6寸双层打包盒","2":"500ml易拉罐","3":"纸杯（款式随机发）","4":"专版   6寸单层"},"specification":{"0":"0_1箱*50套","1":"0_1箱*50套","2":"0_200个*1箱","3":"0_500只*1箱","4":"0_1箱*50套"},"category1":{"0":"无","1":"无","2":"无","3":"无","4":"无"},"sku_type":{"0":"客户自营","1":"客户自营","2":"客户自营","3":"客户自营","4":"客户自营"},"total_gmv":{"0":"3328","1":"3510","2":"131404","3":"5057","4":"810"},"sku_cnt":{"0":"32","1":"27","2":"874","3":"19","4":"9"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   sku_cnt |
|:------|---------------:|----------:|
| count |           1284 | 1284      |
| mean  |             38 |   15.271  |
| std   |              0 |   55.6828 |
| min   |             38 |    1      |
| 25%   |             38 |    1      |
| 50%   |             38 |    3      |
| 75%   |             38 |   10      |
| max   |             38 | 1000      |