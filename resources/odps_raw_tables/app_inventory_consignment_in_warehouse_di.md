# app_inventory_consignment_in_warehouse_di
* comment: 代销入仓库存表
* last_data_modified_time: 2025-09-18 05:14:25

# schema:
CREATE TABLE summerfarm_tech.`app_inventory_consignment_in_warehouse_di` (
  `supplier_id` BIGINT COMMENT '供应商id',
  `supplier_name` STRING COMMENT '供应商名称',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '仓库名称',
  `batch_no` STRING COMMENT '批次',
  `sku` STRING COMMENT 'sku编码',
  `sku_desc` STRING COMMENT '商品描述',
  `spu_name` STRING COMMENT 'spu名称',
  `stock_quantity` BIGINT COMMENT '库存数量',
  `stock_amount` DECIMAL(38,18) COMMENT '库存金额',
  `stock_type` BIGINT COMMENT '库存类型',
  `production_date` DATETIME COMMENT '生产日期',
  `quality_date` DATETIME COMMENT '保质期',
  `date_flag` STRING COMMENT '日期标识',
  `custom_sku` STRING COMMENT '供应商自有sku',
  `sub_type` BIGINT COMMENT '，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓'
)
COMMENT '代销入仓库存表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"supplier_id":{"0":"2173.0","1":"2122.0","2":"2122.0","3":"2122.0","4":"2122.0"},"supplier_name":{"0":"清远市彬利食品贸易有限公司","1":"深圳速拓供应链有限公司","2":"深圳速拓供应链有限公司","3":"深圳速拓供应链有限公司","4":"深圳速拓供应链有限公司"},"warehouse_no":{"0":"117","1":"24","2":"64","3":"38","4":"155"},"warehouse_name":{"0":"东莞冷冻总仓","1":"华西总仓","2":"青岛总仓","3":"福州总仓","4":"武汉总仓"},"batch_no":{"0":"202506041564622118","1":"0120250912176531","2":"0120250916453112","3":"20250722106144170","4":"20250730106952111"},"sku":{"0":"1003312273255","1":"1007801326018","2":"1007801326018","3":"1007801326057","4":"1007801326057"},"sku_desc":{"0":"25KG*1箱","1":"1L*1盒(有盖)","2":"1L*1盒(有盖)","3":"1L*12盒(有盖)","4":"1L*12盒(有盖)"},"spu_name":{"0":"苏力士发酵黄油","1":"速拓厚椰乳","2":"速拓厚椰乳","3":"速拓厚椰乳","4":"速拓厚椰乳"},"stock_quantity":{"0":"12","1":"9","2":"12","3":"15","4":"25"},"stock_amount":{"0":"10800","1":"117","2":"156","3":"2137.5","4":"3562.5"},"stock_type":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"production_date":{"0":"2025-05-12","1":"2025-07-15","2":"2025-07-15","3":"2025-07-14","4":"2025-07-14"},"quality_date":{"0":"2027-05-12","1":"2026-04-15","2":"2026-04-15","3":"2026-04-14","4":"2026-04-14"},"date_flag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"custom_sku":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"sub_type":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   supplier_id |   warehouse_no |   stock_quantity |   stock_type | production_date            | quality_date                  |   sub_type |
|:------|--------------:|---------------:|-----------------:|-------------:|:---------------------------|:------------------------------|-----------:|
| count |      9995     |     10000      |       10000      | 10000        | 10000                      | 10000                         |      10000 |
| mean  |      2071.77  |        77.7243 |           9.9418 |     0.7048   | 2024-10-16 18:02:26.880000 | 2026-01-05 18:05:45.600000256 |          2 |
| min   |        38     |         1      |        -410      |     0        | 2021-02-22 00:00:00        | 2023-06-05 00:00:00           |          2 |
| 25%   |      1906     |        29      |          -7      |     0        | 2024-04-12 18:00:00        | 2025-04-11 18:00:00           |          2 |
| 50%   |      2046     |        69      |           2      |     1        | 2024-12-21 00:00:00        | 2026-01-12 00:00:00           |          2 |
| 75%   |      2208     |       121      |          12      |     1        | 2025-05-28 00:00:00        | 2026-07-10 00:00:00           |          2 |
| max   |      3425     |       155      |        2175      |     1        | 2025-09-22 00:00:00        | 2052-12-15 00:00:00           |          2 |
| std   |       599.953 |        48.6548 |          71.5142 |     0.456155 | nan                        | nan                           |          0 |