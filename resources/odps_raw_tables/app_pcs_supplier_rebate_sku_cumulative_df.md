# app_pcs_supplier_rebate_sku_cumulative_df
* comment: 供应商返利目标累计月维度表
* last_data_modified_time: 2025-09-18 03:44:02

# schema:
CREATE TABLE summerfarm_tech.`app_pcs_supplier_rebate_sku_cumulative_df` (
  `year` BIGINT COMMENT '年度',
  `period` STRING COMMENT '周期',
  `sku_id` STRING COMMENT 'SKU编码',
  `commodity_name_and_specification` STRING COMMENT '商品名称&规格',
  `supplier_id` BIGINT COMMENT '供货商ID',
  `supplier_name` STRING COMMENT '供货商名称',
  `warehouse_name` STRING COMMENT '仓库名称',
  `commodity_temperature_zone` STRING COMMENT '商品温区',
  `purchase_order_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购下单金额',
  `purchase_order_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购下单金额（不含税）',
  `purchase_order_quantity_in_period` BIGINT COMMENT '周期内采购下单件数',
  `purchase_order_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购下单重量',
  `purchase_order_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购下单体积',
  `purchase_inbound_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购入库金额',
  `purchase_inbound_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购入库金额（不含税）',
  `purchase_inbound_quantity_in_period` BIGINT COMMENT '周期内采购入库件数',
  `purchase_inbound_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购入库重量',
  `purchase_inbound_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购入库体积',
  `purchase_reservation_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购预约金额',
  `purchase_reservation_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购预约金额（不含税）',
  `purchase_reservation_quantity_in_period` BIGINT COMMENT '周期内采购预约件数',
  `purchase_reservation_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购预约重量',
  `purchase_reservation_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购预约体积'
)
COMMENT '供应商返利目标累计月维度表'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"year":{"0":"2023","1":"2023","2":"2023","3":"2023","4":"2023"},"period":{"0":"年","1":"年","2":"年","3":"年","4":"年"},"sku_id":{"0":"106306","1":"106306","2":"106306","3":"1233884862","4":"1256524478"},"commodity_name_and_specification":{"0":"安佳奶油奶酪20kg&20KG*1箱\/新款","1":"安佳奶油奶酪20kg&20KG*1箱\/新款","2":"安佳奶油奶酪20kg&20KG*1箱\/新款","3":"安佳碎条状马苏里拉干酪&12KG*1箱\/红标(产品适用于堂食，胶质感较强，拉丝效果较好)","4":"安佳再制切达奶酪_橙色&84片*1包"},"supplier_id":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"supplier_name":{"0":"厦门建发生活资材有限责任公司","1":"厦门建发生活资材有限责任公司","2":"厦门建发生活资材有限责任公司","3":"厦门建发生活资材有限责任公司","4":"厦门建发生活资材有限责任公司"},"warehouse_name":{"0":"东莞冷冻总仓,东莞总仓,华西总仓,南宁总仓,昆明总仓,武汉总仓,重庆总仓,长沙总仓","1":"南京总仓,苏州总仓","2":"嘉兴总仓,嘉兴海盐总仓","3":"东莞冷冻总仓,东莞总仓,华西总仓,南宁总仓,昆明总仓,武汉总仓,重庆总仓,长沙总仓","4":"南京总仓,苏州总仓"},"commodity_temperature_zone":{"0":"冷藏","1":"冷藏","2":"冷藏","3":"冷冻","4":"冷藏"},"purchase_order_amount_in_period":{"0":"118500","1":"39500","2":"39500","3":"137280","4":"7056"},"purchase_order_amount_excluding_tax":{"0":"104867.25663716814159292","1":"34955.752212389380530973","2":"34955.752212389380530973","3":"121486.725663716814159292","4":"6244.247787610619469027"},"purchase_order_quantity_in_period":{"0":"159","1":"53","2":"53","3":"260","4":"120"},"purchase_order_weight_in_period":{"0":"3180","1":"1060","2":"1060","3":"3380","4":"123.6"},"purchase_order_volume_in_period":{"0":"3.60612","1":"1.20204","2":"1.20204","3":"7.293","4":"0.1152"},"purchase_inbound_amount_in_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_inbound_amount_excluding_tax":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_inbound_quantity_in_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_inbound_weight_in_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_inbound_volume_in_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_reservation_amount_in_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_reservation_amount_excluding_tax":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_reservation_quantity_in_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_reservation_weight_in_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchase_reservation_volume_in_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |        year |   purchase_order_quantity_in_period |   purchase_inbound_quantity_in_period |   purchase_reservation_quantity_in_period |
|:------|------------:|------------------------------------:|--------------------------------------:|------------------------------------------:|
| count | 7417        |                            7417     |                              7417     |                                  7417     |
| mean  | 2024.53     |                             600.468 |                               568.721 |                                   568.113 |
| std   |    0.510886 |                            3903.46  |                              3907.81  |                                  3904.51  |
| min   | 2023        |                               0     |                                 0     |                                     0     |
| 25%   | 2024        |                              34     |                                22     |                                    21     |
| 50%   | 2025        |                             102     |                                90     |                                    90     |
| 75%   | 2025        |                             320     |                               300     |                                   300     |
| max   | 2025        |                          206550     |                            208950     |                                208950     |