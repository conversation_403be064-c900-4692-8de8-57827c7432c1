# app_kpi_operate_large_category_delivery_di
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:40:20

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_large_category_delivery_di` (
  `date` STRING COMMENT '月份',
  `large_area_name` STRING COMMENT '运营服务大区',
  `category` STRING COMMENT '鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"large_area_name":{"0":"上海大区","1":"上海大区","2":"上海大区","3":"广州大区","4":"成都大区"},"category":{"0":"乳制品","1":"其他","2":"鲜果","3":"鲜果","4":"乳制品"},"origin_total_amt":{"0":"153722.65","1":"60985.39","2":"95661.62","3":"195527.76","4":"69140.34"},"real_total_amt":{"0":"149586.420000000000000001","1":"58612.599999999999999999","2":"93064.280000000000000005","3":"184962.900000000000000005","4":"67513.21"},"marketing_amt":{"0":"4136.229999999999999999","1":"2372.790000000000000001","2":"2597.339999999999999995","3":"10564.859999999999999995","4":"1627.13"},"cost_amt":{"0":"142686.28","1":"55185.39","2":"69317.89","3":"141950.68","4":"64521.81"},"origin_gross":{"0":"11036.37","1":"5800","2":"26343.73","3":"53577.08","4":"4618.53"},"real_gross":{"0":"6900.140000000000000001","1":"3427.209999999999999999","2":"23746.390000000000000005","3":"43012.220000000000000005","4":"2991.4"},"origin_gross_margin":{"0":"0.071794039460027524","1":"0.095104745579228074","2":"0.275384527253458597","3":"0.274012651707358587","4":"0.066799353315300445"},"real_gross_margin":{"0":"0.046128117779675454","1":"0.058472239757321805","2":"0.255161163875119434","3":"0.232545121210794165","4":"0.044308365725759448"},"cust_cnt":{"0":"225","1":"212","2":"452","3":"842","4":"97"},"point_cnt":{"0":"232","1":"228","2":"485","3":"863","4":"100"},"origin_pre_cust_price":{"0":"683.211777777777777778","1":"287.666933962264150943","2":"211.640752212389380531","3":"232.218242280285035629","4":"712.787010309278350515"},"real_pre_cust_price":{"0":"664.828533333333333333","1":"276.474528301886792453","2":"205.894424778761061947","3":"219.670902612826603325","4":"696.012474226804123711"},"timing_origin_amt":{"0":"13476","1":"11474","2":"0","3":"0","4":"5877"},"timing_real_amt":{"0":"12642","1":"10544.5","2":"0","3":"0","4":"5474"},"consign_origin_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_marketing_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_origin_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_real_gross":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"consign_cust_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"storage_amt":{"0":"3214.051933300802544091","1":"3085.01216322405796904","2":"4078.082050619162232603","3":"6220.569874001224020879","4":"545.669997798653897126"},"arterial_roads_amt":{"0":"588.400544944295714316","1":"623.118239792840652499","2":"509.840727939063476378","3":"3645.636489885305116222","4":"0"},"self_picked_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"allocation_amt":{"0":"55.030740431647989991","1":"52.8513060974514195","2":"70.30434771605902059","3":"1087.369746920327314984","4":"531.246996692566017508"},"other_amt":{"0":"8.704491906319678729","1":"10.057191589520474867","2":"19.001726349858523615","3":"0","4":"0"},"deliver_amt":{"0":"6664.614816595720409356","1":"6455.69492304604241844","2":"8462.398744775305611876","3":"13441.438049755883019167","4":"2214.852577468555021884"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |     43     |      43     |                 43 |
| mean  |    238.767 |     245.419 |                  0 |
| std   |    252.995 |     260.342 |                  0 |
| min   |      1     |       1     |                  0 |
| 25%   |     66.5   |      67     |                  0 |
| 50%   |    180     |     193     |                  0 |
| 75%   |    320     |     325     |                  0 |
| max   |   1091     |    1127     |                  0 |