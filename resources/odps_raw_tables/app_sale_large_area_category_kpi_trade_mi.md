# app_sale_large_area_category_kpi_trade_mi
* comment: 销售KPI指标汇总
* last_data_modified_time: 2025-09-18 03:48:10

# schema:
CREATE TABLE summerfarm_tech.`app_sale_large_area_category_kpi_trade_mi` (
  `month` STRING COMMENT '月份',
  `large_area_name` STRING COMMENT '运营服务大区',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"large_area_name":{"0":"杭州大区","1":"贵阳大区","2":"南宁大区","3":"苏州大区","4":"昆明大区"},"category":{"0":"其他","1":"乳制品","2":"其他","3":"鲜果","4":"鲜果"},"order_origin_total_amt":{"0":"2148724.91","1":"392012.1","2":"240196.8","3":"1026496.05","4":"11060.6"},"order_real_total_amt":{"0":"2066209.74","1":"382882.02","2":"226593.83","3":"996774.36","4":"10871.84"},"order_cust_cnt":{"0":"4166","1":"208","2":"459","3":"2231","4":"43"},"order_cust_arpu":{"0":"515.776502640422467595","1":"1884.673557692307692308","2":"523.30457516339869281","3":"460.105804571940833707","4":"257.223255813953488372"},"order_cnt":{"0":"8979","1":"403","2":"867","3":"6604","4":"77"},"delivery_origin_total_amt":{"0":"2017130.99","1":"352249.1","2":"224350.6","3":"1022173.42","4":"12035.4"},"delivery_real_total_amt":{"0":"1944520.859999999999999996","1":"344408.32","2":"211766.540000000000000003","3":"994188.11000000000000003","4":"11846.64"},"delivery_cust_cnt":{"0":"4174","1":"206","2":"448","3":"2227","4":"46"},"delivery_origin_profit":{"0":"387060.28","1":"21420.03","2":"34775.81","3":"280294.09","4":"1985.68"},"delivery_real_profit":{"0":"314450.149999999999999996","1":"13579.25","2":"22191.750000000000000003","3":"252308.78000000000000003","4":"1796.92"},"delivery_after_profit":{"0":"24950.881758989665505461","1":"-47.339693275509011535","2":"-11980.116818226998245713","3":"70812.558717900777369693","4":"647.40991249004882044"},"delivery_days_avg":{"0":"1.980114997604216","1":"1.742718446601942","2":"1.649553571428571","3":"2.617422541535698","4":"1.673913043478261"},"delivery_point_cnt":{"0":"8469","1":"368","2":"757","3":"6018","4":"79"},"delivery_amt":{"0":"289499.268241010334494535","1":"13626.589693275509011535","2":"34171.866818226998245716","3":"181496.221282099222630337","4":"1149.51008750995117956"},"new_delivery_origin_total_amt":{"0":"38984.43","1":"6070.1","2":"4700.3","3":"14854.96","4":"775"},"new_delivery_real_total_amt":{"0":"36981.870000000000000002","1":"5795.36","2":"4395.3","3":"14230.829999999999999999","4":"760"},"new_delivery_cust_cnt":{"0":"141","1":"10","2":"20","3":"72","4":"3"},"new_delivery_real_profit":{"0":"5297.700000000000000002","1":"253.4","2":"552.61","3":"3407.619999999999999999","4":"71"},"old_delivery_origin_total_amt":{"0":"1978146.56","1":"346179","2":"219650.3","3":"1007318.46","4":"11260.4"},"old_delivery_real_total_amt":{"0":"1907538.989999999999999994","1":"338612.96","2":"207371.240000000000000003","3":"979957.280000000000000031","4":"11086.64"},"old_delivery_cust_cnt":{"0":"4033","1":"196","2":"428","3":"2155","4":"43"},"old_delivery_real_profit":{"0":"309152.449999999999999994","1":"13325.85","2":"21639.140000000000000003","3":"248901.160000000000000031","4":"1725.92"},"order_sku_cnt":{"0":"497","1":"51","2":"210","3":"180","4":"19"},"order_sku_weight":{"0":"173977.48","1":"10938.91000000001","2":"24001.56999999999","3":"82521.65999999996","4":"908.0099999999998"},"delivery_sku_cnt":{"0":"505","1":"50","2":"206","3":"178","4":"20"},"delivery_sku_weight":{"0":"163728.66","1":"10019.36","2":"22352.68","3":"82012.40999999997","4":"993.87"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   order_cust_cnt |   order_cnt |   delivery_cust_cnt |   delivery_point_cnt |   new_delivery_cust_cnt |   old_delivery_cust_cnt |   order_sku_cnt |   delivery_sku_cnt |
|:------|-----------------:|------------:|--------------------:|---------------------:|------------------------:|------------------------:|----------------:|-------------------:|
| count |            47    |       47    |               47    |                47    |                 47      |                   47    |          47     |             47     |
| mean  |          1615.04 |     4192.34 |             1618.28 |              3913.66 |                 66.5319 |                 1551.74 |         164.085 |            164.383 |
| std   |          1627.59 |     4806.9  |             1630.16 |              4407.61 |                 71.2945 |                 1563    |         130.301 |            130.979 |
| min   |             1    |        1    |                1    |                 1    |                  0      |                    0    |           1     |              1     |
| 25%   |           384.5  |      812    |              376    |               729.5  |                 20.5    |                  355    |          88     |             88.5   |
| 50%   |          1182    |     2665    |             1177    |              2448    |                 43      |                 1110    |         119     |            118     |
| 75%   |          1979    |     4881.5  |             1970.5  |              4523.5  |                 78.5    |                 1917    |         241     |            241     |
| max   |          6146    |    22339    |             6082    |             20255    |                330      |                 5752    |         497     |            505     |