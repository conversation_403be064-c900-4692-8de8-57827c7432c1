# app_self_cust_delivery_kpi_di
* comment: 履约口径kpi指标日汇总(自营)
* last_data_modified_time: 2025-09-18 03:39:41

# schema:
CREATE TABLE summerfarm_tech.`app_self_cust_delivery_kpi_di` (
  `date` STRING COMMENT '日期',
  `cust_team` STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `day_point_cnt` BIGINT COMMENT '日均点位',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
)
COMMENT '履约口径kpi指标日汇总(自营)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917"},"cust_team":{"0":"Mars大客户","1":"平台客户","2":"集团大客户（茶百道）"},"origin_total_amt":{"0":"39273.62","1":"3922935.6","2":"4804.97"},"real_total_amt":{"0":"39264.62","1":"3783091.111904761904761989","2":"4800.21"},"cost_amt":{"0":"30651.11","1":"3386109.43","2":"3995.05"},"timing_origin_total_amt":{"0":"0","1":"253286","2":"0"},"timing_real_total_amt":{"0":"0","1":"237279.261904761904761907","2":"0"},"cust_cnt":{"0":"161","1":"7374","2":"19"},"order_cnt":{"0":"166","1":"8849","2":"20"},"point_cnt":{"0":"161","1":"7658","2":"19"},"day_point_cnt":{"0":"161","1":"7660","2":"19"},"sku_cnt":{"0":"645","1":"35833","2":"90"},"delivery_amt":{"0":"360","1":"7095.68","2":"10"},"after_sale_received_amt":{"0":"1221.91","1":"8397.05","2":"0"},"storage_amt":{"0":"674.51249537744306411","1":"81608.155426826121546312","2":"170.063349099777729953"},"arterial_roads_amt":{"0":"198.115916735163987854","1":"69441.98401381341612705","2":"214.263223375606998754"},"deliver_amt":{"0":"2158.429388313755756745","1":"255893.895626644141170828","2":"492.50055456449950499"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"other_amt":{"0":"53.361527413482699479","1":"10439.017586164567554887","2":"41.710044371031081157"},"allocation_amt":{"0":"269.968304341798435111","1":"13667.907418832362218447","2":"17.971634916195522509"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   point_cnt |   day_point_cnt |   sku_cnt |
|:------|-----------:|------------:|------------:|----------------:|----------:|
| count |       3    |        3    |        3    |            3    |       3   |
| mean  |    2518    |     3011.67 |     2612.67 |         2613.33 |   12189.3 |
| std   |    4206.02 |     5055.81 |     4369.96 |         4371.12 |   20477.9 |
| min   |      19    |       20    |       19    |           19    |      90   |
| 25%   |      90    |       93    |       90    |           90    |     367.5 |
| 50%   |     161    |      166    |      161    |          161    |     645   |
| 75%   |    3767.5  |     4507.5  |     3909.5  |         3910.5  |   18239   |
| max   |    7374    |     8849    |     7658    |         7660    |   35833   |