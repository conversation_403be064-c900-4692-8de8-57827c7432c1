# app_saas_brand_log_di
* comment: saas品牌埋点数据表
* last_data_modified_time: 2025-09-18 02:16:59

# schema:
CREATE TABLE summerfarm_tech.`app_saas_brand_log_di` (
  `date` STRING COMMENT '日期',
  `brand_alias` STRING COMMENT '品牌名称',
  `login_pv` BIGINT COMMENT '总登录PV',
  `login_uv` BIGINT COMMENT '总登录UV',
  `mall_pv` BIGINT COMMENT '商场模块PV',
  `mall_uv` BIGINT COMMENT '商场模块UV',
  `customer_pv` BIGINT COMMENT '门店模块PV',
  `customer_uv` BIGINT COMMENT '门店模块UV',
  `goods_pv` BIGINT COMMENT '商品模块PV',
  `goods_uv` BIGINT COMMENT '商品模块UV',
  `order_pv` BIGINT COMMENT '订单模块PV',
  `order_uv` BIGINT COMMENT '订单模块UV',
  `purchasing_pv` BIGINT COMMENT '采购模块PV',
  `purchasing_uv` BIGINT COMMENT '采购模块UV',
  `store_pv` BIGINT COMMENT '仓库模块PV',
  `store_uv` BIGINT COMMENT '仓库模块UV',
  `data_pv` BIGINT COMMENT '数据模块PV',
  `data_uv` BIGINT COMMENT '数据模块UV',
  `finance_pv` BIGINT COMMENT '财务模块PV',
  `finance_uv` BIGINT COMMENT '财务模块UV'
)
COMMENT 'saas品牌埋点数据表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"brand_alias":{"0":"Keke可可同学订货商城","1":"VQ","2":"世界冠军面包（会成好物）","3":"东桃蛋糕（南客甄选）","4":"乳果说茶饮"},"login_pv":{"0":"19","1":"27","2":"3","3":"1","4":"124"},"login_uv":{"0":"2","1":"1","2":"1","3":"1","4":"2"},"mall_pv":{"0":"1","1":"6","2":"0","3":"0","4":"0"},"mall_uv":{"0":"1","1":"1","2":"0","3":"0","4":"0"},"customer_pv":{"0":"3","1":"0","2":"0","3":"0","4":"29"},"customer_uv":{"0":"1","1":"0","2":"0","3":"0","4":"1"},"goods_pv":{"0":"3","1":"1","2":"0","3":"0","4":"10"},"goods_uv":{"0":"1","1":"1","2":"0","3":"0","4":"1"},"order_pv":{"0":"14","1":"5","2":"3","3":"1","4":"34"},"order_uv":{"0":"2","1":"1","2":"1","3":"1","4":"2"},"purchasing_pv":{"0":"1","1":"0","2":"0","3":"0","4":"20"},"purchasing_uv":{"0":"1","1":"0","2":"0","3":"0","4":"1"},"store_pv":{"0":"0","1":"5","2":"0","3":"0","4":"49"},"store_uv":{"0":"0","1":"1","2":"0","3":"0","4":"2"},"data_pv":{"0":"1","1":"3","2":"0","3":"0","4":"12"},"data_uv":{"0":"1","1":"1","2":"0","3":"0","4":"1"},"finance_pv":{"0":"0","1":"0","2":"0","3":"0","4":"2"},"finance_uv":{"0":"0","1":"0","2":"0","3":"0","4":"1"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   login_pv |   login_uv |   mall_pv |   mall_uv |   customer_pv |   customer_uv |   goods_pv |   goods_uv |   order_pv |   order_uv |   purchasing_pv |   purchasing_uv |   store_pv |   store_uv |   data_pv |   data_uv |   finance_pv |   finance_uv |
|:------|-----------:|-----------:|----------:|----------:|--------------:|--------------:|-----------:|-----------:|-----------:|-----------:|----------------:|----------------:|-----------:|-----------:|----------:|----------:|-------------:|-------------:|
| count |    29      |   29       |  29       | 29        |      29       |     29        |    29      |  29        |    29      |   29       |         29      |       29        |   29       |   29       |  29       |  29       |    29        |    29        |
| mean  |    59.9655 |    2.7931  |   1.24138 |  0.551724 |       7.27586 |      1        |    12.8621 |   0.965517 |    15.3448 |    2       |         10.6552 |        0.689655 |    9.68966 |    1       |   2.86207 |   0.62069 |     0.793103 |     0.241379 |
| std   |    97.9028 |    2.80789 |   2.26235 |  0.783135 |       9.14915 |      0.801784 |    35.8267 |   0.944259 |    23.1011 |    2.67261 |         24.2918 |        1.13715  |   19.707   |    1.48805 |   6.33428 |   1.17758 |     2.04205  |     0.510964 |
| min   |     0      |    1       |   0       |  0        |       0       |      0        |     0      |   0        |     0      |    0       |          0      |        0        |    0       |    0       |   0       |   0       |     0        |     0        |
| 25%   |    11      |    1       |   0       |  0        |       0       |      1        |     0      |   0        |     1      |    1       |          0      |        0        |    0       |    0       |   0       |   0       |     0        |     0        |
| 50%   |    19      |    2       |   0       |  0        |       5       |      1        |     3      |   1        |     5      |    1       |          0      |        0        |    1       |    1       |   0       |   0       |     0        |     0        |
| 75%   |    67      |    3       |   1       |  1        |       9       |      1        |    10      |   1        |    14      |    2       |         11      |        1        |    5       |    1       |   2       |   1       |     0        |     0        |
| max   |   475      |   11       |   7       |  3        |      34       |      3        |   193      |   4        |    72      |   11       |        119      |        5        |   84       |    7       |  31       |   6       |     9        |     2        |