# app_kpi_category_trade_mi
* comment: 交易口径kpi指标月汇总
* last_data_modified_time: 2025-09-18 02:53:04

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_category_trade_mi` (
  `month` STRING COMMENT '月份',
  `category` STRING COMMENT '品类:鲜果,乳制品,其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
)
COMMENT '交易口径kpi指标月汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509"},"category":{"0":"乳制品","1":"其他","2":"鲜果"},"origin_total_amt":{"0":"38973539.16","1":"11744050.13","2":"18017142.14"},"real_total_amt":{"0":"37844657.83","1":"11237337.39","2":"17482619.73"},"cust_cnt":{"0":"23683","1":"22418","2":"30585"},"cust_arpu":{"0":"1645.633541358780559895","1":"523.866987688464626639","2":"589.084261566127186529"},"order_cnt":{"0":"50412","1":"45993","2":"103962"},"order_avg":{"0":"773.100435610568912164","1":"255.344294349140086535","2":"173.305074354090917835"},"after_sale_noreceived_amt":{"0":"1658377.2","1":"401506.46","2":"360943.29"},"after_sale_rate":{"0":"0.042551362687175572","1":"0.034188074433909113","2":"0.020033326439639222"},"dire_origin_total_amt":{"0":"0","1":"0","2":"0"},"delivery_amt":{"0":"76730.2","1":"29512.29","2":"166661.44"},"timing_origin_total_amt":{"0":"4243923","1":"1134577","2":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |
|:------|-----------:|------------:|
| count |       3    |         3   |
| mean  |   25562    |     66789   |
| std   |    4395.79 |     32268.5 |
| min   |   22418    |     45993   |
| 25%   |   23050.5  |     48202.5 |
| 50%   |   23683    |     50412   |
| 75%   |   27134    |     77187   |
| max   |   30585    |    103962   |