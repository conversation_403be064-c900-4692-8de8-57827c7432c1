# app_kpi_area_honour_di
* comment: 履约kpi城配仓维度日表
* last_data_modified_time: 2025-09-18 03:43:17

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_area_honour_di` (
  `date` STRING COMMENT '日期',
  `area_no` BIGINT COMMENT '城配仓',
  `area_name` STRING COMMENT '城配仓名',
  `sku_type` STRING COMMENT '自营，代仓',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本价',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率',
  `refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `total_deliver_amt` DECIMAL(38,18) COMMENT '履约总费用'
)
COMMENT '履约kpi城配仓维度日表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"area_no":{"0":"1","1":"1","2":"2","3":"2","4":"8"},"area_name":{"0":"杭州仓","1":"杭州仓","2":"上海仓","3":"上海仓","4":"宁波仓"},"sku_type":{"0":"代售","1":"自营","2":"代售","3":"自营","4":"代仓"},"origin_total_amt":{"0":"12551.2","1":"87578.95","2":"15200.2","3":"102628.47","4":"13347.28"},"real_total_amt":{"0":"12406.36","1":"84469.620000000000000001","2":"14647.145238095238095239","3":"99426.700000000000000002","4":"13347.28"},"cost_amt":{"0":"5838.62","1":"71403.22","2":"9177.75","3":"85775.24","4":"128.31"},"preferential_amt":{"0":"144.84","1":"3109.329999999999999999","2":"553.054761904761904761","3":"3201.769999999999999998","4":"0"},"origin_pay_rate":{"0":"0.534815794505704634","1":"0.18469883459438598","2":"0.396208602518387916","3":"0.164215933453943141","4":"0.990386805401549979"},"real_pay_rate":{"0":"0.529384928375446142","1":"0.154687566961944425","2":"0.373410323253304195","3":"0.137301750938128289","4":"0.990386805401549979"},"refund_amt":{"0":"0","1":"164.68","2":"166.21","3":"506.41","4":"0"},"cust_cnt":{"0":"52","1":"240","2":"55","3":"214","4":"3"},"cust_unit_amt":{"0":"241.369230769230769231","1":"364.912291666666666667","2":"276.367272727272727273","3":"479.572289719626168224","4":"4449.093333333333333333"},"order_cnt":{"0":"57","1":"263","2":"59","3":"254","4":"4"},"point_cnt":{"0":"52","1":"244","2":"57","3":"224","4":"3"},"storage_amt":{"0":"205.273832780376886686","1":"1777.052508989024669141","2":"391.533930607822399116","3":"2697.592607971432540341","4":"38.04340517378125343"},"arterial_roads_amt":{"0":"170.933379400360201781","1":"1479.767716221161781019","2":"0","3":"0","4":"17.708811554827713945"},"deliver_amt":{"0":"595.282116875450042832","1":"5153.3484079366839819","2":"1083.895818648867843116","3":"7467.831315817801906752","4":"94.839718302038083807"},"total_deliver_amt":{"0":"971.489329056187131299","1":"8410.16863314687043206","2":"1475.429749256690242232","3":"10165.423923789234447093","4":"150.591935030647051182"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   area_no |   cust_cnt |   order_cnt |   point_cnt |
|:------|----------:|-----------:|------------:|------------:|
| count |  109      |   109      |    109      |    109      |
| mean  |   57.1651 |    78.2661 |     91.5596 |     80.7523 |
| std   |   41.5462 |    89.1843 |    108.386  |     93.2561 |
| min   |    1      |     0      |      0      |      0      |
| 25%   |   25      |    13      |     13      |     13      |
| 50%   |   39      |    41      |     44      |     42      |
| 75%   |   88      |   114      |    138      |    120      |
| max   |  154      |   382      |    464      |    394      |