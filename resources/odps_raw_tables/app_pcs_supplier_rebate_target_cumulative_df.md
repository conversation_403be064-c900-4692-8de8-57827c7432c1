# app_pcs_supplier_rebate_target_cumulative_df
* comment: 供应商返利目标累计月维度表
* last_data_modified_time: 2025-09-18 02:16:08

# schema:
CREATE TABLE summerfarm_tech.`app_pcs_supplier_rebate_target_cumulative_df` (
  `year` BIGINT COMMENT '年度',
  `period` STRING COMMENT '周期',
  `supplier_id` BIGINT COMMENT '供货商ID',
  `supplier_name` STRING COMMENT '供货商名称',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '仓库名称',
  `commodity_temperature_zone` STRING COMMENT '商品温区',
  `purchase_order_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购下单金额',
  `purchase_order_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购下单金额（不含税）',
  `purchase_order_quantity_in_period` BIGINT COMMENT '周期内采购下单件数',
  `purchase_order_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购下单重量',
  `purchase_order_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购下单体积',
  `purchase_inbound_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购入库金额',
  `purchase_inbound_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购入库金额（不含税）',
  `purchase_inbound_quantity_in_period` BIGINT COMMENT '周期内采购入库件数',
  `purchase_inbound_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购入库重量',
  `purchase_inbound_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购入库体积',
  `purchase_reservation_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购预约金额',
  `purchase_reservation_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购预约金额（不含税）',
  `purchase_reservation_quantity_in_period` BIGINT COMMENT '周期内采购预约件数',
  `purchase_reservation_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购预约重量',
  `purchase_reservation_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购预约体积'
)
COMMENT '供应商返利目标累计月维度表'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"year":{"0":"2024","1":"2024","2":"2024","3":"2024","4":"2024"},"period":{"0":"H1","1":"H1","2":"H1","3":"H1","4":"H1"},"supplier_id":{"0":"28","1":"28","2":"28","3":"28","4":"28"},"supplier_name":{"0":"西诺迪斯食品（上海）有限公司","1":"西诺迪斯食品（上海）有限公司","2":"西诺迪斯食品（上海）有限公司","3":"西诺迪斯食品（上海）有限公司","4":"西诺迪斯食品（上海）有限公司"},"warehouse_no":{"0":"2","1":"2","2":"10","3":"10","4":"24"},"warehouse_name":{"0":"上海总仓","1":"上海总仓","2":"嘉兴总仓","3":"嘉兴总仓","4":"华西总仓"},"commodity_temperature_zone":{"0":"冷藏","1":"常温","2":"冷藏","3":"常温","4":"冷藏"},"purchase_order_amount_in_period":{"0":"1383360.64","1":"54444.7","2":"10018932.45","3":"150250.05","4":"2839742.33"},"purchase_order_amount_excluding_tax":{"0":"1224212.955752212389380529","1":"48181.150442477876106193","2":"8866311.902654867256637167","3":"132964.646017699115044248","4":"2513046.309734513274336284"},"purchase_order_quantity_in_period":{"0":"3008","1":"196","2":"22363","3":"750","4":"5818"},"purchase_order_weight_in_period":{"0":"35003.9","1":"280","2":"262967.10000000001","3":"732","4":"68994.1"},"purchase_order_volume_in_period":{"0":"45.786635000000001","1":"0.9468480000000001","2":"356.62907","3":"2.5100640000000001","4":"87.758885"},"purchase_inbound_amount_in_period":{"0":"1354835.402933333333333262","1":"54444.699999999999999966","2":"10014029.674786324786324516","3":"150250.049999999999999968","4":"3099803.329999999999999972"},"purchase_inbound_amount_excluding_tax":{"0":"1198969.383126843657817041","1":"48181.150442477876106163","2":"8861973.163527721049844703","3":"132964.64601769911504422","4":"2743188.78761061946902652"},"purchase_inbound_quantity_in_period":{"0":"2944","1":"196","2":"22352","3":"750","4":"6318"},"purchase_inbound_weight_in_period":{"0":"34210.300000000001","1":"280","2":"262830.7","3":"732","4":"75014.099999999999"},"purchase_inbound_volume_in_period":{"0":"44.792075000000001","1":"0.9468480000000001","2":"356.45813000000005","3":"2.51006400000000012","4":"94.941635"},"purchase_reservation_amount_in_period":{"0":"1354835.402933333333333262","1":"54444.699999999999999966","2":"10014029.674786324786324516","3":"150250.049999999999999968","4":"3099803.329999999999999972"},"purchase_reservation_amount_excluding_tax":{"0":"1198969.383126843657817041","1":"48181.150442477876106163","2":"8861973.163527721049844703","3":"132964.64601769911504422","4":"2743188.78761061946902652"},"purchase_reservation_quantity_in_period":{"0":"2944","1":"196","2":"22352","3":"750","4":"6318"},"purchase_reservation_weight_in_period":{"0":"34210.300000000002","1":"280","2":"262830.69999999999","3":"732","4":"75014.099999999999"},"purchase_reservation_volume_in_period":{"0":"44.792075000000001","1":"0.9468480000000001","2":"356.45813000000006","3":"2.51006400000000012","4":"94.941635"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |        year |   supplier_id |   warehouse_no |   purchase_order_quantity_in_period |   purchase_inbound_quantity_in_period |   purchase_reservation_quantity_in_period |
|:------|------------:|--------------:|---------------:|------------------------------------:|--------------------------------------:|------------------------------------------:|
| count | 1485        |      1485     |      1485      |                             1485    |                               1485    |                                   1485    |
| mean  | 2024.41     |       703.766 |        50.4438 |                             2202.98 |                               2107.05 |                                   2089.26 |
| std   |    0.501871 |       762.233 |        44.1469 |                             5995.7  |                               5688.1  |                                   5635.31 |
| min   | 2023        |        28     |         1      |                                0    |                                  0    |                                      0    |
| 25%   | 2024        |        28     |        10      |                               88    |                                 76    |                                     76    |
| 50%   | 2024        |       617     |        38      |                              370    |                                357    |                                    347    |
| 75%   | 2025        |       969     |        69      |                             1507    |                               1427    |                                   1427    |
| max   | 2025        |      2912     |       155      |                            76964    |                              76029    |                                  76029    |