# app_platform_kpi_mi
* comment: 平台KPI
* last_data_modified_time: 2025-09-18 03:25:31

# schema:
CREATE TABLE summerfarm_tech.`app_platform_kpi_mi` (
  `month` STRING COMMENT '月份',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV',
  `timing_cust_cnt` BIGINT COMMENT '省心送履约客户数',
  `timing_ratio` DECIMAL(38,18) COMMENT '省心送合单率',
  `login_uv` BIGINT COMMENT '登陆UV',
  `order_uv` BIGINT COMMENT '交易客户数',
  `activity_uv` BIGINT COMMENT '特价点击UV',
  `activity_order_uv` BIGINT COMMENT '特价下单人数',
  `exchange_uv` BIGINT COMMENT '换购点击UV',
  `exchange_order_uv` BIGINT COMMENT '换购下单人数',
  `expand_uv` BIGINT COMMENT '拓展购买点击UV',
  `expand_order_uv` BIGINT COMMENT '拓展购买下单人数',
  `meeting_uv` BIGINT COMMENT '会场活动页点击UV',
  `meeting_order_uv` BIGINT COMMENT '会场活动页下单人数',
  `other_uv` BIGINT COMMENT '其他点击UV',
  `other_order_uv` BIGINT COMMENT '其他下单人数'
)
COMMENT '平台KPI'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509"},"origin_total_amt":{"0":"63935800.24"},"real_total_amt":{"0":"61932315.833454545454546037"},"cust_cnt":{"0":"44738"},"preferential_amt":{"0":"2003484.406545454545453963"},"timing_origin_amt":{"0":"3507383"},"timing_cust_cnt":{"0":"1851"},"timing_ratio":{"0":"0.8406266882766072"},"login_uv":{"0":"69719"},"order_uv":{"0":"45303"},"activity_uv":{"0":"4650"},"activity_order_uv":{"0":"0"},"exchange_uv":{"0":"812"},"exchange_order_uv":{"0":"0"},"expand_uv":{"0":"0"},"expand_order_uv":{"0":"0"},"meeting_uv":{"0":"0"},"meeting_order_uv":{"0":"0"},"other_uv":{"0":"67588"},"other_order_uv":{"0":"31368"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   timing_cust_cnt |   login_uv |   order_uv |   activity_uv |   activity_order_uv |   exchange_uv |   exchange_order_uv |   expand_uv |   expand_order_uv |   meeting_uv |   meeting_order_uv |   other_uv |   other_order_uv |
|:------|-----------:|------------------:|-----------:|-----------:|--------------:|--------------------:|--------------:|--------------------:|------------:|------------------:|-------------:|-------------------:|-----------:|-----------------:|
| count |          1 |                 1 |          1 |          1 |             1 |                   1 |             1 |                   1 |           1 |                 1 |            1 |                  1 |          1 |                1 |
| mean  |      44738 |              1851 |      69719 |      45303 |          4650 |                   0 |           812 |                   0 |           0 |                 0 |            0 |                  0 |      67588 |            31368 |
| std   |        nan |               nan |        nan |        nan |           nan |                 nan |           nan |                 nan |         nan |               nan |          nan |                nan |        nan |              nan |
| min   |      44738 |              1851 |      69719 |      45303 |          4650 |                   0 |           812 |                   0 |           0 |                 0 |            0 |                  0 |      67588 |            31368 |
| 25%   |      44738 |              1851 |      69719 |      45303 |          4650 |                   0 |           812 |                   0 |           0 |                 0 |            0 |                  0 |      67588 |            31368 |
| 50%   |      44738 |              1851 |      69719 |      45303 |          4650 |                   0 |           812 |                   0 |           0 |                 0 |            0 |                  0 |      67588 |            31368 |
| 75%   |      44738 |              1851 |      69719 |      45303 |          4650 |                   0 |           812 |                   0 |           0 |                 0 |            0 |                  0 |      67588 |            31368 |
| max   |      44738 |              1851 |      69719 |      45303 |          4650 |                   0 |           812 |                   0 |           0 |                 0 |            0 |                  0 |      67588 |            31368 |