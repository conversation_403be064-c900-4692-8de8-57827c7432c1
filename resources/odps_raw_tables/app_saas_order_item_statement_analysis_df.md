# app_saas_order_item_statement_analysis_df
* comment: saas订单对账表
* last_data_modified_time: 2025-09-18 02:18:27

# schema:
CREATE TABLE summerfarm_tech.`app_saas_order_item_statement_analysis_df` (
  `tenant_id` BIGINT COMMENT '租户id',
  `order_no` STRING COMMENT '订单编号',
  `order_source` BIGINT COMMENT '订单来源:0：门店下单; 1：openapi调用; 2:总部代下单',
  `store_id` BIGINT COMMENT '门店id',
  `store_no` STRING COMMENT '门店编号',
  `store_name` STRING COMMENT '门店名称',
  `store_type` BIGINT COMMENT '门店类型：0、直营店 1、加盟店 2、托管店',
  `store_group_id` BIGINT COMMENT '分组Id',
  `store_group_name` STRING COMMENT '门店分组名',
  `order_time` DATETIME COMMENT '下单时间',
  `pay_time` DATETIME COMMENT '支付时间',
  `delivery_time` DATETIME COMMENT '配送时间',
  `finished_time` DATETIME COMMENT '完成时间',
  `payable_price` DECIMAL(38,18) COMMENT '应付价格(应付总额)',
  `delivery_fee` DECIMAL(38,18) COMMENT '配送费',
  `total_price` DECIMAL(38,18) COMMENT '总金额（实收金额）',
  `pay_type` BIGINT COMMENT '支付方式 1、微信支付 2、账期 3、余额支付 4、支付宝支付 5、无需支付',
  `online_pay_channel` BIGINT COMMENT '支付渠道 0、微信 1、汇付',
  `payment_no` STRING COMMENT '支付单号（支付平台交易订单号）',
  `transaction_id` STRING COMMENT '交易流水号（银行流水号）',
  `pay_total_price` DECIMAL(38,18) COMMENT '支付金额',
  `warehouse_type` BIGINT COMMENT '配送仓类型 0,无仓1三方仓 2自营仓',
  `warehouse_no` STRING COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '配送仓名称',
  `warehouse_service_name` STRING COMMENT '库存仓服务商名称',
  `outbound_batch_no` STRING COMMENT '出库批次号',
  `supplier_id` BIGINT COMMENT '供应商Id',
  `supplier_name` STRING COMMENT '供应商名称',
  `item_id` BIGINT COMMENT '商品id',
  `item_title` STRING COMMENT '商品名称',
  `item_code` STRING COMMENT '自有编码',
  `goods_type` BIGINT COMMENT '商品类型 0无货商品 1报价货品 2自营货品',
  `specification_unit` STRING COMMENT '规格单位',
  `specification` STRING COMMENT '规格',
  `first_classification_name` STRING COMMENT '一级分类名称',
  `second_classification_name` STRING COMMENT '二级分类名称',
  `sku_id` BIGINT COMMENT '货品id',
  `sku_code` STRING COMMENT '货品sku编码',
  `goods_title` STRING COMMENT '货品名称',
  `first_category` STRING COMMENT '一级类目',
  `second_category` STRING COMMENT '二级类目',
  `third_category` STRING COMMENT '三级类目',
  `item_payable_price` DECIMAL(38,18) COMMENT '商品单价',
  `item_amount` BIGINT COMMENT '商品数量',
  `item_total_price` DECIMAL(38,18) COMMENT '商品总价',
  `supply_price` DECIMAL(38,18) COMMENT '供应价',
  `total_supply_price` DECIMAL(38,18) COMMENT '供应总价',
  `gross_margin_ratio` DECIMAL(38,18) COMMENT '毛利率，百分位',
  `item_refund_price` DECIMAL(38,18) COMMENT '商品退款总金额',
  `outbound_time` DATETIME COMMENT '出库时间',
  `outbound_amount` DECIMAL(38,18) COMMENT '出库数量'
)
COMMENT 'saas订单对账表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"order_no":{"0":"OR165415019089953","1":"OR165415858304755","2":"OR165416594579369","3":"OR165562977467628","4":"OR165563109752121"},"order_source":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"store_id":{"0":"3","1":"1","2":"5","3":"3","4":"1"},"store_no":{"0":"3","1":"1","2":"5","3":"3","4":"1"},"store_name":{"0":"悟空小店","1":"帆台杭州旗舰店","2":"朱永林店铺","3":"悟空小店","4":"帆台杭州旗舰店"},"store_type":{"0":"0","1":"0","2":"2","3":"0","4":"0"},"store_group_id":{"0":"41","1":"41","2":"3","3":"41","4":"41"},"store_group_name":{"0":"默认分组","1":"默认分组","2":"成都","3":"默认分组","4":"默认分组"},"order_time":{"0":"2022-06-02 14:09:50","1":"2022-06-02 16:29:43","2":"2022-06-02 18:32:25","3":"2022-06-19 17:09:34","4":"2022-06-19 17:31:37"},"pay_time":{"0":"2022-06-02 14:09:59","1":"2022-06-02 16:29:58","2":"2022-06-02 18:32:33","3":"2022-06-19 17:10:08","4":"2022-06-19 17:31:44"},"delivery_time":{"0":"2022-06-03 00:00:00","1":"2022-06-03 00:00:00","2":"NaT","3":"2022-06-20 00:00:00","4":"2022-06-20 00:00:00"},"finished_time":{"0":"2022-06-06 00:01:00","1":"2022-06-06 00:01:00","2":"2022-06-12 00:01:00","3":"2022-06-23 00:01:00","4":"2022-06-23 00:01:00"},"payable_price":{"0":"0.03","1":"26.01","2":"0.02","3":"23","4":"148"},"delivery_fee":{"0":"0.01","1":"0.01","2":"0.01","3":"0","4":"0"},"total_price":{"0":"0.03","1":"26.01","2":"0.02","3":"23","4":"148"},"pay_type":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"online_pay_channel":{"0":"0.0","1":"0.0","2":"0.0","3":"0.0","4":"0.0"},"payment_no":{"0":"P1532243074071605248","1":"P1532278272843730944","2":"P1532309154224820224","3":"P1538448898565533696","4":"P1538454447270035456"},"transaction_id":{"0":"4200001464202206027222011426","1":"4200001495202206026965397003","2":"4200001459202206023493323716","3":"4200001469202206199065117763","4":"4200001487202206193156148626"},"pay_total_price":{"0":"0.03","1":"26.01","2":"0.02","3":"23","4":"148"},"warehouse_type":{"0":"1","1":"1","2":"0","3":"1","4":"1"},"warehouse_no":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"warehouse_name":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"warehouse_service_name":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"outbound_batch_no":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"supplier_id":{"0":"nan","1":"nan","2":"nan","3":"nan","4":"nan"},"supplier_name":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"item_id":{"0":"3","1":"9","2":"1","3":"31","4":"21"},"item_title":{"0":"大草莓","1":"玉菇甜瓜","2":"大草莓","3":"佳沛绿心奇异果","4":"羊角蜜瓜"},"item_code":{"0":"None","1":"","2":"sagf5a1d4f51","3":"None","4":"None"},"goods_type":{"0":"1","1":"1","2":"0","3":"1","4":"1"},"specification_unit":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"specification":{"0":"1\/kg","1":"2个\/包","2":"1*200\/g","3":"6个*1盒\/标准规格\/一级","4":"毛重4-5斤\/普通\/标准规格"},"first_classification_name":{"0":"鲜果","1":"鲜果","2":"鲜果","3":"特供鲜果","4":"特供鲜果"},"second_classification_name":{"0":"热销水果","1":"热销水果","2":"热销水果","3":"奇异果","4":"瓜果"},"sku_id":{"0":"100002","1":"100008","2":"100000","3":"100030","4":"100020"},"sku_code":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"goods_title":{"0":"爱护植脂甜奶油","1":"玉菇甜瓜","2":"大草莓","3":"佳沛奇异果金果","4":"羊角蜜瓜"},"first_category":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"second_category":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"third_category":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"item_payable_price":{"0":"0.02","1":"26","2":"0.01","3":"23","4":"23"},"item_amount":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"item_total_price":{"0":"0.02","1":"26","2":"0.01","3":"23","4":"23"},"supply_price":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"total_supply_price":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"gross_margin_ratio":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"item_refund_price":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"outbound_time":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"outbound_amount":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   order_source |   store_id |   store_type |   store_group_id | order_time                    | pay_time                      | delivery_time                 | finished_time                 |     pay_type |   online_pay_channel |   warehouse_type |   supplier_id |    item_id |   goods_type |      sku_id |   item_amount |
|:------|------------:|---------------:|-----------:|-------------:|-----------------:|:------------------------------|:------------------------------|:------------------------------|:------------------------------|-------------:|---------------------:|-----------------:|--------------:|-----------:|-------------:|------------:|--------------:|
| count | 10000       |          10000 |  10000     | 10000        |        10000     | 10000                         | 10000                         | 9997                          | 10000                         | 10000        |                 8884 |    10000         |  8902         | 10000      | 10000        |  10000      |   10000       |
| mean  |     5.0266  |              0 |    130.835 |     0.8352   |          217.211 | 2022-09-07 20:27:30.971899904 | 2022-09-07 20:28:15.481800192 | 2022-09-08 08:11:45.811743744 | 2022-09-08 19:23:56.124599808 |     1.1116   |                    0 |        0.9997    |     1.00112   |   158.494  |     1.6983   | 100157      |       2.8464  |
| min   |     2       |              0 |      1     |     0        |            3     | 2022-06-02 14:09:50           | 2022-06-02 14:09:59           | 2022-06-03 00:00:00           | 2022-06-06 00:01:00           |     1        |                    0 |        0         |     1         |     1      |     0        | 100000      |       1       |
| 25%   |     4       |              0 |     41     |     1        |           43     | 2022-08-23 09:12:35.249999872 | 2022-08-23 09:12:45.500000    | 2022-08-24 00:00:00           | 2022-08-24 07:02:12           |     1        |                    0 |        1         |     1         |   107      |     1        | 100106      |       1       |
| 50%   |     6       |              0 |     91     |     1        |           45     | 2022-09-13 16:07:13           | 2022-09-13 16:07:20           | 2022-09-14 00:00:00           | 2022-09-14 11:31:21.500000    |     1        |                    0 |        1         |     1         |   162      |     2        | 100161      |       2       |
| 75%   |     6       |              0 |    213     |     1        |           45     | 2022-09-25 16:46:50           | 2022-09-25 16:46:50           | 2022-09-26 00:00:00           | 2022-09-26 13:46:29           |     1        |                    0 |        1         |     1         |   212      |     2        | 100211      |       3       |
| max   |     6       |              0 |    387     |     2        |         1086     | 2022-10-04 20:16:20           | 2022-10-04 20:16:26           | 2022-10-07 00:00:00           | 2022-10-07 11:06:36           |     2        |                    0 |        1         |     6         |   276      |     2        | 100275      |     250       |
| std   |     1.00488 |              0 |    102.509 |     0.371826 |          387.23  | nan                           | nan                           | nan                           | nan                           |     0.314889 |                    0 |        0.0173188 |     0.0749405 |    61.9079 |     0.459672 |     61.9079 |       4.31628 |