# app_kpi_operate_delivery_mi
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:43:21

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_delivery_mi` (
  `month` STRING COMMENT '月份',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509"},"origin_total_amt":{"0":"63935800.24"},"real_total_amt":{"0":"61932315.833454545454546037"},"marketing_amt":{"0":"2003484.406545454545453963"},"cost_amt":{"0":"55170941.19"},"origin_gross":{"0":"8764859.05"},"real_gross":{"0":"6761374.643454545454546037"},"origin_gross_margin":{"0":"0.137088438982522697"},"real_gross_margin":{"0":"0.109173612393841599"},"cust_cnt":{"0":"44738"},"point_cnt":{"0":"132653"},"origin_pre_cust_price":{"0":"1429.116192945594349323"},"real_pre_cust_price":{"0":"1384.333582937425576793"},"timing_origin_amt":{"0":"3507383"},"timing_real_amt":{"0":"3272386.263454545454545426"},"consign_origin_amt":{"0":"0"},"consign_real_amt":{"0":"0"},"consign_marketing_amt":{"0":"0"},"consign_origin_gross":{"0":"0"},"consign_real_gross":{"0":"0"},"consign_cust_cnt":{"0":"0"},"turnover_day_cnt":{"0":"16.026357163698541028"},"damage_amt":{"0":"129538.82"},"storage_amt":{"0":"1310935.925994524446427216"},"arterial_roads_amt":{"0":"1033932.143884318668414899"},"self_picked_amt":{"0":"0"},"allocation_amt":{"0":"252743.463843885655930516"},"other_amt":{"0":"164331.990882696605951611"},"deliver_amt":{"0":"3927634.020492540908854771"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |          1 |           1 |                  1 |
| mean  |      44738 |      132653 |                  0 |
| std   |        nan |         nan |                nan |
| min   |      44738 |      132653 |                  0 |
| 25%   |      44738 |      132653 |                  0 |
| 50%   |      44738 |      132653 |                  0 |
| 75%   |      44738 |      132653 |                  0 |
| max   |      44738 |      132653 |                  0 |