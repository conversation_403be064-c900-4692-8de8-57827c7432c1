# app_log_channel_sku_wi
* comment: 流量分渠道sku转化
* last_data_modified_time: 2025-09-18 02:32:22

# schema:
CREATE TABLE summerfarm_tech.`app_log_channel_sku_wi` (
  `year` STRING COMMENT '年',
  `week_of_year` STRING COMMENT '周数',
  `monday` STRING COMMENT '周一',
  `sunday` STRING COMMENT '周天',
  `sku_id` STRING COMMENT 'sku编号',
  `category1` STRING COMMENT '一级类目',
  `category2` STRING COMMENT '二级类目',
  `category3` STRING COMMENT '三级类目',
  `category4` STRING COMMENT '四级类目',
  `sku_type` STRING COMMENT '自营，代仓，代售',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品描述',
  `model` STRING COMMENT '渠道',
  `uv` BIGINT COMMENT '曝光uv',
  `pv` BIGINT COMMENT '曝光pv',
  `click_uv` BIGINT COMMENT '点击uv',
  `click_pv` BIGINT COMMENT '点击pv',
  `addbug_uv` BIGINT COMMENT '加买uv',
  `addbug_pv` BIGINT COMMENT '加买pv',
  `cust_cnt` BIGINT COMMENT '交易人数'
)
COMMENT '流量分渠道sku转化'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"sku_id":{"0":"None","1":"1001218743510","2":"1001662557788","3":"1003043034688","4":"1003065575401"},"category1":{"0":"None","1":"其他","2":"其他","3":"乳制品","4":"乳制品"},"category2":{"0":"None","1":"成品原料","2":"成品原料","3":"乳制品","4":"乳制品"},"category3":{"0":"None","1":"方便速食","2":"方便速食","3":"黄油","4":"黄油"},"category4":{"0":"None","1":"速食面","2":"速食面","3":"乳酸黄油","4":"乳酸黄油"},"sku_type":{"0":"None","1":"2","2":"2","3":"2","4":"2"},"spu_name":{"0":"None","1":"水妈妈春卷皮（22cm）","2":"水妈妈春卷皮（16cm）","3":"菲仕兰乳酸发酵黄油","4":"柏札莱阿尔卑黄油"},"sku_disc":{"0":"None","1":"500g*32包","2":"200g*50包","3":"25KG*1箱","4":"400g*10盒\/400g"},"model":{"0":"banner","1":"banner","2":"banner","3":"banner","4":"banner"},"uv":{"0":"143","1":"2","2":"2","3":"22","4":"24"},"pv":{"0":"303","1":"2","2":"2","3":"90","4":"30"},"click_uv":{"0":"489","1":"0","2":"0","3":"0","4":"0"},"click_pv":{"0":"2762","1":"0","2":"0","3":"0","4":"0"},"addbug_uv":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"addbug_pv":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"cust_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |         uv |        pv |   click_uv |   click_pv |   addbug_uv |   addbug_pv |   cust_cnt |
|:------|-----------:|----------:|-----------:|-----------:|------------:|------------:|-----------:|
| count | 10000      | 10000     | 10000      | 10000      | 10000       | 10000       | 10000      |
| mean  |    84.0519 |   109.169 |     4.2799 |     9.0253 |     0.6101  |     1.0556  |     1.0754 |
| std   |   214.736  |   336.766 |    22.1418 |    58.9215 |     5.54919 |     9.59331 |    11.056  |
| min   |     1      |     1     |     0      |     0      |     0       |     0       |     0      |
| 25%   |     9      |    10     |     0      |     0      |     0       |     0       |     0      |
| 50%   |    25      |    29     |     0      |     0      |     0       |     0       |     0      |
| 75%   |    74.25   |    91     |     2      |     2      |     0       |     0       |     0      |
| max   |  9175      | 18418     |   781      |  2762      |   214       |   341       |   436      |