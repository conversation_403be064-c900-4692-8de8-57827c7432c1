# app_saas_supplier_metrics_summary_di
* comment: saas供应商指标汇总
* last_data_modified_time: 2025-09-18 02:06:29

# schema:
CREATE TABLE summerfarm_tech.`app_saas_supplier_metrics_summary_di` (
  `time_tag` STRING COMMENT '日期',
  `tenant_id` BIGINT COMMENT 'sku租户id',
  `supplier_no` BIGINT COMMENT '供应商序号',
  `supplier_name` STRING COMMENT '供应商名称',
  `supplier_type` STRING COMMENT '供应商类型',
  `purchase_to_warehouse_on_time_rate` DECIMAL(38,18) COMMENT '近30天采购到仓准时率',
  `fully_stocked_purchase_tasks_num_30d` BIGINT COMMENT '近30天完全入库的采购入库任务数',
  `fully_stocked_on_time_purchase_tasks_num_30d` BIGINT COMMENT '近30天完全入库且准时入库的采购入库任务数',
  `to_warehouse_accuracy` DECIMAL(38,18) COMMENT '近30天采购到仓准确率 ',
  `purchase_tasks_num_30d` BIGINT COMMENT '近30天采购入库单数',
  `received_equal_incoming_tasks_num` BIGINT COMMENT '近30天实收数量等于应入数量的入库单数'
)
COMMENT 'saas供应商指标汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"tenant_id":{"0":"2","1":"7","2":"7","3":"7","4":"8"},"supplier_no":{"0":"1823","1":"1869","2":"3035","3":"3357","4":"1830"},"supplier_name":{"0":"大鹏测试","1":"龙岩市森汇食品有限公司","2":"无锡祁牧鲜活供应链管理有限公司","3":"广西亿顺食品有限公司","4":"上海艾炒品牌管理有限公司"},"supplier_type":{"0":"企业（生产商）","1":"企业（生产商）","2":"企业（生产商）","3":"企业（生产商）","4":"企业（经销商）"},"purchase_to_warehouse_on_time_rate":{"0":"100","1":"0","2":"100","3":"100","4":"81.81999999999999"},"fully_stocked_purchase_tasks_num_30d":{"0":"5","1":"1","2":"4","3":"2","4":"11"},"fully_stocked_on_time_purchase_tasks_num_30d":{"0":"5","1":"0","2":"4","3":"2","4":"9"},"to_warehouse_accuracy":{"0":"100","1":"0","2":"100","3":"100","4":"91.67"},"purchase_tasks_num_30d":{"0":"5","1":"1","2":"4","3":"2","4":"12"},"received_equal_incoming_tasks_num":{"0":"5","1":"0","2":"4","3":"2","4":"11"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   supplier_no |   fully_stocked_purchase_tasks_num_30d |   fully_stocked_on_time_purchase_tasks_num_30d |   purchase_tasks_num_30d |   received_equal_incoming_tasks_num |
|:------|------------:|--------------:|---------------------------------------:|-----------------------------------------------:|-------------------------:|------------------------------------:|
| count |     84      |        84     |                               84       |                                       84       |                  84      |                             84      |
| mean  |     62.0833 |      2730.75  |                                7.19048 |                                        6.07143 |                  11.7857 |                             11.25   |
| std   |     31.7624 |       478.366 |                               12.8714  |                                       11.0637  |                  25.0869 |                             24.2903 |
| min   |      2      |      1823     |                                1       |                                        0       |                   1      |                              0      |
| 25%   |     38      |      2243     |                                1       |                                        1       |                   2      |                              1.75   |
| 50%   |     85      |      2804     |                                3       |                                        2       |                   3.5    |                              3      |
| 75%   |     85      |      3122     |                                7       |                                        5       |                   9      |                              8.25   |
| max   |    116      |      3398     |                               78       |                                       68       |                 161      |                            155      |