# app_pcs_direct_category_warehouse_purchase_kpi_wi
* comment: 直采kpi
* last_data_modified_time: 2025-09-18 03:23:54

# schema:
CREATE TABLE summerfarm_tech.`app_pcs_direct_category_warehouse_purchase_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `warehouse_no` BIGINT COMMENT '库存仓号',
  `warehouse_name` STRING COMMENT '库存仓',
  `category4` STRING COMMENT '四级类目',
  `direct_purchase_amt` DECIMAL(38,18) COMMENT '直采金额',
  `purchases_amt` DECIMAL(38,18) COMMENT '采购金额（非直采）',
  `cost_flow_amt` DECIMAL(38,18) COMMENT '成本浮动金额',
  `direct_delivery_origin_amt` DECIMAL(38,18) COMMENT '直采履约应付金额',
  `direct_delivery_real_amt` DECIMAL(38,18) COMMENT '直采履约实付金额',
  `direct_delivery_market_amt` DECIMAL(38,18) COMMENT '直采营销费用',
  `direct_delivery_cost_amt` DECIMAL(38,18) COMMENT '直采成本费用',
  `direct_init_amt` DECIMAL(38,18) COMMENT '直采期末库存金额',
  `direct_after_sale_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责售后金额',
  `direct_damage_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责货损金额'
)
COMMENT '直采kpi'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"warehouse_no":{"0":"38","1":"125","2":"150","3":"69","4":"150"},"warehouse_name":{"0":"福州总仓","1":"南京总仓","2":"嘉兴水果批发总仓","3":"东莞总仓","4":"嘉兴水果批发总仓"},"category4":{"0":"柚","1":"提子","2":"国产红心","3":"柑","4":"雪莲果"},"direct_purchase_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"purchases_amt":{"0":"0","1":"7906","2":"0","3":"0","4":"0"},"cost_flow_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"direct_delivery_origin_amt":{"0":"0","1":"0","2":"0","3":"143","4":"0"},"direct_delivery_real_amt":{"0":"0","1":"0","2":"0","3":"140.06","4":"0"},"direct_delivery_market_amt":{"0":"0","1":"0","2":"0","3":"2.94","4":"0"},"direct_delivery_cost_amt":{"0":"0","1":"0","2":"0","3":"107.8","4":"0"},"direct_init_amt":{"0":"0","1":"0","2":"0","3":"294","4":"0"},"direct_after_sale_pcs_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"direct_damage_pcs_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   warehouse_no |
|:------|---------------:|---------------:|
| count |            330 |       330      |
| mean  |             38 |        78.7303 |
| std   |              0 |        53.6389 |
| min   |             38 |         1      |
| 25%   |             38 |        38      |
| 50%   |             38 |        64      |
| 75%   |             38 |       145      |
| max   |             38 |       155      |