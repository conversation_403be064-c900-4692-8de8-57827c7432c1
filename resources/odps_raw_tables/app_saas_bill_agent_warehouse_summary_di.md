# app_saas_bill_agent_warehouse_summary_di
* comment: SAAS对账单-代仓账单概要
* last_data_modified_time: 2025-09-18 02:52:43

# schema:
CREATE TABLE summerfarm_tech.`app_saas_bill_agent_warehouse_summary_di` (
  `tenant_id` BIGINT COMMENT '租户Id',
  `time_tag` STRING COMMENT '时间标签',
  `total_actual_warehouse_expenses` DECIMAL(38,18) COMMENT '代仓实付费用合计',
  `total_warehouse_expenses` DECIMAL(38,18) COMMENT '代仓应付费用合计',
  `total_after_sale_warehouse_expenses` DECIMAL(38,18) COMMENT '已到货售后（代仓服务商责任）费用合计',
  `total_sales_amount` DECIMAL(38,18) COMMENT '销售金额总计',
  `total_sales_amount_wechat_pay` DECIMAL(38,18) COMMENT '销售金额总计（微信支付）',
  `total_sales_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '销售金额总计（账期加余额支付）',
  `after_sale_amount_wechat_pay` DECIMAL(38,18) COMMENT '销售售后金额（微信支付）',
  `after_sale_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '销售售后金额（账期加余额支付）',
  `deduct_after_sales_amount_wechat_pay` DECIMAL(38,18) COMMENT '扣除售后销售金额（微信支付）',
  `deduct_after_sales_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '扣除售后销售金额（账期加余额支付）',
  `delivery_fee_deduct_after_sales_amount` DECIMAL(38,18) COMMENT '剔除售后的订单配送费'
)
COMMENT 'SAAS对账单-代仓账单概要'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"7","1":"8","2":"32","3":"38","4":"47"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"total_actual_warehouse_expenses":{"0":"0","1":"736.46","2":"0","3":"0","4":"0"},"total_warehouse_expenses":{"0":"0","1":"736.46","2":"0","3":"0","4":"0"},"total_after_sale_warehouse_expenses":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"total_sales_amount":{"0":"7238","1":"9205.2","2":"63208.5","3":"43652.1","4":"2060.1"},"total_sales_amount_wechat_pay":{"0":"7238","1":"3428.7","2":"63208.5","3":"43652.1","4":"2060.1"},"total_sales_amount_bill_balance_pay":{"0":"0","1":"5776.5","2":"0","3":"0","4":"0"},"after_sale_amount_wechat_pay":{"0":"0","1":"0","2":"0","3":"1650","4":"0"},"after_sale_amount_bill_balance_pay":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"deduct_after_sales_amount_wechat_pay":{"0":"7238","1":"3428.7","2":"63208.5","3":"42002.1","4":"2060.1"},"deduct_after_sales_amount_bill_balance_pay":{"0":"0","1":"5776.5","2":"0","3":"0","4":"0"},"delivery_fee_deduct_after_sales_amount":{"0":"10","1":"0","2":"1896","3":"0","4":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |
|:------|------------:|
| count |     15      |
| mean  |     67      |
| std   |     36.0753 |
| min   |      7      |
| 25%   |     42.5    |
| 50%   |     63      |
| 75%   |     97.5    |
| max   |    116      |