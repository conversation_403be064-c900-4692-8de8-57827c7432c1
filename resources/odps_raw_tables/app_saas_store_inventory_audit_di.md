# app_saas_store_inventory_audit_di
* comment: 门店库存进销稽核日表
* last_data_modified_time: 2025-09-18 04:23:22

# schema:
CREATE TABLE summerfarm_tech.`app_saas_store_inventory_audit_di` (
  `report_day` DATETIME COMMENT '稽核自然日',
  `channel_type` BIGINT COMMENT '1=美团',
  `tenant_id` BIGINT COMMENT '租户id',
  `out_store_code` STRING COMMENT '外部系统门店code',
  `out_store_name` STRING COMMENT '外部系统门店名称',
  `merchant_store_id` BIGINT COMMENT '帆台门店id',
  `merchant_store_code` STRING COMMENT '帆台门店code',
  `out_item_code` STRING COMMENT '外部系统物料编码',
  `out_item_name` STRING COMMENT '外部系统物料名称',
  `market_item_id` BIGINT COMMENT '帆台商品id',
  `specification` STRING COMMENT '规格',
  `store_ordering_inventory_unit` STRING COMMENT '门店订货单位',
  `store_inventory_unit` STRING COMMENT '门店库存单位',
  `use_count` DECIMAL(38,18) COMMENT '销用总量',
  `need_buy_count` DECIMAL(38,18) COMMENT '应进货总量',
  `real_buy_count` DECIMAL(38,18) COMMENT '实际帆台进货总量',
  `inventory_check_before_count` DECIMAL(38,18) COMMENT '盘前数量',
  `inventory_check_after_count` DECIMAL(38,18) COMMENT '盘后数量'
)
COMMENT '门店库存进销稽核日表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"report_day":{"0":"2025-09-17"},"channel_type":{"0":"3"},"tenant_id":{"0":"88"},"out_store_code":{"0":"None"},"out_store_name":{"0":"None"},"merchant_store_id":{"0":"None"},"merchant_store_code":{"0":"None"},"out_item_code":{"0":"30837"},"out_item_name":{"0":"None"},"market_item_id":{"0":"None"},"specification":{"0":"None"},"store_ordering_inventory_unit":{"0":"None"},"store_inventory_unit":{"0":"None"},"use_count":{"0":"0"},"need_buy_count":{"0":"None"},"real_buy_count":{"0":"None"},"inventory_check_before_count":{"0":"None"},"inventory_check_after_count":{"0":"None"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       | report_day          |   channel_type |   tenant_id |
|:------|:--------------------|---------------:|------------:|
| count | 1                   |              1 |           1 |
| mean  | 2025-09-17 00:00:00 |              3 |          88 |
| min   | 2025-09-17 00:00:00 |              3 |          88 |
| 25%   | 2025-09-17 00:00:00 |              3 |          88 |
| 50%   | 2025-09-17 00:00:00 |              3 |          88 |
| 75%   | 2025-09-17 00:00:00 |              3 |          88 |
| max   | 2025-09-17 00:00:00 |              3 |          88 |
| std   | nan                 |            nan |         nan |