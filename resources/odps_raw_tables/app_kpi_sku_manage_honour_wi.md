# app_kpi_sku_manage_honour_wi
* comment: 履约口径kpi指标月汇总
* last_data_modified_time: 2025-09-18 03:40:18

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_sku_manage_honour_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `manage_type` STRING COMMENT '商品经营类型：自营，代仓',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额(gmv)',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `after_sale_received_order_cnt` BIGINT COMMENT '已到货售后订单数',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `point_out_rate` DECIMAL(38,18) COMMENT '外区点位占比',
  `point_in_rate` DECIMAL(38,18) COMMENT '内区点位占比',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
  `damage_rate` DECIMAL(38,18) COMMENT '货损率',
  `replenish_out_amt` DECIMAL(38,18) COMMENT '补发出库总金额',
  `return_in_amt` DECIMAL(38,18) COMMENT '退货入库总金额',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `point_day_cnt` BIGINT COMMENT '均日点位数',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费'
)
COMMENT '履约口径kpi指标月汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025"},"week_of_year":{"0":"38","1":"38","2":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921"},"manage_type":{"0":"代售","1":"自营","2":"代仓"},"origin_total_amt":{"0":"1021174.64","1":"11748579.14","2":"3377615.88"},"real_total_amt":{"0":"1000832.05","1":"11393037.25","2":"3377615.88"},"origin_pay_rate":{"0":"0.447414058382805119","1":"1.131928895163980367","2":"0.98571387282795461"},"real_pay_rate":{"0":"0.436182374455334439","1":"1.103976865362020156","2":"0.98571387282795461"},"preferential_amt":{"0":"20342.59","1":"355541.89","2":"0"},"refund_amt":{"0":"41001.3","1":"1026065.42","2":"0"},"after_sale_received_amt":{"0":"8188.16","1":"30019.23","2":"0"},"after_sale_received_order_cnt":{"0":"67","1":"513","2":"0"},"cust_cnt":{"0":"3744","1":"18603","2":"232"},"cust_unit_amt":{"0":"272.749636752136752137","1":"982.027223887559889879","2":"14558.689137931034482759"},"order_cnt":{"0":"4401","1":"27021","2":"368"},"point_cnt":{"0":"3868","1":"19621","2":"232"},"point_out_rate":{"0":"0.01861427094105481","1":"0.02661351147505657","2":"0"},"point_in_rate":{"0":"0.7621509824198552","1":"0.7535825880831807","2":"0.7370689655172413"},"inventory_loss_amt":{"0":"0","1":"498","2":"0"},"inventory_profit_amt":{"0":"0","1":"288","2":"0"},"damage_amt":{"0":"0","1":"18154.8","2":"3704.14"},"damage_rate":{"0":"0","1":"0.001545276223078666","2":"0.001096672958560344"},"replenish_out_amt":{"0":"0","1":"9429.44","2":"176.04"},"return_in_amt":{"0":"0","1":"112635.16","2":"721"},"sku_cnt":{"0":"10442","1":"107278","2":"5549"},"storage_amt":{"0":"23664.873532178832615934","1":"225794.263159558332689182","2":"7721.553576953068883023"},"arterial_roads_amt":{"0":"19541.709736074084064368","1":"183093.907290285386293789","2":"16348.941076361715637108"},"deliver_amt":{"0":"72604.015517342574911947","1":"680219.318804086823762527","2":"30673.028990324166193994"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"other_amt":{"0":"3077.391613635179658249","1":"28761.343540808065863237","2":"2101.473989395999551382"},"point_day_cnt":{"0":"3868","1":"19621","2":"232"},"allocation_amt":{"0":"2704.564060883269658528","1":"40898.586309674897849899","2":"117.174990082360108302"},"delivery_amt":{"0":"4874.25","1":"23070.93","2":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   after_sale_received_order_cnt |   cust_cnt |   order_cnt |   point_cnt |   sku_cnt |   point_day_cnt |
|:------|---------------:|--------------------------------:|-----------:|------------:|------------:|----------:|----------------:|
| count |              3 |                           3     |       3    |         3   |         3   |       3   |             3   |
| mean  |             38 |                         193.333 |    7526.33 |     10596.7 |      7907   |   41089.7 |          7907   |
| std   |              0 |                         278.859 |    9752.07 |     14366.1 |     10306.2 |   57373   |         10306.2 |
| min   |             38 |                           0     |     232    |       368   |       232   |    5549   |           232   |
| 25%   |             38 |                          33.5   |    1988    |      2384.5 |      2050   |    7995.5 |          2050   |
| 50%   |             38 |                          67     |    3744    |      4401   |      3868   |   10442   |          3868   |
| 75%   |             38 |                         290     |   11173.5  |     15711   |     11744.5 |   58860   |         11744.5 |
| max   |             38 |                         513     |   18603    |     27021   |     19621   |  107278   |         19621   |