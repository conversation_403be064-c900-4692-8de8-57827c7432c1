# app_saas_self_goods_cost_price_df
* comment: SAAS自营货品成本价格表
* last_data_modified_time: 2025-09-18 01:22:30

# schema:
CREATE TABLE summerfarm_tech.`app_saas_self_goods_cost_price_df` (
  `tenant_id` BIGINT COMMENT '租户Id',
  `sku_id` BIGINT COMMENT '鲜沐sku主键',
  `sku_code` STRING COMMENT '鲜沐sku',
  `province` STRING COMMENT '省',
  `city` STRING COMMENT '城市',
  `area` STRING COMMENT '区域',
  `price` DECIMAL(38,18) COMMENT '价格',
  `valid_time` DATETIME COMMENT '日期'
)
COMMENT 'SAAS自营货品成本价格表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"sku_id":{"0":"10637","1":"12570","2":"13412","3":"10637","4":"12570"},"sku_code":{"0":"1038757775048","1":"734115302785","2":"734726181378","3":"1038757775048","4":"734115302785"},"province":{"0":"河南省","1":"河南省","2":"河南省","3":"河南省","4":"河南省"},"city":{"0":"商丘市","1":"商丘市","2":"商丘市","3":"商丘市","4":"商丘市"},"area":{"0":"宁陵县","1":"宁陵县","2":"宁陵县","3":"梁园区","4":"梁园区"},"price":{"0":"1","1":"1.57","2":"1","3":"1","4":"1.57"},"valid_time":{"0":"2025-09-17","1":"2025-09-17","2":"2025-09-17","3":"2025-09-17","4":"2025-09-17"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   sku_id | valid_time          |
|:------|------------:|---------:|:--------------------|
| count |  10000      | 10000    | 10000               |
| mean  |     10.8248 | 15680    | 2025-09-17 00:00:00 |
| min   |      2      |  5916    | 2025-09-17 00:00:00 |
| 25%   |      4      | 10872    | 2025-09-17 00:00:00 |
| 50%   |      7      | 13695    | 2025-09-17 00:00:00 |
| 75%   |      8      | 19360    | 2025-09-17 00:00:00 |
| max   |     32      | 31975    | 2025-09-17 00:00:00 |
| std   |     10.073  |  6608.42 | nan                 |