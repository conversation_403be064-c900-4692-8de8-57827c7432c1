# app_saas_order_after_sale_inverted_summary_di
* comment: SAAS对账单-售后单倒挂明细
* last_data_modified_time: 2025-09-18 02:35:05

# schema:
CREATE TABLE summerfarm_tech.`app_saas_order_after_sale_inverted_summary_di` (
  `tenant_id` BIGINT COMMENT '租户Id',
  `time_tag` STRING COMMENT '时间标签',
  `after_sale_order_no` STRING COMMENT '售后单号',
  `order_no` STRING COMMENT '订单编号',
  `order_item_id` STRING COMMENT '订单项编号',
  `apply_time` DATETIME COMMENT '售后申请时间',
  `audit_time` DATETIME COMMENT '审核时间',
  `refund_total_amount` DECIMAL(38,18) COMMENT '退款总额',
  `responsibility_type` BIGINT COMMENT '售后责任方 0、供应商1、品牌方2、门店',
  `brand_refundable_amout` DECIMAL(38,18) COMMENT '品牌应退金额',
  `brand_actual_amount` DECIMAL(38,18) COMMENT '品牌实退金额',
  `supplier_refundable_amount` DECIMAL(38,18) COMMENT '供应商应退金额',
  `supplier_actual_amount` DECIMAL(38,18) COMMENT '供应商实退金额',
  `inverted_amount` DECIMAL(38,18) COMMENT '品牌应付给供应商的差额（供应商实退金额-供应商应退金额）',
  `supplier_id` BIGINT COMMENT '供应商Id'
)
COMMENT 'SAAS对账单-售后单倒挂明细'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"-1","1":"123"},"time_tag":{"0":"20250917","1":"20250917"},"after_sale_order_no":{"0":"None","1":"AS1968250103782666240"},"order_no":{"0":"None","1":"OR175801399822578"},"order_item_id":{"0":"None","1":"1785610"},"apply_time":{"0":"NaT","1":"2025-09-17 17:46:07"},"audit_time":{"0":"NaT","1":"2025-09-17 19:06:06"},"refund_total_amount":{"0":"None","1":"9.17"},"responsibility_type":{"0":"nan","1":"0.0"},"brand_refundable_amout":{"0":"None","1":"0"},"brand_actual_amount":{"0":"None","1":"0"},"supplier_refundable_amount":{"0":"None","1":"8.34"},"supplier_actual_amount":{"0":"None","1":"9.17"},"inverted_amount":{"0":"None","1":"0.83"},"supplier_id":{"0":"nan","1":"0.0"},"ds":{"0":"20250917","1":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id | apply_time          | audit_time          |   responsibility_type |   supplier_id |
|:------|------------:|:--------------------|:--------------------|----------------------:|--------------:|
| count |      2      | 1                   | 1                   |                     1 |             1 |
| mean  |     61      | 2025-09-17 17:46:07 | 2025-09-17 19:06:06 |                     0 |             0 |
| min   |     -1      | 2025-09-17 17:46:07 | 2025-09-17 19:06:06 |                     0 |             0 |
| 25%   |     30      | 2025-09-17 17:46:07 | 2025-09-17 19:06:06 |                     0 |             0 |
| 50%   |     61      | 2025-09-17 17:46:07 | 2025-09-17 19:06:06 |                     0 |             0 |
| 75%   |     92      | 2025-09-17 17:46:07 | 2025-09-17 19:06:06 |                     0 |             0 |
| max   |    123      | 2025-09-17 17:46:07 | 2025-09-17 19:06:06 |                     0 |             0 |
| std   |     87.6812 | nan                 | nan                 |                   nan |           nan |