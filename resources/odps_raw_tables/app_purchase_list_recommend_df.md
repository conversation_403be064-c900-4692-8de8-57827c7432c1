# app_purchase_list_recommend_df
* comment: 进货单-推荐商品表
* last_data_modified_time: 2025-09-18 02:33:24

# schema:
CREATE TABLE summerfarm_tech.`app_purchase_list_recommend_df` (
  `m_id` BIGINT COMMENT '商家ID',
  `sku` STRING COMMENT 'sku',
  `product_id` BIGINT COMMENT 'pd_id',
  `recommend_sort` BIGINT COMMENT '推荐排序'
)
COMMENT '进货单-推荐商品表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"m_id":{"0":"10","1":"10","2":"10","3":"10","4":"10"},"sku":{"0":"L001S01R001","1":"K001N01Z001","2":"N001S01R002","3":"835228062","4":"Q001F01R007"},"product_id":{"0":"71","1":"186","2":"52","3":"1235","4":"175"},"recommend_sort":{"0":"1","1":"2","2":"3","3":"4","4":"5"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |     m_id |   product_id |   recommend_sort |
|:------|---------:|-------------:|-----------------:|
| count | 10000    |     10000    |      10000       |
| mean  |  3254.56 |      2122.13 |         10.5     |
| std   |  1913.81 |      3555.97 |          5.76657 |
| min   |    10    |         1    |          1       |
| 25%   |  1459.5  |       104    |          5.75    |
| 50%   |  3204    |       606.5  |         10.5     |
| 75%   |  4901    |      1691    |         15.25    |
| max   |  6593    |     17930    |         20       |