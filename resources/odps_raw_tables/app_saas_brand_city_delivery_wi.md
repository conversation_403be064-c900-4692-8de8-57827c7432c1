# app_saas_brand_city_delivery_wi
* comment: saas履约网络可视化表
* last_data_modified_time: 2025-09-18 02:56:47

# schema:
CREATE TABLE summerfarm_tech.`app_saas_brand_city_delivery_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `brand_alias` STRING COMMENT '品牌名称',
  `province` STRING COMMENT '省',
  `city` STRING COMMENT '市',
  `area` STRING COMMENT '区',
  `point_cnt` BIGINT COMMENT '点位数'
)
COMMENT 'saas履约网络可视化表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921"},"brand_alias":{"0":"GIGI LUCKY舒芙蕾","1":"GIGI LUCKY舒芙蕾","2":"GIGI LUCKY舒芙蕾","3":"GIGI LUCKY舒芙蕾","4":"GIGI LUCKY舒芙蕾"},"province":{"0":"广东","1":"广东","2":"广东","3":"广东","4":"广东"},"city":{"0":"东莞市","1":"佛山市","2":"广州市","3":"惠州市","4":"江门市"},"area":{"0":"南城街道","1":"顺德区","2":"天河区","3":"惠城区","4":"江海区"},"point_cnt":{"0":"1","1":"1","2":"2","3":"1","4":"1"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   point_cnt |
|:------|---------------:|------------:|
| count |            561 |   561       |
| mean  |             38 |     2.90553 |
| std   |              0 |     3.61445 |
| min   |             38 |     1       |
| 25%   |             38 |     1       |
| 50%   |             38 |     2       |
| 75%   |             38 |     3       |
| max   |             38 |    39       |