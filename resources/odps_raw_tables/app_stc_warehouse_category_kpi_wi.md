# app_stc_warehouse_category_kpi_wi
* comment: 品控kpi
* last_data_modified_time: 2025-09-18 03:08:11

# schema:
CREATE TABLE summerfarm_tech.`app_stc_warehouse_category_kpi_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` BIGINT COMMENT '周数',
  `monday` STRING COMMENT '周一日期',
  `sunday` STRING COMMENT '周日日期',
  `warehouse_no` BIGINT COMMENT '库存仓',
  `warehouse_name` STRING COMMENT '库存仓名',
  `category` STRING COMMENT '类目：鲜果，标品',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后金额',
  `after_sale_amt_check` DECIMAL(38,18) COMMENT '售后金额品控责',
  `damage_cnt` BIGINT COMMENT '货损数量',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `damage_cnt_wah` BIGINT COMMENT '货损数量_仓配责',
  `damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额_仓配责',
  `damage_cnt_pur` BIGINT COMMENT '货损数量_采购责',
  `damage_amt_pur` DECIMAL(38,18) COMMENT '货损金额_采购责',
  `damage_cnt_opr` BIGINT COMMENT '货损数量_运营责',
  `damage_amt_opr` DECIMAL(38,18) COMMENT '货损金额_运营责',
  `damage_cnt_oth` BIGINT COMMENT '货损数量_其他责',
  `damage_amt_oth` DECIMAL(38,18) COMMENT '货损金额_其他责',
  `sale_cnt` BIGINT COMMENT '销售数量',
  `sale_amt` DECIMAL(38,18) COMMENT '销售金额',
  `test_cnt` BIGINT COMMENT '抽检数量',
  `qualified_cnt` BIGINT COMMENT '合格数量',
  `check_cnt` BIGINT COMMENT '货检数量',
  `inbound_cnt` BIGINT COMMENT '入库数量'
)
COMMENT '品控kpi'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025","1":"2025","2":"2025","3":"2025","4":"2025","5":"2025","6":"2025","7":"2025","8":"2025","9":"2025"},"week_of_year":{"0":"38","1":"38","2":"38","3":"38","4":"38","5":"38","6":"38","7":"38","8":"38","9":"38"},"monday":{"0":"20250915","1":"20250915","2":"20250915","3":"20250915","4":"20250915","5":"20250915","6":"20250915","7":"20250915","8":"20250915","9":"20250915"},"sunday":{"0":"20250921","1":"20250921","2":"20250921","3":"20250921","4":"20250921","5":"20250921","6":"20250921","7":"20250921","8":"20250921","9":"20250921"},"warehouse_no":{"0":"64","1":"38","2":"125","3":"125","4":"63","5":"150","6":"69","7":"121","8":"63","9":"64"},"warehouse_name":{"0":"青岛总仓","1":"福州总仓","2":"南京总仓","3":"南京总仓","4":"贵阳总仓","5":"嘉兴水果批发总仓","6":"东莞总仓","7":"嘉兴海盐总仓","8":"贵阳总仓","9":"青岛总仓"},"category":{"0":"鲜果","1":"鲜果","2":"标品","3":"鲜果","4":"标品","5":"鲜果","6":"标品","7":"标品","8":"鲜果","9":"标品"},"origin_total_amt":{"0":"48375.5","1":"114420.49","2":"631862.98","3":"179827.11","4":"79506.71","5":"0","6":"1726079.53","7":"453913.55","8":"4618.6","9":"187162.55"},"real_total_amt":{"0":"46778.930000000000000012","1":"108389.910000000000000001","2":"610005.080476190476190489","3":"174073.540000000000000008","4":"78121.28","5":"0","6":"1671582.468888888888888901","7":"443984.345238095238095228","8":"4597.02","9":"179551.399999999999999998"},"after_sale_amt":{"0":"1001.99","1":"2128.23","2":"4082.55","3":"1979.33","4":"427.16","5":"0","6":"3504.56","7":"1554.36","8":"4.63","9":"1016.95"},"after_sale_amt_check":{"0":"786.38","1":"1855.11","2":"429.06","3":"1170.99","4":"427.16","5":"0","6":"1367.72","7":"86.46","8":"4.63","9":"816.95"},"damage_cnt":{"0":"1","1":"16","2":"6","3":"11","4":"0","5":"0","6":"8","7":"1","8":"11","9":"5"},"damage_amt":{"0":"70","1":"910.01","2":"698","3":"410.4","4":"0","5":"0","6":"1904.5","7":"12.16","8":"440.2","9":"305.32"},"damage_cnt_wah":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"damage_amt_wah":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"damage_cnt_pur":{"0":"0","1":"0","2":"0","3":"2","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"damage_amt_pur":{"0":"0","1":"0","2":"0","3":"85","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"damage_cnt_opr":{"0":"0","1":"3","2":"3","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"1"},"damage_amt_opr":{"0":"0","1":"180","2":"464","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"150"},"damage_cnt_oth":{"0":"1","1":"13","2":"3","3":"9","4":"0","5":"0","6":"8","7":"1","8":"11","9":"4"},"damage_amt_oth":{"0":"70","1":"730.01","2":"234","3":"325.4","4":"0","5":"0","6":"1904.5","7":"12.16","8":"440.2","9":"155.32"},"sale_cnt":{"0":"1165","1":"1895","2":"3708","3":"3690","4":"294","5":"59","6":"8662","7":"4635","8":"127","9":"904"},"sale_amt":{"0":"38296.32","1":"81010.99","2":"543269.43","3":"133969.79","4":"73094.84","5":"0","6":"1477801.21","7":"408967.23","8":"3812.64","9":"167416.09"},"test_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"qualified_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"check_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"inbound_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   week_of_year |   warehouse_no |   damage_cnt |   damage_cnt_wah |   damage_cnt_pur |   damage_cnt_opr |   damage_cnt_oth |   sale_cnt |   test_cnt |   qualified_cnt |   check_cnt |   inbound_cnt |
|:------|---------------:|---------------:|-------------:|-----------------:|-----------------:|-----------------:|-----------------:|-----------:|-----------:|----------------:|------------:|--------------:|
| count |             30 |        30      |      30      |          30      |       30         |         30       |          30      |      30    |         30 |              30 |          30 |            30 |
| mean  |             38 |        73.3667 |      17.1333 |           0.4    |        0.0666667 |          2.9     |          13.7667 |    3872.4  |          0 |               0 |           0 |             0 |
| std   |              0 |        46.9963 |      36.8358 |           1.4527 |        0.365148  |          8.70731 |          35.468  |    5820.1  |          0 |               0 |           0 |             0 |
| min   |             38 |         2      |       0      |           0      |        0         |          0       |           0      |      21    |          0 |               0 |           0 |             0 |
| 25%   |             38 |        40.5    |       0.25   |           0      |        0         |          0       |           0      |     387.75 |          0 |               0 |           0 |             0 |
| 50%   |             38 |        62.5    |       5      |           0      |        0         |          0       |           3      |    1975    |          0 |               0 |           0 |             0 |
| 75%   |             38 |       120      |      15.25   |           0      |        0         |          0.75    |          10.5    |    4234.5  |          0 |               0 |           0 |             0 |
| max   |             38 |       155      |     192      |           7      |        2         |         40       |         192      |   27865    |          0 |               0 |           0 |             0 |