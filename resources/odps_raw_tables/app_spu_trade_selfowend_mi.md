# app_spu_trade_selfowend_mi
* comment: 自营品城市整体交易数据月表
* last_data_modified_time: 2025-09-18 02:53:11

# schema:
CREATE TABLE summerfarm_tech.`app_spu_trade_selfowend_mi` (
  `month` STRING COMMENT '月份',
  `register_province` STRING COMMENT '注册省',
  `register_city` STRING COMMENT '注册市',
  `register_area` STRING COMMENT '注册区',
  `cause_type` STRING COMMENT '鲜沐，SAAS',
  `spu_id` STRING COMMENT 'pd_id',
  `spu_name` STRING COMMENT 'SPU名称',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举：大客户,普通,批发客户;<以前的：单店,批发大客户,普通大客户,KA大客户 已弃用>',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目',
  `category2_id` STRING COMMENT '二级类目id',
  `category2` STRING COMMENT '二级类目',
  `category3_id` STRING COMMENT '三级类目id',
  `category3` STRING COMMENT '三级类目',
  `category4_id` STRING COMMENT '四级类目id',
  `category4` STRING COMMENT '四级类目',
  `origin_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `new_cust_cnt` BIGINT COMMENT '（历史截止今天）当月新客户数',
  `order_time_cnt` DECIMAL(38,18) COMMENT '客户下单时间间隔之和,天',
  `order_time_avg` DECIMAL(38,18) COMMENT '平均下单时间间隔,天',
  `order_time_cnt_m` DECIMAL(38,18) COMMENT '客户下单时间间隔之和,分',
  `order_time_avg_m` DECIMAL(38,18) COMMENT '平均下单时间间隔,分',
  `before_month_cust_cnt` BIGINT COMMENT 'T-1月客户数',
  `month_cust_cnt` BIGINT COMMENT 'T-1月T月客户数'
)
COMMENT '自营品城市整体交易数据月表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509","5":"202509","6":"202509","7":"202509","8":"202509","9":"202509"},"register_province":{"0":"上海","1":"上海","2":"江苏","3":"四川","4":"广东","5":"广东","6":"重庆","7":"重庆","8":"上海","9":"上海"},"register_city":{"0":"上海市","1":"上海市","2":"常州市","3":"成都市","4":"深圳市","5":"珠海市","6":"重庆市","7":"重庆市","8":"上海市","9":"上海市"},"register_area":{"0":"长宁区","1":"虹口区","2":"新北区","3":"成华区","4":"龙华区","5":"金湾区","6":"南岸区","7":"沙坪坝区","8":"静安区","9":"虹口区"},"cause_type":{"0":"SAAS","1":"SAAS","2":"SAAS","3":"SAAS","4":"SAAS","5":"SAAS","6":"SAAS","7":"SAAS","8":"SAAS","9":"SAAS"},"spu_id":{"0":"3830","1":"4758","2":"5779","3":"101068","4":"101068","5":"101068","6":"101068","7":"101068","8":"104285","9":"104292"},"spu_name":{"0":"C味马蹄爆爆珠","1":"C味血糯米罐头","2":"澄善速冻百香果浆","3":"Protag纯牛奶","4":"Protag纯牛奶","5":"Protag纯牛奶","6":"Protag纯牛奶","7":"Protag纯牛奶","8":"澄善草莓果酱","9":"澄善金枕榴莲碎果肉"},"cust_type":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"brand_type":{"0":"大客户","1":"大客户","2":"大客户","3":"大客户","4":"大客户","5":"大客户","6":"大客户","7":"大客户","8":"大客户","9":"大客户"},"brand_name":{"0":"C味","1":"C味","2":"澄善","3":"Protag蛋白标签","4":"Protag蛋白标签","5":"Protag蛋白标签","6":"Protag蛋白标签","7":"Protag蛋白标签","8":"澄善","9":"澄善"},"category1":{"0":"其他","1":"其他","2":"其他","3":"乳制品","4":"乳制品","5":"乳制品","6":"乳制品","7":"乳制品","8":"其他","9":"其他"},"category2_id":{"0":"405","1":"409","2":"421","3":"401","4":"401","5":"401","6":"401","7":"401","8":"417","9":"417"},"category2":{"0":"成品原料","1":"谷物制品","2":"饮品原料","3":"乳制品","4":"乳制品","5":"乳制品","6":"乳制品","7":"乳制品","8":"水果制品","9":"水果制品"},"category3_id":{"0":"458","1":"470","2":"522","3":"435","4":"435","5":"435","6":"435","7":"435","8":"506","9":"504"},"category3":{"0":"果冻类配料","1":"谷物罐头","2":"果汁原料","3":"液体乳","4":"液体乳","5":"液体乳","6":"液体乳","7":"液体乳","8":"水果风味制品","9":"冷冻水果"},"category4_id":{"0":"671","1":"713","2":"830","3":"607","4":"607","5":"607","6":"607","7":"607","8":"785","9":"780"},"category4":{"0":"爆爆珠","1":"杂粮罐头","2":"果汁原浆","3":"常温牛奶","4":"常温牛奶","5":"常温牛奶","6":"常温牛奶","7":"常温牛奶","8":"果茶酱","9":"冷冻果肉"},"origin_total_amt":{"0":"15","1":"22","2":"284.99","3":"75","4":"150","5":"86.11","6":"6480","7":"2880","8":"100","9":"85"},"real_total_amt":{"0":"15","1":"22","2":"284.99","3":"75","4":"150","5":"86.11","6":"6480","7":"2880","8":"100","9":"85"},"cust_cnt":{"0":"1","1":"1","2":"1","3":"1","4":"1","5":"1","6":"3","7":"1","8":"1","9":"1"},"new_cust_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"1","7":"0","8":"0","9":"0"},"order_time_cnt":{"0":"0","1":"0","2":"0","3":"0","4":"11","5":"0","6":"12","7":"6","8":"3","9":"0"},"order_time_avg":{"0":"0","1":"0","2":"0","3":"0","4":"11","5":"0","6":"4","7":"6","8":"3","9":"0"},"order_time_cnt_m":{"0":"0","1":"0","2":"0","3":"0","4":"16067","5":"0","6":"18103","7":"8793","8":"4461","9":"0"},"order_time_avg_m":{"0":"0","1":"0","2":"0","3":"0","4":"16067","5":"0","6":"6034.333333333333","7":"8793","8":"4461","9":"0"},"before_month_cust_cnt":{"0":"1","1":"1","2":"1","3":"1","4":"1","5":"1","6":"3","7":"1","8":"1","9":"1"},"month_cust_cnt":{"0":"1","1":"1","2":"1","3":"1","4":"1","5":"1","6":"3","7":"1","8":"1","9":"1"},"ds":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509","5":"202509","6":"202509","7":"202509","8":"202509","9":"202509"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |    cust_cnt |   new_cust_cnt |   before_month_cust_cnt |   month_cust_cnt |
|:------|------------:|---------------:|------------------------:|-----------------:|
| count | 10000       |   10000        |             10000       |      10000       |
| mean  |     1.5351  |       0.2877   |                 1.6183  |          0.8263  |
| std   |     1.57029 |       0.518802 |                 2.61868 |          1.13554 |
| min   |     0       |       0        |                 0       |          0       |
| 25%   |     1       |       0        |                 0       |          0       |
| 50%   |     1       |       0        |                 1       |          1       |
| 75%   |     1       |       1        |                 2       |          1       |
| max   |    33       |       6        |                60       |         19       |