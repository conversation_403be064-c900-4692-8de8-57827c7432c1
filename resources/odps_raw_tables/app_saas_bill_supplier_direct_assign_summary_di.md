# app_saas_bill_supplier_direct_assign_summary_di
* comment: SAAS对账单-供应商直配账单概要表
* last_data_modified_time: 2025-09-18 02:52:49

# schema:
CREATE TABLE summerfarm_tech.`app_saas_bill_supplier_direct_assign_summary_di` (
  `tenant_id` BIGINT COMMENT '租户Id',
  `supplier_id` BIGINT COMMENT '供应商id',
  `time_tag` STRING COMMENT '时间标签',
  `total_purchase_amount` DECIMAL(38,18) COMMENT '采购金额总计',
  `total_purchase_amount_wechat_pay` DECIMAL(38,18) COMMENT '采购金额总计（微信支付）',
  `total_purchase_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '采购金额总计（账期加余额支付）',
  `total_amount_of_purchase_and_after_sales_wechat_pay` DECIMAL(38,18) COMMENT '等比换算后采购售后金额合计（微信支付）',
  `total_amount_of_purchase_and_after_sales_bill_balance_pay` DECIMAL(38,18) COMMENT '等比换算后采购售后金额合计（账期和余额支付）',
  `total_purchase_amount_remove_refund_wechat_pay` DECIMAL(38,18) COMMENT '扣除售后的采购金额（微信支付）',
  `total_purchase_amount_remove_refund_bill_balance_pay` DECIMAL(38,18) COMMENT '扣除售后的采购金额（账期和余额支付）',
  `sales_and_purchase_difference_wechat_pay` DECIMAL(38,18) COMMENT '销售与采购差额(剔除售后,不含运费)（微信支付）',
  `sales_and_purchase_difference_bill_balance_pay` DECIMAL(38,18) COMMENT '销售与采购差额(剔除售后,不含运费)（账期加余额支付）',
  `total_sales_amount` DECIMAL(38,18) COMMENT '销售金额总计',
  `total_sales_amount_wechat_pay` DECIMAL(38,18) COMMENT '销售金额总计（微信支付）',
  `total_sales_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '销售金额总计（账期加余额支付）',
  `after_sale_amount_wechat_pay` DECIMAL(38,18) COMMENT '销售售后金额（微信支付）',
  `after_sale_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '销售售后金额（账期加余额支付）',
  `deduct_after_sales_amount_wechat_pay` DECIMAL(38,18) COMMENT '扣除售后销售金额（微信支付）',
  `deduct_after_sales_amount_bill_balance_pay` DECIMAL(38,18) COMMENT '扣除售后销售金额（账期加余额支付）',
  `goods_delivery_fee_remove_refund` DECIMAL(38,18) COMMENT '供应商配送费扣除售后',
  `delivery_fee_deduct_after_sales_amount` DECIMAL(38,18) COMMENT '剔除售后的订单配送费'
)
COMMENT 'SAAS对账单-供应商直配账单概要表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"8","1":"8","2":"13","3":"14","4":"32"},"supplier_id":{"0":"0","1":"1830","2":"0","3":"0","4":"0"},"time_tag":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"total_purchase_amount":{"0":"964.43","1":"14","2":"27850.56","3":"22121.4","4":"2468"},"total_purchase_amount_wechat_pay":{"0":"367.3","1":"0","2":"17672.46","3":"22121.4","4":"2468"},"total_purchase_amount_bill_balance_pay":{"0":"597.13","1":"14","2":"10178.1","3":"0","4":"0"},"total_amount_of_purchase_and_after_sales_wechat_pay":{"0":"0","1":"0","2":"1845.97","3":"241.14","4":"0"},"total_amount_of_purchase_and_after_sales_bill_balance_pay":{"0":"0","1":"0","2":"288.06","3":"0","4":"0"},"total_purchase_amount_remove_refund_wechat_pay":{"0":"367.3","1":"0","2":"15826.49","3":"21880.26","4":"2468"},"total_purchase_amount_remove_refund_bill_balance_pay":{"0":"597.13","1":"14","2":"9890.04","3":"0","4":"0"},"sales_and_purchase_difference_wechat_pay":{"0":"0","1":"0","2":"0","3":"1765","4":"92"},"sales_and_purchase_difference_bill_balance_pay":{"0":"18","1":"4","2":"0","3":"0","4":"0"},"total_sales_amount":{"0":"982.43","1":"18","2":"27850.56","3":"23905.7","4":"2560"},"total_sales_amount_wechat_pay":{"0":"367.3","1":"0","2":"17672.46","3":"23905.7","4":"2560"},"total_sales_amount_bill_balance_pay":{"0":"615.13","1":"18","2":"10178.1","3":"0","4":"0"},"after_sale_amount_wechat_pay":{"0":"0","1":"0","2":"1845.97","3":"260.44","4":"0"},"after_sale_amount_bill_balance_pay":{"0":"0","1":"0","2":"288.06","3":"0","4":"0"},"deduct_after_sales_amount_wechat_pay":{"0":"367.3","1":"0","2":"15826.49","3":"23645.26","4":"2560"},"deduct_after_sales_amount_bill_balance_pay":{"0":"615.13","1":"18","2":"9890.04","3":"0","4":"0"},"goods_delivery_fee_remove_refund":{"0":"0","1":"0","2":"70","3":"330","4":"0"},"delivery_fee_deduct_after_sales_amount":{"0":"0","1":"0","2":"70","3":"30","4":"1012"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   supplier_id |
|:------|------------:|--------------:|
| count |     39      |         39    |
| mean  |     74.5385 |       1012.31 |
| std   |     37.1992 |       1414.24 |
| min   |      8      |          0    |
| 25%   |     42.5    |          0    |
| 50%   |     85      |          0    |
| 75%   |    105      |       2389    |
| max   |    123      |       3404    |