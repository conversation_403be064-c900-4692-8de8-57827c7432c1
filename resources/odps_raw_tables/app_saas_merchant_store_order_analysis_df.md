# app_saas_merchant_store_order_analysis_df
* comment: saas-门店订货分析
* last_data_modified_time: 2025-09-18 02:38:19

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_order_analysis_df` (
  `tenant_id` BIGINT COMMENT '租户id',
  `type` BIGINT COMMENT '1、周 2、月 3、季度',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `store_id` BIGINT COMMENT '门店id',
  `average_order_period` DECIMAL(38,18) COMMENT '平均订货周期',
  `average_order_period_last_period` DECIMAL(38,18) COMMENT '上周期平均订货周期',
  `average_order_period_upper_period` DECIMAL(38,18) COMMENT '平均订货周期环比',
  `order_amount` BIGINT COMMENT '订货数量',
  `order_amount_last_period` BIGINT COMMENT '上周期订货数量',
  `order_amount_upper_period` DECIMAL(38,18) COMMENT '订货数量环比',
  `order_price` DECIMAL(38,18) COMMENT '订货金额',
  `order_price_last_period` DECIMAL(38,18) COMMENT '上周期订货金额',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '订货金额环比',
  `last_order_time` STRING COMMENT '最后订货日期 yyyy-MM-dd',
  `last_order_amount` BIGINT COMMENT '最后订货数量',
  `last_order_price` DECIMAL(38,18) COMMENT '最后订货金额'
)
COMMENT 'saas-门店订货分析'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"type":{"0":"3","1":"3","2":"3","3":"3","4":"3"},"time_tag":{"0":"20220401","1":"20220401","2":"20220401","3":"20220701","4":"20220701"},"store_id":{"0":"1","1":"3","2":"5","3":"2","4":"5"},"average_order_period":{"0":"1","1":"1","2":"1","3":"1","4":"2"},"average_order_period_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"1"},"average_order_period_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"100"},"order_amount":{"0":"6","1":"2","2":"1","3":"1","4":"2"},"order_amount_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"1"},"order_amount_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"100"},"order_price":{"0":"174","1":"23.02","2":"0.01","3":"24","4":"20"},"order_price_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0.01"},"order_price_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"199900"},"last_order_time":{"0":"2022-06-19","1":"2022-06-19","2":"2022-06-02","3":"2022-09-06","4":"2022-09-21"},"last_order_amount":{"0":"5","1":"1","2":"1","3":"1","4":"1"},"last_order_price":{"0":"148","1":"23","2":"0.01","3":"24","4":"10"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   type |   store_id |   order_amount |   order_amount_last_period |   last_order_amount |
|:------|------------:|-------:|-----------:|---------------:|---------------------------:|--------------------:|
| count |  10000      |  10000 |   10000    |      10000     |                  10000     |          10000      |
| mean  |     19.7216 |      3 |   75989.9  |        477.663 |                    371.536 |             40.0229 |
| std   |     17.6446 |      0 |  147506    |       3560.27  |                   3320.51  |            401.353  |
| min   |      2      |      3 |       1    |          1     |                      0     |              1      |
| 25%   |      8      |      3 |     953    |         15     |                      0     |              2      |
| 50%   |     14      |      3 |    1749    |         49     |                     23     |              3      |
| 75%   |     31      |      3 |    3285.25 |        134     |                     98     |              6      |
| max   |     64      |      3 |  396432    |      86643     |                  86643     |          12902      |