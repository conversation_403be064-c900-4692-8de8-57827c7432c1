# app_platform_kpi_di
* comment: 平台KPI
* last_data_modified_time: 2025-09-18 03:29:20

# schema:
CREATE TABLE summerfarm_tech.`app_platform_kpi_di` (
  `date` STRING COMMENT '日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV',
  `timing_cust_cnt` BIGINT COMMENT '省心送履约客户数',
  `timing_ratio` DECIMAL(38,18) COMMENT '省心送合单率',
  `timing_cust_30_not_cnt` BIGINT COMMENT '省心送超过30天未履约客户数',
  `timing_cust_30_90_cnt` BIGINT COMMENT '省心送在30天-90天履约客户数',
  `login_uv` BIGINT COMMENT '登陆UV',
  `order_uv` BIGINT COMMENT '交易客户数',
  `activity_uv` BIGINT COMMENT '特价点击UV',
  `activity_order_uv` BIGINT COMMENT '特价下单人数',
  `exchange_uv` BIGINT COMMENT '换购点击UV',
  `exchange_order_uv` BIGINT COMMENT '换购下单人数',
  `expand_uv` BIGINT COMMENT '拓展购买点击UV',
  `expand_order_uv` BIGINT COMMENT '拓展购买下单人数',
  `meeting_uv` BIGINT COMMENT '会场活动页点击UV',
  `meeting_order_uv` BIGINT COMMENT '会场活动页下单人数',
  `other_uv` BIGINT COMMENT '其他点击UV',
  `other_order_uv` BIGINT COMMENT '其他下单人数',
  `timing_all_cust_cnt` BIGINT COMMENT '省心送总客户数'
)
COMMENT '平台KPI'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917"},"origin_total_amt":{"0":"3922935.6"},"real_total_amt":{"0":"3783091.111904761904761989"},"cust_cnt":{"0":"7374"},"preferential_amt":{"0":"139844.488095238095238011"},"timing_origin_amt":{"0":"253286"},"timing_cust_cnt":{"0":"262"},"timing_ratio":{"0":"0.5114503816793893"},"timing_cust_30_not_cnt":{"0":"918"},"timing_cust_30_90_cnt":{"0":"15492"},"login_uv":{"0":"16178"},"order_uv":{"0":"7239"},"activity_uv":{"0":"394"},"activity_order_uv":{"0":"0"},"exchange_uv":{"0":"56"},"exchange_order_uv":{"0":"0"},"expand_uv":{"0":"0"},"expand_order_uv":{"0":"0"},"meeting_uv":{"0":"0"},"meeting_order_uv":{"0":"0"},"other_uv":{"0":"15249"},"other_order_uv":{"0":"4351"},"timing_all_cust_cnt":{"0":"25876"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   timing_cust_cnt |   timing_cust_30_not_cnt |   timing_cust_30_90_cnt |   login_uv |   order_uv |   activity_uv |   activity_order_uv |   exchange_uv |   exchange_order_uv |   expand_uv |   expand_order_uv |   meeting_uv |   meeting_order_uv |   other_uv |   other_order_uv |   timing_all_cust_cnt |
|:------|-----------:|------------------:|-------------------------:|------------------------:|-----------:|-----------:|--------------:|--------------------:|--------------:|--------------------:|------------:|------------------:|-------------:|-------------------:|-----------:|-----------------:|----------------------:|
| count |          1 |                 1 |                        1 |                       1 |          1 |          1 |             1 |                   1 |             1 |                   1 |           1 |                 1 |            1 |                  1 |          1 |                1 |                     1 |
| mean  |       7374 |               262 |                      918 |                   15492 |      16178 |       7239 |           394 |                   0 |            56 |                   0 |           0 |                 0 |            0 |                  0 |      15249 |             4351 |                 25876 |
| std   |        nan |               nan |                      nan |                     nan |        nan |        nan |           nan |                 nan |           nan |                 nan |         nan |               nan |          nan |                nan |        nan |              nan |                   nan |
| min   |       7374 |               262 |                      918 |                   15492 |      16178 |       7239 |           394 |                   0 |            56 |                   0 |           0 |                 0 |            0 |                  0 |      15249 |             4351 |                 25876 |
| 25%   |       7374 |               262 |                      918 |                   15492 |      16178 |       7239 |           394 |                   0 |            56 |                   0 |           0 |                 0 |            0 |                  0 |      15249 |             4351 |                 25876 |
| 50%   |       7374 |               262 |                      918 |                   15492 |      16178 |       7239 |           394 |                   0 |            56 |                   0 |           0 |                 0 |            0 |                  0 |      15249 |             4351 |                 25876 |
| 75%   |       7374 |               262 |                      918 |                   15492 |      16178 |       7239 |           394 |                   0 |            56 |                   0 |           0 |                 0 |            0 |                  0 |      15249 |             4351 |                 25876 |
| max   |       7374 |               262 |                      918 |                   15492 |      16178 |       7239 |           394 |                   0 |            56 |                   0 |           0 |                 0 |            0 |                  0 |      15249 |             4351 |                 25876 |