# app_log_puchase_transfer_di
* comment: 采购助手转化漏斗
* last_data_modified_time: 2025-09-18 02:33:31

# schema:
CREATE TABLE summerfarm_tech.`app_log_puchase_transfer_di` (
  `date` STRING COMMENT '日期',
  `type` STRING COMMENT 'V3、V4、对照组',
  `category` STRING COMMENT '搜索模块、分类模块、采购助手模块',
  `cust_cnt` BIGINT COMMENT '曝光UV',
  `click_uv` BIGINT COMMENT '商品点击UV',
  `order_cnt` BIGINT COMMENT '宽口径：如下单前15分钟内在对应模块有点击行为，则纳入统计'
)
COMMENT '采购助手转化漏斗'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区'
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"},"type":{"0":"V4","1":"对照组","2":"V4","3":"V3","4":"V4"},"category":{"0":"分类页面","1":"分类页面","2":"采购助手","3":"搜索页面","4":"搜索页面"},"cust_cnt":{"0":"6625","1":"1","2":"1613","3":"1","4":"7741"},"click_uv":{"0":"4392","1":"0","2":"917","3":"0","4":"6116"},"order_cnt":{"0":"2296","1":"0","2":"329","3":"0","4":"3272"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   click_uv |   order_cnt |
|:------|-----------:|-----------:|------------:|
| count |       6    |       6    |       6     |
| mean  |    2663.67 |    1904.17 |     982.833 |
| std   |    3573.37 |    2675.1  |    1434.58  |
| min   |       1    |       0    |       0     |
| 25%   |       1    |       0    |       0     |
| 50%   |     807    |     458.5  |     164.5   |
| 75%   |    5372    |    3523.25 |    1804.25  |
| max   |    7741    |    6116    |    3272     |