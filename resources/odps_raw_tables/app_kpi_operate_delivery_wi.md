# app_kpi_operate_delivery_wi
* comment: 运营履约kpi（平台客户）
* last_data_modified_time: 2025-09-18 03:41:39

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_operate_delivery_wi` (
  `year` STRING COMMENT '年份',
  `week_of_year` STRING COMMENT '周数',
  `monday` STRING COMMENT '周一',
  `sunday` STRING COMMENT '周天',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
  `turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约kpi（平台客户）'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"year":{"0":"2025"},"week_of_year":{"0":"38"},"monday":{"0":"20250915"},"sunday":{"0":"20250921"},"origin_total_amt":{"0":"11022871.06"},"real_total_amt":{"0":"10667885.0736813186813188"},"marketing_amt":{"0":"354985.9863186813186812"},"cost_amt":{"0":"9493034.41"},"origin_gross":{"0":"1529836.65"},"real_gross":{"0":"1174850.6636813186813188"},"origin_gross_margin":{"0":"0.138787493899978542"},"real_gross_margin":{"0":"0.110129670086134165"},"cust_cnt":{"0":"18210"},"point_cnt":{"0":"22919"},"origin_pre_cust_price":{"0":"605.319662822624931356"},"real_pre_cust_price":{"0":"585.825649296063628848"},"timing_origin_amt":{"0":"682201"},"timing_real_amt":{"0":"636589.003681318681318679"},"consign_origin_amt":{"0":"0"},"consign_real_amt":{"0":"0"},"consign_marketing_amt":{"0":"0"},"consign_origin_gross":{"0":"0"},"consign_real_gross":{"0":"0"},"consign_cust_cnt":{"0":"0"},"turnover_day_cnt":{"0":"16.683389024342090113"},"damage_amt":{"0":"18154.8"},"storage_amt":{"0":"231351.211393971279747866"},"arterial_roads_amt":{"0":"187208.098477233527319534"},"self_picked_amt":{"0":"0"},"allocation_amt":{"0":"41834.179435045515659006"},"other_amt":{"0":"28999.726326030532038677"},"deliver_amt":{"0":"702303.83486172488515694"},"ds":{"0":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   point_cnt |   consign_cust_cnt |
|:------|-----------:|------------:|-------------------:|
| count |          1 |           1 |                  1 |
| mean  |      18210 |       22919 |                  0 |
| std   |        nan |         nan |                nan |
| min   |      18210 |       22919 |                  0 |
| 25%   |      18210 |       22919 |                  0 |
| 50%   |      18210 |       22919 |                  0 |
| 75%   |      18210 |       22919 |                  0 |
| max   |      18210 |       22919 |                  0 |