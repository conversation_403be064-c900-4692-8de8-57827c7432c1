# app_spu_delivery_selfowend_di
* comment: 城市整体配送数据日表
* last_data_modified_time: 2025-09-18 03:04:44

# schema:
CREATE TABLE summerfarm_tech.`app_spu_delivery_selfowend_di` (
  `date` STRING COMMENT '日期',
  `province` STRING COMMENT '省',
  `admin_city` STRING COMMENT '市',
  `area` STRING COMMENT '区',
  `spu_id` BIGINT COMMENT 'pd_id',
  `spu_name` STRING COMMENT 'spu名称',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举：大客户,普通,批发客户;<以前的：单店,批发大客户,普通大客户,KA大客户 已弃用>',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目',
  `category2_id` STRING COMMENT '二级类目id',
  `category2` STRING COMMENT '二级类目',
  `category3_id` STRING COMMENT '三级类目id',
  `category3` STRING COMMENT '三级类目',
  `category4_id` STRING COMMENT '四级类目id',
  `category4` STRING COMMENT '四级类目',
  `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '总成本',
  `origin_pay_margin` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_pay_margin` DECIMAL(38,18) COMMENT '实付毛利润',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `deliver_total_amt` DECIMAL(38,18) COMMENT '配送GMV'
)
COMMENT '城市整体配送数据日表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"},"province":{"0":"广东","1":"广西壮族自治区","2":"江苏","3":"江苏","4":"江苏","5":"浙江","6":"湖南","7":"湖南","8":"湖南","9":"福建"},"admin_city":{"0":"广州市","1":"南宁市","2":"南京市","3":"淮安市","4":"盐城市","5":"温州市","6":"益阳市","7":"郴州市","8":"长沙市","9":"莆田市"},"area":{"0":"海珠区","1":"西乡塘区","2":"玄武区","3":"清江浦区","4":"亭湖区","5":"瑞安市","6":"资阳区","7":"北湖区","8":"宁乡市","9":"仙游县"},"spu_id":{"0":"1691","1":"1691","2":"1691","3":"1691","4":"1691","5":"1691","6":"1691","7":"1691","8":"1691","9":"1691"},"spu_name":{"0":"C味原味波波晶球","1":"C味原味波波晶球","2":"C味原味波波晶球","3":"C味原味波波晶球","4":"C味原味波波晶球","5":"C味原味波波晶球","6":"C味原味波波晶球","7":"C味原味波波晶球","8":"C味原味波波晶球","9":"C味原味波波晶球"},"cust_type":{"0":"茶饮","1":"茶饮","2":"茶饮","3":"甜品冰淇淋","4":"面包蛋糕","5":"其他","6":"西餐","7":"西餐","8":"茶饮","9":"茶饮"},"brand_type":{"0":"普通","1":"普通","2":"普通","3":"普通","4":"普通","5":"普通","6":"普通","7":"普通","8":"普通","9":"普通"},"brand_name":{"0":"C味","1":"C味","2":"C味","3":"C味","4":"C味","5":"C味","6":"C味","7":"C味","8":"C味","9":"C味"},"category1":{"0":"其他","1":"其他","2":"其他","3":"其他","4":"其他","5":"其他","6":"其他","7":"其他","8":"其他","9":"其他"},"category2_id":{"0":"405","1":"405","2":"405","3":"405","4":"405","5":"405","6":"405","7":"405","8":"405","9":"405"},"category2":{"0":"成品原料","1":"成品原料","2":"成品原料","3":"成品原料","4":"成品原料","5":"成品原料","6":"成品原料","7":"成品原料","8":"成品原料","9":"成品原料"},"category3_id":{"0":"458","1":"458","2":"458","3":"458","4":"458","5":"458","6":"458","7":"458","8":"458","9":"458"},"category3":{"0":"果冻类配料","1":"果冻类配料","2":"果冻类配料","3":"果冻类配料","4":"果冻类配料","5":"果冻类配料","6":"果冻类配料","7":"果冻类配料","8":"果冻类配料","9":"果冻类配料"},"category4_id":{"0":"666","1":"666","2":"666","3":"666","4":"666","5":"666","6":"666","7":"666","8":"666","9":"666"},"category4":{"0":"波波丨晶球","1":"波波丨晶球","2":"波波丨晶球","3":"波波丨晶球","4":"波波丨晶球","5":"波波丨晶球","6":"波波丨晶球","7":"波波丨晶球","8":"波波丨晶球","9":"波波丨晶球"},"origin_total_amt":{"0":"92","1":"92","2":"7.8","3":"7.8","4":"92","5":"7.8","6":"7.8","7":"92","8":"92","9":"184"},"real_total_amt":{"0":"92","1":"92","2":"6","3":"6","4":"80","5":"6","6":"7.8","7":"83.04","8":"92","9":"160"},"cost_amt":{"0":"64.8","1":"64.8","2":"5.4","3":"5.4","4":"64.8","5":"5.4","6":"5.4","7":"64.8","8":"64.8","9":"129.6"},"origin_pay_margin":{"0":"27.2","1":"27.2","2":"2.4","3":"2.4","4":"27.2","5":"2.4","6":"2.4","7":"27.2","8":"27.2","9":"54.4"},"real_pay_margin":{"0":"27.2","1":"27.2","2":"0.6","3":"0.6","4":"15.2","5":"0.6","6":"2.4","7":"18.24","8":"27.2","9":"30.4"},"preferential_amt":{"0":"0","1":"0","2":"1.8","3":"1.8","4":"12","5":"1.8","6":"0","7":"8.96","8":"0","9":"24"},"after_sale_amt":{"0":"0","1":"0","2":"0","3":"0","4":"0","5":"0","6":"0","7":"0","8":"0","9":"0"},"deliver_total_amt":{"0":"92","1":"92","2":"7.8","3":"7.8","4":"92","5":"7.8","6":"7.8","7":"92","8":"92","9":"184"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917","5":"20250917","6":"20250917","7":"20250917","8":"20250917","9":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   spu_id |
|:------|---------:|
| count |  2200    |
| mean  |  6645.91 |
| std   |  3265.83 |
| min   |  1528    |
| 25%   |  4219    |
| 50%   |  5789    |
| 75%   |  9260    |
| max   | 18589    |