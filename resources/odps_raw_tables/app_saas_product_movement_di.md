# app_saas_product_movement_di
* comment: saas商品维度动销表
* last_data_modified_time: 2025-09-18 02:38:20

# schema:
CREATE TABLE summerfarm_tech.`app_saas_product_movement_di` (
  `tenant_id` BIGINT COMMENT '租户id',
  `type` BIGINT COMMENT '时间标签类型：1、日 2、周 3、月',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `on_sale_num` BIGINT COMMENT '在售商品数',
  `pay_success_num` BIGINT COMMENT '支付成功商品数',
  `sale_rate` DECIMAL(38,18) COMMENT '动销率(支付成功商品数/在售商品数)',
  `last_on_sale_num` BIGINT COMMENT '上个周期在售商品数',
  `on_sale_chain` DECIMAL(38,18) COMMENT '在售环比',
  `last_pay_success_num` BIGINT COMMENT '上个周期支付商品成功数',
  `pay_success_chain` DECIMAL(38,18) COMMENT '支付成功环比',
  `last_sale_rate` DECIMAL(38,18) COMMENT '上个周期动销率',
  `sale_rate_chain` DECIMAL(38,18) COMMENT '动销环比',
  `warehouse_type` BIGINT COMMENT '归属类型 0自营品 1三方品',
  `delivery_type` BIGINT COMMENT '配送方式 0品牌方配送 1三方配送',
  `goods_type` BIGINT COMMENT '商品类型 0无货商品 1报价货品 2自营货品'
)
COMMENT 'saas商品维度动销表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"4"},"type":{"0":"3","1":"3","2":"3","3":"3","4":"3"},"time_tag":{"0":"20250901","1":"20250901","2":"20250901","3":"20250901","4":"20250901"},"on_sale_num":{"0":"153","1":"117","2":"137","3":"19","4":"17"},"pay_success_num":{"0":"12","1":"0","2":"5","3":"0","4":"0"},"sale_rate":{"0":"7.84","1":"0","2":"3.65","3":"0","4":"0"},"last_on_sale_num":{"0":"146","1":"114","2":"134","3":"19","4":"17"},"on_sale_chain":{"0":"4.794520547945205","1":"2.631578947368421","2":"2.238805970149254","3":"0","4":"0"},"last_pay_success_num":{"0":"2","1":"0","2":"1","3":"0","4":"0"},"pay_success_chain":{"0":"500","1":"0","2":"400","3":"0","4":"0"},"last_sale_rate":{"0":"1.37","1":"0","2":"0.75","3":"0","4":"0"},"sale_rate_chain":{"0":"472.2627737226277372","1":"0","2":"386.6666666666666667","3":"0","4":"0"},"warehouse_type":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"delivery_type":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"goods_type":{"0":"0","1":"1","2":"2","3":"3","4":"1"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |       type |   on_sale_num |   pay_success_num |   last_on_sale_num |   last_pay_success_num |   goods_type |
|:------|------------:|-----------:|--------------:|------------------:|-------------------:|-----------------------:|-------------:|
| count |    406      | 406        |       406     |          406      |           406      |              406       |    406       |
| mean  |     61.5911 |   2.00246  |        81.968 |            9.9064 |            81.1182 |                9.47537 |      1.15271 |
| std   |     37.3275 |   0.818003 |       165.436 |           19.5878 |           164.581  |               18.5055  |      0.83523 |
| min   |      2      |   1        |         0     |            0      |             0      |                0       |      0       |
| 25%   |     32      |   1        |        13     |            0      |            12.25   |                0       |      1       |
| 50%   |     58      |   2        |        42     |            0      |            42      |                0       |      1       |
| 75%   |    100      |   3        |       100     |           11      |            98.75   |               11.75    |      2       |
| max   |    123      |   3        |      1645     |          155      |          1645      |              125       |      3       |