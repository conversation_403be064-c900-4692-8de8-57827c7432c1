# app_self_category_delivery_warehouse_kpi_di
* comment: 履约口径kpi指标日汇总(自营)
* last_data_modified_time: 2025-09-18 03:41:49

# schema:
CREATE TABLE summerfarm_tech.`app_self_category_delivery_warehouse_kpi_di` (
  `date` STRING COMMENT '日期',
  `category` STRING COMMENT '鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `day_point_cnt` BIGINT COMMENT '日均点位',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
  `damage_rate` DECIMAL(38,18) COMMENT '货损占比',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
)
COMMENT '履约口径kpi指标日汇总(自营)'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917"},"category":{"0":"鲜果","1":"乳制品","2":"其他"},"origin_total_amt":{"0":"1056486.47","1":"2253683.57","2":"656844.15"},"real_total_amt":{"0":"1017092.350000000000000046","1":"2182833.985238095238095271","2":"627229.606666666666666672"},"cost_amt":{"0":"783368.33","1":"2099560.26","2":"537827"},"timing_origin_total_amt":{"0":"0","1":"192044","2":"61242"},"timing_real_total_amt":{"0":"0","1":"180678.59523809523809524","2":"56600.666666666666666667"},"cust_cnt":{"0":"5270","1":"2682","2":"2497"},"order_cnt":{"0":"5931","1":"2893","2":"2711"},"point_cnt":{"0":"5446","1":"2742","2":"2547"},"day_point_cnt":{"0":"5448","1":"2742","2":"2547"},"sku_cnt":{"0":"22525","1":"7428","2":"6615"},"delivery_amt":{"0":"5224.56","1":"1694.13","2":"546.99"},"after_sale_received_amt":{"0":"4477.33","1":"1445.82","2":"3695.81"},"inventory_loss_amt":{"0":"0","1":"0","2":"498"},"inventory_profit_amt":{"0":"0","1":"0","2":"288"},"damage_amt":{"0":"4999.44","1":"61.35","2":"922.34"},"damage_rate":{"0":"0.004732138216592589","1":"0.000027222100217024","2":"0.001404199154396062"},"storage_amt":{"0":"31843.438928338968326009","1":"29330.786186584016605743","2":"21278.506156380367408339"},"arterial_roads_amt":{"0":"25821.049370102993789189","1":"25246.714547610528146079","2":"18786.59923621067123311"},"deliver_amt":{"0":"102917.796151617825762662","1":"90248.480471446358373643","2":"65378.548946458231074243"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"other_amt":{"0":"4260.450476303547423211","1":"3769.506981466155815919","2":"2504.13170017937827355"},"allocation_amt":{"0":"4829.675969021004938514","1":"5357.924379097449460699","2":"3768.247009971903430097"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   cust_cnt |   order_cnt |   point_cnt |   day_point_cnt |   sku_cnt |
|:------|-----------:|------------:|------------:|----------------:|----------:|
| count |       3    |        3    |        3    |            3    |      3    |
| mean  |    3483    |     3845    |     3578.33 |         3579    |  12189.3  |
| std   |    1550.35 |     1808.82 |     1620.38 |         1621.54 |   8960.18 |
| min   |    2497    |     2711    |     2547    |         2547    |   6615    |
| 25%   |    2589.5  |     2802    |     2644.5  |         2644.5  |   7021.5  |
| 50%   |    2682    |     2893    |     2742    |         2742    |   7428    |
| 75%   |    3976    |     4412    |     4094    |         4095    |  14976.5  |
| max   |    5270    |     5931    |     5446    |         5448    |  22525    |