# app_merchant_pool_data_df
* comment: 圈人2.0商家数据
* last_data_modified_time: 2025-09-18 11:01:08

# schema:
CREATE TABLE summerfarm_tech.`app_merchant_pool_data_df` (
  `pool_info_id` BIGINT COMMENT '圈人2.0主键ID',
  `m_id` BIGINT COMMENT '商家id',
  `area_no` BIGINT COMMENT '门店运营服务区域',
  `size` STRING COMMENT '店铺类型：单店、大客户',
  `day_tag` STRING COMMENT '日期标签，格式为YYYYMMDD'
)
COMMENT '圈人2.0商家数据'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc
LIFECYCLE 1

# head data:
{"pool_info_id":{"0":"852","1":"852","2":"852","3":"852","4":"852"},"m_id":{"0":"29","1":"79","2":"115","3":"117","4":"132"},"area_no":{"0":"1001","1":"1001","2":"10287","3":"1001","4":"1001"},"size":{"0":"单店","1":"单店","2":"单店","3":"单店","4":"单店"},"day_tag":{"0":"20250918","1":"20250918","2":"20250918","3":"20250918","4":"20250918"},"ds":{"0":"20250918","1":"20250918","2":"20250918","3":"20250918","4":"20250918"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   pool_info_id |      m_id |   area_no |
|:------|---------------:|----------:|----------:|
| count |     10000      |  10000    |   10000   |
| mean  |       909.572  | 157200    |   11274   |
| std   |        47.5393 | 202195    |   14600.2 |
| min   |       852      |      1    |    1001   |
| 25%   |       854      |   3245.75 |    1001   |
| 50%   |       950      |   6905.5  |    2750   |
| 75%   |       950      | 349706    |   18268   |
| max   |       950      | 569570    |   44259   |