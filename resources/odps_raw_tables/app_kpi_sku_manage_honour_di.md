# app_kpi_sku_manage_honour_di
* comment: 履约口径商品经营维度kpi指标日汇总
* last_data_modified_time: 2025-09-18 03:43:29

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_sku_manage_honour_di` (
  `date` STRING COMMENT '日期',
  `manage_type` STRING COMMENT '商品经营类型：自营，代仓',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额(gmv)',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `after_sale_received_order_cnt` BIGINT COMMENT '已到货售后订单数',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `point_out_rate` DECIMAL(38,18) COMMENT '外区点位占比',
  `point_in_rate` DECIMAL(38,18) COMMENT '内区点位占比',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
  `damage_rate` DECIMAL(38,18) COMMENT '货损率',
  `replenish_out_amt` DECIMAL(38,18) COMMENT '补发出库总金额',
  `return_in_amt` DECIMAL(38,18) COMMENT '退货入库总金额',
  `sku_cnt` BIGINT COMMENT 'sku数量',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `point_day_cnt` BIGINT COMMENT '均日点位数',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费'
)
COMMENT '履约口径商品经营维度kpi指标日汇总'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"date":{"0":"20250917","1":"20250917","2":"20250917"},"manage_type":{"0":"代售","1":"自营","2":"代仓"},"origin_total_amt":{"0":"329765.78","1":"4076108.24","2":"439015.9"},"real_total_amt":{"0":"324105.58","1":"3936249.99","2":"439015.9"},"origin_pay_rate":{"0":"0.418996719429165755","1":"1.13573997089850899","2":"0.979243895266663463"},"real_pay_rate":{"0":"0.408850042014086891","1":"1.104096400336139338","2":"0.979243895266663463"},"preferential_amt":{"0":"5660.2","1":"139858.25","2":"0"},"refund_amt":{"0":"15221.56","1":"391279.7","2":"0"},"after_sale_received_amt":{"0":"2411.95","1":"9618.96","2":"0"},"after_sale_received_order_cnt":{"0":"23","1":"181","2":"0"},"cust_cnt":{"0":"1372","1":"7563","2":"38"},"cust_unit_amt":{"0":"240.354067055393586006","1":"802.819902616018475619","2":"11553.05"},"order_cnt":{"0":"1447","1":"9049","2":"39"},"point_cnt":{"0":"1394","1":"7847","2":"38"},"point_out_rate":{"0":"0.02869440459110473","1":"0.03568521031207599","2":"0"},"point_in_rate":{"0":"0.7410329985652798","1":"0.7324287652645861","2":"0.6842105263157895"},"inventory_loss_amt":{"0":"0","1":"498","2":"0"},"inventory_profit_amt":{"0":"0","1":"288","2":"0"},"damage_amt":{"0":"187","1":"5983.13","2":"39.5"},"damage_rate":{"0":"0.000567069148290644","1":"0.001467853562200792","2":"0.00008997396221868"},"replenish_out_amt":{"0":"175","1":"2172.17","2":"1.04"},"return_in_amt":{"0":"320","1":"2986.8","2":"0"},"sku_cnt":{"0":"3403","1":"36887","2":"733"},"storage_amt":{"0":"7679.465337314912160377","1":"79540.853780998531635801","2":"1043.596770529436214304"},"arterial_roads_amt":{"0":"6850.65140535876905763","1":"67925.870317395419720548","2":"5996.205138420826264616"},"deliver_amt":{"0":"24206.651485232045680068","1":"246817.974515941901346545","2":"5853.449804985772021043"},"self_picked_amt":{"0":"0","1":"0","2":"0"},"other_amt":{"0":"1004.761822780966937709","1":"10119.95280754242020468","2":"296.162890892196226965"},"point_day_cnt":{"0":"1394","1":"7847","2":"38"},"allocation_amt":{"0":"886.779566135958783052","1":"13271.972023840326770323","2":"18.689563183325999339"},"delivery_amt":{"0":"1498.64","1":"7545.68","2":"0"},"ds":{"0":"20250917","1":"20250917","2":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   after_sale_received_order_cnt |   cust_cnt |   order_cnt |   point_cnt |   sku_cnt |   point_day_cnt |
|:------|--------------------------------:|-----------:|------------:|------------:|----------:|----------------:|
| count |                          3      |       3    |        3    |        3    |       3   |            3    |
| mean  |                         68      |    2991    |     3511.67 |     3093    |   13674.3 |         3093    |
| std   |                         98.5343 |    4015.26 |     4846.87 |     4172.54 |   20147   |         4172.54 |
| min   |                          0      |      38    |       39    |       38    |     733   |           38    |
| 25%   |                         11.5    |     705    |      743    |      716    |    2068   |          716    |
| 50%   |                         23      |    1372    |     1447    |     1394    |    3403   |         1394    |
| 75%   |                        102      |    4467.5  |     5248    |     4620.5  |   20145   |         4620.5  |
| max   |                        181      |    7563    |     9049    |     7847    |   36887   |         7847    |