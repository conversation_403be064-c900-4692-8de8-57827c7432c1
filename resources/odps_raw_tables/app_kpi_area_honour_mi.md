# app_kpi_area_honour_mi
* comment: 履约kpi城配仓维度日表
* last_data_modified_time: 2025-09-18 03:42:51

# schema:
CREATE TABLE summerfarm_tech.`app_kpi_area_honour_mi` (
  `month` STRING COMMENT '日期',
  `area_no` BIGINT COMMENT '城配仓',
  `area_name` STRING COMMENT '城配仓名',
  `sku_type` STRING COMMENT '自营，代仓',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本价',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率',
  `refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `total_deliver_amt` DECIMAL(38,18) COMMENT '履约总费用'
)
COMMENT '履约kpi城配仓维度日表'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"month":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"},"area_no":{"0":"1","1":"1","2":"1","3":"2","4":"2"},"area_name":{"0":"杭州仓","1":"杭州仓","2":"杭州仓","3":"上海仓","4":"上海仓"},"sku_type":{"0":"代仓","1":"代售","2":"自营","3":"代售","4":"自营"},"origin_total_amt":{"0":"349858.82","1":"249553.18","2":"1811728.52","3":"277800.99","4":"1919544.91"},"real_total_amt":{"0":"349858.82","1":"242680.920000000000000014","2":"1766650.016666666666666684","3":"268223.012380952380952391","4":"1866835.925000000000000023"},"cost_amt":{"0":"3067.93","1":"130121.67","2":"1523070.77","3":"166340.23","4":"1619649.78"},"preferential_amt":{"0":"0","1":"6872.259999999999999986","2":"45078.503333333333333316","3":"9577.977619047619047609","4":"52708.984999999999999977"},"origin_pay_rate":{"0":"0.991230948529466829","1":"0.478581398962738123","2":"0.159327264992218591","3":"0.401225208016717291","4":"0.156232411358377648"},"real_pay_rate":{"0":"0.991230948529466829","1":"0.463815820378462386","2":"0.137876344702531678","3":"0.379843554348909014","4":"0.132409143026321395"},"refund_amt":{"0":"0","1":"2016.61","2":"3127.29","3":"1735.62","4":"2680.67"},"cust_cnt":{"0":"13","1":"491","2":"1251","3":"443","4":"1108"},"cust_unit_amt":{"0":"26912.216923076923076923","1":"508.25494908350305499","2":"1448.224236610711430855","3":"627.090270880361173815","4":"1732.441254512635379061"},"order_cnt":{"0":"133","1":"1021","2":"4969","3":"1051","4":"4606"},"point_cnt":{"0":"13","1":"508","2":"1333","3":"489","4":"1251"},"storage_amt":{"0":"1323.169355678384848232","1":"4404.624211096430022228","2":"32142.28470744840239819","3":"7466.481976688028563842","4":"48057.725759363508648099"},"arterial_roads_amt":{"0":"1101.81510434935744155","1":"3667.77046637447202151","2":"26765.171538307679882782","3":"0","4":"0"},"deliver_amt":{"0":"3837.113792656042498624","1":"12773.152763351096847607","2":"93210.746945688517945674","3":"20669.699256934849849505","4":"133039.728953972308334774"},"total_deliver_amt":{"0":"6262.098252683784788406","1":"20845.547440821998891345","2":"152118.203191444600226646","3":"28136.181233622878413347","4":"181097.454713335816982873"},"ds":{"0":"202509","1":"202509","2":"202509","3":"202509","4":"202509"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   area_no |   cust_cnt |   order_cnt |   point_cnt |
|:------|----------:|-----------:|------------:|------------:|
| count |  136      |    136     |      136    |     136     |
| mean  |   64.6324 |    420.853 |     1275.66 |     451.125 |
| std   |   45.1079 |    487.36  |     1722.07 |     530.57  |
| min   |    1      |      1     |        2    |       1     |
| 25%   |   26      |     29.75  |      104.25 |      30     |
| 50%   |   56      |    253.5   |      536    |     261.5   |
| 75%   |   98.25   |    612.5   |     1594.75 |     647.75  |
| max   |  155      |   2127     |     7785    |    2275     |