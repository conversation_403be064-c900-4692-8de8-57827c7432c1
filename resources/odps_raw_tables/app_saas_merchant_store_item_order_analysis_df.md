# app_saas_merchant_store_item_order_analysis_df
* comment: saas-门店商品订货分析
* last_data_modified_time: 2025-09-18 02:38:39

# schema:
CREATE TABLE summerfarm_tech.`app_saas_merchant_store_item_order_analysis_df` (
  `tenant_id` BIGINT COMMENT '租户id',
  `type` BIGINT COMMENT '1、周 2、月 3、季度',
  `time_tag` STRING COMMENT '时间标签 yyyyMMdd',
  `item_id` BIGINT COMMENT '商品id',
  `title` STRING COMMENT '商品名称',
  `store_id` BIGINT COMMENT '门店id',
  `average_order_period` DECIMAL(38,18) COMMENT '平均订货周期',
  `average_order_period_last_period` DECIMAL(38,18) COMMENT '上周期平均订货周期',
  `average_order_period_upper_period` DECIMAL(38,18) COMMENT '平均订货周期环比(百分数)',
  `order_amount` BIGINT COMMENT '订货数量',
  `order_amount_last_period` BIGINT COMMENT '上周期订货数量',
  `order_amount_upper_period` DECIMAL(38,18) COMMENT '订货数量环比(百分数)',
  `order_price` DECIMAL(38,18) COMMENT '订货金额',
  `order_price_last_period` DECIMAL(38,18) COMMENT '上周期订货金额',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '订货金额环比(百分数)',
  `last_order_time` STRING COMMENT '最后订货日期 yyyy-MM-dd',
  `last_order_amount` BIGINT COMMENT '最后订货数量',
  `last_order_price` DECIMAL(38,18) COMMENT '最后订货金额'
)
COMMENT 'saas-门店商品订货分析'
PARTITIONED BY (
  `ds` STRING NOT NULL
)
STORED AS AliOrc

# head data:
{"tenant_id":{"0":"2","1":"2","2":"2","3":"2","4":"2"},"type":{"0":"3","1":"3","2":"3","3":"3","4":"3"},"time_tag":{"0":"20220401","1":"20220401","2":"20220701","3":"20220701","4":"20220701"},"item_id":{"0":"17","1":"21","2":"10","3":"18","4":"18"},"title":{"0":"台农芒果","1":"羊角蜜瓜","2":"泰国无核榴莲冻肉","3":"台农芒果","4":"台农芒果"},"store_id":{"0":"1","1":"1","2":"5","3":"2","4":"10"},"average_order_period":{"0":"1","1":"1","2":"2","3":"1","4":"1"},"average_order_period_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"average_order_period_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_amount":{"0":"1","1":"1","2":"2","3":"1","4":"1"},"order_amount_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_amount_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"order_price":{"0":"58","1":"23","2":"20","3":"24","4":"24"},"order_price_last_period":{"0":"0","1":"0","2":"0","3":"0","4":"0"},"order_price_upper_period":{"0":"None","1":"None","2":"None","3":"None","4":"None"},"last_order_time":{"0":"2022-06-19","1":"2022-06-19","2":"2022-09-21","3":"2022-09-06","4":"2022-08-24"},"last_order_amount":{"0":"1","1":"1","2":"1","3":"1","4":"1"},"last_order_price":{"0":"58","1":"23","2":"10","3":"24","4":"24"},"ds":{"0":"20250917","1":"20250917","2":"20250917","3":"20250917","4":"20250917"}}

# head data desc(generated by pandas.dataframe.describe()):
|       |   tenant_id |   type |   item_id |   store_id |   order_amount |   order_amount_last_period |   last_order_amount |
|:------|------------:|-------:|----------:|-----------:|---------------:|---------------------------:|--------------------:|
| count | 10000       |  10000 |  10000    |  10000     |     10000      |                 10000      |         10000       |
| mean  |     4.9418  |      3 |    421.72 |    380.855 |         8.6757 |                     2.514  |             2.2476  |
| std   |     1.51269 |      0 |    339.57 |    313.471 |        19.7087 |                    11.5863 |             4.37582 |
| min   |     2       |      3 |      1    |      1     |         1      |                     0      |             1       |
| 25%   |     4       |      3 |    189    |    159     |         1      |                     0      |             1       |
| 50%   |     4       |      3 |    277    |    334     |         3      |                     0      |             1       |
| 75%   |     6       |      3 |    570    |    531     |         7      |                     0      |             2       |
| max   |    10       |      3 |   1386    |   2365     |       437      |                   350      |           250       |