```sql
CREATE TABLE IF NOT EXISTS app_crm_bd_day_gmv_di(
	bd_id BIGINT COMMENT '销售ID，唯一标识一个销售人员',
	bd_name STRING COMMENT '销售姓名',
	total_gmv DECIMAL(38,18) COMMENT '总GMV（Gross Merchandise Volume，商品交易总额）',
	single_gmv DECIMAL(38,18) COMMENT '单店GMV',
	vip_gmv DECIMAL(38,18) COMMENT '大客户GMV',
	fruit_gmv DECIMAL(38,18) COMMENT '鲜果品类GMV',
	dairy_gmv DECIMAL(38,18) COMMENT '乳制品品类GMV',
	non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品品类GMV',
	brand_gmv DECIMAL(38,18) COMMENT '自营品牌GMV',
	reward_gmv DECIMAL(38,18) COMMENT '固定奖励SKU的GMV',
	reward_amout BIGINT COMMENT '固定奖励SKU销量',
	category_award DECIMAL(38,18) COMMENT '品类提成金额',
	core_merchant_amout BIGINT COMMENT '核心客户数量',
	core_merchant_card_level DECIMAL(38,18) COMMENT '核心商户数净增长牌级',
	month_live_amout BIGINT COMMENT '月活客户数',
	pull_new_amout BIGINT COMMENT '拉新客户数',
	performance DECIMAL(38,18) COMMENT 'BD绩效金额',
	ordinary_num BIGINT COMMENT '普通拜访次数',
	drop_in_visit_num BIGINT COMMENT '普通上门拜访次数',
	efficient_num BIGINT COMMENT '本月有效拜访次数，‘有效拜访’是特指销售上门成功见到核心 KP（老板 / 下单负责人）并向其介绍商城活动、推荐产品、进行客情维护等',
	worth_num BIGINT COMMENT '价值拜访次数',
	saas_total_gmv_amt DECIMAL(38,18) COMMENT 'SaaS商家GMV金额',
	saas_total_cust_cnt BIGINT COMMENT 'SaaS月活客户数',
	single_month_live_num BIGINT COMMENT '单店月活客户数',
	vip_month_live_num BIGINT COMMENT '品牌客户月活数',
	delivery_gmv DECIMAL(38,18) COMMENT '配送GMV金额',
	spu_average DECIMAL(38,18) COMMENT '配送SPU（Standard Product Unit）均值',
	single_spu_average DECIMAL(38,18) COMMENT '单店配送SPU均值',
	vip_spu_average DECIMAL(38,18) COMMENT '品牌客户配送SPU均值',
	category_multiply_gmv DECIMAL(38,18) COMMENT '周期品类提成*配送金额',
	is_m1 BIGINT COMMENT '是否是M1管理者：1-是，0-否。M1管理者的绩效数据是所有下级BD的绩效数据汇总，因此分析数据时，M1管理者的数据和普通BD的数据需要分开处理',
	delivery_order_cnt BIGINT COMMENT '配送订单数量',
	fruit_month_live_num BIGINT COMMENT '鲜果下单客户数',
	dairy_month_live_num BIGINT COMMENT '乳制品下单客户数',
	non_dairy_month_live_num BIGINT COMMENT '非乳制品下单客户数',
	brand_month_live_num BIGINT COMMENT '自营品牌下单客户数',
	reward_month_live_num BIGINT COMMENT '固定奖励SKU下单客户数',
	normal_pull_new_amout BIGINT COMMENT '普通拉新数（注册且首单<15天）',
	effect_month_live_amout BIGINT COMMENT '有效月活数',
	normal_month_live_amout BIGINT COMMENT '普通月活数',
	no_order_register BIGINT COMMENT '注册未下单客户数',
	agent_goods_gmv DECIMAL(38,18) COMMENT '代售品GMV',
	saas_total_gmv DECIMAL(38,18) COMMENT 'SaaS总GMV',
	saas_xianmu_gvm DECIMAL(38,18) COMMENT 'SaaS鲜沐自营GMV',
	saas_depot_gmv DECIMAL(38,18) COMMENT 'SaaS鲜沐代仓GMV',
	saas_myself_gmv DECIMAL(38,18) COMMENT 'SaaS品牌自营GMV',
	saas_add_good_gvm DECIMAL(38,18) COMMENT 'SaaS鲜沐商品加价收入GMV',
	saas_brand_count BIGINT COMMENT 'SaaS品牌数量',
	saas_order_shop_count BIGINT COMMENT 'SaaS下单门店数量',
	saas_shop_register_count BIGINT COMMENT 'SaaS注册门店数量',
	saas_brand_total_count BIGINT COMMENT 'SaaS品牌门店总数'
) 
COMMENT 'BD本月GMV明细表，包含销售人员的各类GMV指标、拜访指标、客户指标和SaaS相关指标。每个ds分区其实包含了当月的BD绩效，而不是当天的绩效。请你注意甄别'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期。每个分区包含了截止该日期的BD月度销售业绩明细，也就是说，20250831分区包含了202508这个月的汇总数据，以此类推') 
LIFECYCLE 30;
```

-- 获取上个月的BD绩效数据(使用了last_day函数来获取上个月的最后一天，因为ds的格式是yyyyMMdd，且每个分区包含了截止该日期的BD月度销售业绩明细，也就是说，20250831分区包含了202508这个月的汇总数据，以此类推)
select * from app_crm_bd_day_gmv_di 
where ds = replace(LAST_DAY(DATE_SUB(TO_CHAR(GETDATE(), 'yyyy-MM-01'), 1)), '-','');

-- 获取本月截止到当前日期的BD绩效数据(使用了昨天的分区，因为所有的ODPS表的分区都是T+1生成的，所以需要使用昨天的分区)
select * from app_crm_bd_day_gmv_di 
where ds = replace(DATE_SUB(CURRENT_DATE(), 1), '-','');