import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { SendIcon, SquareIcon, WandSparklesIcon, ChatBubbleIcon, AnalyticsIcon, DatabaseIcon } from '../../utils/Icons.js';
// 重构：改为通过统一的会话Store发起 /query
import { conversationStore } from '../store/conversationStore.js';
import QuestionPrompts from './QuestionPrompts.js';
import {
    uploadImage,
    extractImageFromPaste,
    createImagePreviewUrl,
    revokeImagePreviewUrl,
    validateImageFile,
    formatFileSize
} from '../services/uploadService.js';
import { getCookie, setCookie } from '../../utils/CookieUtils.js';
// 重构后不再依赖 useChatState，改为会话Store接管
import { renderMarkdown } from '../../utils/MarkdownRenderer.js';
// Agent配置系统
import {
    getAvailableAgents,
    getAgentConfig,
    getAgentBorderClass,
    getAgentPlaceholder,
    getAgentFooterText,
    getAgentButtonClasses,
    isValidAgent
} from '../config/agentConfig.js';

export default {
    name: 'ChatInput',
    components: {
        QuestionPrompts
    },
    props: {
        conversationId: {
            type: String,
            default: null
        },
        isDevLogVisible: {
            type: Boolean,
            default: false
        },
        messages: {
            type: Array,
            default: () => []
        }
    },
    emits: ['message-sent', 'message-error', 'message-interrupting', 'toggle-dev-log'],
    setup(props, { emit }) {
        const message = ref('');
        const isLoading = ref(false);
        const isStreaming = ref(false);
        const textareaRef = ref(null);
        const queryController = ref(null); // 保留变量名用于中断按钮控制，但不再用于SSE
        const isHistoryTransition = ref(false);

        // 不再依赖 chatState
        const chatState = null;

        // 图片上传相关状态
        const uploadedImages = ref([]);
        const isUploading = ref(false);
        const uploadProgress = ref(0);

        // 提示词增强相关状态
        const isEnhancing = ref(false);

        // Agent选择相关状态
        const availableAgents = ref(getAvailableAgents()); // 获取所有可用的Agent配置
        const selectedAgent = ref(getCookie('selectedAgent') || null); // 从cookie中读取用户的选择

        // 计算是否显示问题列表（只有输入框为空时才显示）
        const showQuestionPrompts = computed(() => {
            return !props.conversationId &&
                   props.messages.length === 0 &&
                   message.value.trim() === '';
        });

        // 增强按钮可用性：输入不为空且未在流式/加载/增强中
        const canEnhance = computed(() => {
            return (
                message.value.trim() !== '' &&
                !isStreamingOrPolling.value &&
                !isEnhancing.value &&
                !isLoading.value
            );
        });

        // 计算：是否处于流式或轮询恢复中（用于页面刷新后的按钮状态恢复）
        const isStreamingOrPolling = computed(() => {
            if (isStreaming.value) return true;
            const msgs = Array.isArray(props.messages) ? props.messages : [];
            return msgs.some(m => m && m.role === 'assistant' && (m.isStreaming === true || m.isInProcess === true));
        });

        // 获取当前活跃的AI消息（正在流式或轮询中的那条）
        const getActiveAssistantMessage = () => {
            const msgs = Array.isArray(props.messages) ? props.messages : [];
            // 取最后一条处于进行中的AI消息
            for (let i = msgs.length - 1; i >= 0; i--) {
                const m = msgs[i];
                if (m && m.role === 'assistant' && (m.isStreaming === true || m.isInProcess === true)) {
                    return m;
                }
            }
            return null;
        };

        // 监听conversationId变化，检测是否是从新对话切换到历史对话
        watch(() => props.conversationId, (newId, oldId) => {
            // 如果从null（新对话）切换到非null（历史对话），标记为历史转换
            if (oldId === null && newId !== null) {
                isHistoryTransition.value = true;
                // 短暂延迟后重置标记，确保动画有时间执行
                setTimeout(() => {
                    isHistoryTransition.value = false;
                }, 100);
            }
        });

        // 根据内容自动调整高度
        const adjustTextareaHeight = () => {
            if (!textareaRef.value) return;

            // 重置高度以获取正确的scrollHeight
            textareaRef.value.style.height = 'auto';

            // 设置新高度，但限制最大高度
            const newHeight = Math.min(Math.max(textareaRef.value.scrollHeight, 24), 200);
            textareaRef.value.style.height = `${newHeight}px`;
        };

        // 计算输入框的行数
        const textareaRows = computed(() => {
            const lineCount = (message.value.match(/\n/g) || []).length + 1;
            return Math.min(lineCount, 8); // 最多显示8行
        });

        // 计算输入框的边框样式（配置化）
        const inputBoxBorderClass = computed(() => {
            return getAgentBorderClass(selectedAgent.value);
        });

        // 计算输入框的placeholder文本（配置化）
        const inputPlaceholder = computed(() => {
            return getAgentPlaceholder(selectedAgent.value);
        });

        // 计算底部文案显示内容（配置化）
        const footerText = computed(() => {
            return getAgentFooterText(selectedAgent.value);
        });

        // 中断当前查询
        const interruptQuery = async () => {
            // 立即清除本地进行中标记（纯轮询模式下也需如此，避免按钮样式滞留）
            isStreaming.value = false;
            isLoading.value = false;

            // 始终查找当前活跃AI消息（流式或轮询）
            const activeMsg = getActiveAssistantMessage();
            const activeId = activeMsg && activeMsg.chatHistoryId ? parseInt(activeMsg.chatHistoryId) : null;
            
            // 乐观更新：立即停止轮询并标记消息为中断状态
            if (activeId) {
                try {
                    // 1. 立即通过Store标记消息为中断状态
                    conversationStore.markInterruptedByHistoryId(activeId);
                    
                    // 2. 通过会话Store停止实际轮询实例
                    conversationStore.stopPolling(activeId);
                    console.log(`[ChatInput] 立即停止轮询并标记中断 ${activeId}`);
                } catch (e) {
                    console.warn('[ChatInput] 停止轮询失败:', e);
                }
            }

            // 情况1：流式请求正在进行，先中断fetch
            if (queryController.value) {
                emit('message-interrupting');
                try {
                    queryController.value.abort();
                } catch (_) {}
                queryController.value = null;
                // 继续走统一的收尾（尽量让后端停止后台任务）
            }

            // 情况2：轮询恢复中 或 已获取到chatHistoryId的流式消息
            if (!activeId) {
                // 若没有拿到ID（如极早中断，在后端尚未返回chat_history_id），则无法显式终止后端。
                // 此时仅就地停止UI，后台将通过超时清理；可考虑后续提供按conversation_id查询当前流式ID的API以增强。
                return;
            }

            try {
                emit('message-interrupting');

                // 立即在本地将消息标记为中断，避免等待网络往返导致样式不更新
                // 优先精确命中当前消息，否则兜底批量关闭所有进行中的AI消息
                let localTarget = null;
                if (chatState && chatState.messages && Array.isArray(chatState.messages.value)) {
                    localTarget = chatState.messages.value.find(m => m.chatHistoryId === activeId);
                }
                if (!localTarget) localTarget = activeMsg;

                const markInterrupted = (msg) => {
                    if (!msg) return;
                    if (!msg.content || !/\*回复已被中断\*/.test(msg.content)) {
                        msg.content = (msg.content || '');
                    }
                    msg.isStreaming = false;
                    msg.isInProcess = false;
                    msg.isInterrupted = true;
                    msg.timestamp = Date.now();
                    msg.renderedContent = renderMarkdown(msg.content || '');
                };

                if (localTarget) {
                    markInterrupted(localTarget);
                } else if (Array.isArray(props.messages)) {
                    // 极端兜底：将所有进行中的AI消息本地置为中断，确保UI恢复
                    props.messages
                        .filter(m => m && m.role === 'assistant' && (m.isStreaming === true || m.isInProcess === true))
                        .forEach(markInterrupted);
                }

                // 通知后端完成该消息（复用管理端清理接口即可）
                await fetch(`/query/force-cleanup/${activeId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ reason: '用户主动中断' })
                });

                // 拉取一次最终内容，确保UI一致
                const resp = await fetch(`/query/poll/${activeId}`);
                const target = activeMsg;
                if (resp && resp.ok) {
                    const data = await resp.json();
                    if (target) {
                        target.content = data.content || target.content || '';
                        target.isStreaming = false;
                        target.isInProcess = false;
                        target.isInterrupted = true;
                        target.timestamp = Date.now();
                        target.renderedContent = renderMarkdown(target.content || '');
                    }
                } else if (target) {
                    // 后备：仅本地标记中断
                    if (!target.content || !/\*回复已被中断\*/.test(target.content)) {
                        target.content = (target.content || '') + '\n\n*回复已被中断*';
                    }
                    target.isStreaming = false;
                    target.isInProcess = false;
                    target.isInterrupted = true;
                    target.timestamp = Date.now();
                    target.renderedContent = renderMarkdown(target.content || '');
                }
            } catch (e) {
                console.error('中断消息失败:', e);
            }
        };

        // 发送消息
        const sendMessage = async () => {
            // 如果处于流式或轮询中，则中断当前查询
            if (isStreamingOrPolling.value) {
                interruptQuery();
                return;
            }

            // 检查消息是否为空或正在加载
            if (!message.value.trim() && uploadedImages.value.length === 0) return;
            if (isLoading.value || isUploading.value) return;

            // 检查是否有上传失败的图片
            const failedImages = uploadedImages.value.filter(img => img.error);
            if (failedImages.length > 0) {
                alert('请先处理上传失败的图片');
                return;
            }

            // 检查是否有正在上传的图片
            const uploadingImages = uploadedImages.value.filter(img => img.uploading);
            if (uploadingImages.length > 0) {
                alert('请等待图片上传完成');
                return;
            }

            try {
                isLoading.value = true;
                const userMessage = message.value.trim();

                // 收集成功上传的图片URL
                const imageUrls = uploadedImages.value
                    .filter(img => img.url && !img.error && !img.uploading)
                    .map(img => img.url);

                // 清空输入框和图片列表
                message.value = '';
                const imagesToCleanup = [...uploadedImages.value];
                uploadedImages.value = [];

                // 释放预览URL
                imagesToCleanup.forEach(img => {
                    if (img.previewUrl) {
                        revokeImagePreviewUrl(img.previewUrl);
                    }
                });

                if (textareaRef.value) {
                    textareaRef.value.style.height = '24px';
                }

                // 通知父组件用户消息已发送
                emit('message-sent', {
                    content: userMessage,
                    images: imageUrls,
                    timestamp: Date.now()
                });

                // （重构后）不再依赖父组件添加占位，Store 会在 send 中插入占位

                // 纯轮询：通过 Store 发起 /query 并拿到 IDs
                isStreaming.value = true;
                try {
                    const { conversationId: newConvoId, chatHistoryId } = await conversationStore.send({
                        text: userMessage,
                        images: imageUrls,
                        conversationId: props.conversationId,
                        agent: selectedAgent.value,
                        uiId: null
                    });

                    // 由 Store 统一启动轮询并更新UI，此处仅处理按钮状态
                    if (!chatHistoryId) throw new Error('未获取到chat_history_id，无法开始轮询');
                } catch (err) {
                    console.error('查询启动失败:', err);
                    queryController.value = null;
                    emit('message-error', err.message || '发送消息失败');
                } finally {
                    isStreaming.value = false;
                    isLoading.value = false;
                }

            } catch (error) {
                console.error('发送消息失败:', error);
                isStreaming.value = false;
                isLoading.value = false;
                queryController.value = null;
                emit('message-error', error.message || '发送消息失败');
            }
        };

        // 增强提示词
        const enhancePrompt = async () => {
            if (!message.value.trim() || isEnhancing.value) return;

            // 埋点：提示词增强点击
            if (window._xmLog && window._xmLog.clickLog) {
                window._xmLog.clickLog({
                    name: '提示词增强点击'
                });
            }

            try {
                isEnhancing.value = true;

                const response = await fetch('/api/enhance-prompt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: message.value.trim()
                    })
                });

                if (!response.ok) {
                    throw new Error('增强请求失败');
                }

                const result = await response.json();

                if (result.enhanced_prompt) {
                    message.value = result.enhanced_prompt;
                    // 调整文本框高度
                    await nextTick();
                    adjustTextareaHeight();
                }

            } catch (error) {
                console.error('提示词增强失败:', error);
                // 静默失败，不显示错误给用户
            } finally {
                isEnhancing.value = false;
            }
        };

        // 处理按键事件
        const handleKeydown = (event) => {
            // 按下Enter键发送消息，按下Shift+Enter换行
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
            // 按下Ctrl+E增强提示词
            if (event.key === 'e' && (event.ctrlKey || event.metaKey)) {
                event.preventDefault();
                enhancePrompt();
            }
        };

        // 处理输入事件，调整高度
        const handleInput = () => {
            adjustTextareaHeight();
        };

        // 处理图片粘贴
        const handlePaste = async (event) => {
            console.log('粘贴事件触发', event);

            try {
                const imageFile = extractImageFromPaste(event);
                console.log('提取的图片文件:', imageFile);

                if (imageFile) {
                    console.log('检测到图片文件，开始上传:', imageFile.name, imageFile.type, imageFile.size);
                    event.preventDefault();
                    await handleImageUpload(imageFile);
                } else {
                    console.log('未检测到图片文件');
                }
            } catch (error) {
                console.error('处理粘贴事件时出错:', error);
            }
        };

        // 处理图片上传
        const handleImageUpload = async (file) => {
            console.log('开始处理图片上传:', file);

            // 验证图片文件
            const validation = validateImageFile(file);
            console.log('图片验证结果:', validation);

            if (!validation.valid) {
                console.error('图片验证失败:', validation.error);
                alert(validation.error);
                return;
            }

            try {
                isUploading.value = true;
                uploadProgress.value = 0;

                // 创建预览URL
                const previewUrl = createImagePreviewUrl(file);

                // 添加到上传列表（显示预览）
                const imageItem = {
                    id: Date.now(),
                    file,
                    previewUrl,
                    uploading: true,
                    progress: 0,
                    url: null,
                    error: null
                };
                uploadedImages.value.push(imageItem);

                // 上传图片
                const result = await uploadImage(file, (progress) => {
                    imageItem.progress = progress;
                    uploadProgress.value = progress;
                });

                // 上传成功
                imageItem.uploading = false;
                imageItem.url = result.url;
                imageItem.filename = result.filename;
                imageItem.size = result.size;

                console.log('图片上传成功:', result);

            } catch (error) {
                console.error('图片上传失败:', error);

                // 更新错误状态
                const imageItem = uploadedImages.value[uploadedImages.value.length - 1];
                if (imageItem) {
                    imageItem.uploading = false;
                    imageItem.error = error.message;
                }

                alert(`图片上传失败: ${error.message}`);
            } finally {
                isUploading.value = false;
                uploadProgress.value = 0;
            }
        };

        // 移除已上传的图片
        const removeImage = (imageId) => {
            const index = uploadedImages.value.findIndex(img => img.id === imageId);
            if (index !== -1) {
                const image = uploadedImages.value[index];
                // 释放预览URL
                if (image.previewUrl) {
                    revokeImagePreviewUrl(image.previewUrl);
                }
                uploadedImages.value.splice(index, 1);
            }
        };

        // 打开图片预览模态框
        const openImagePreview = (imageUrl, imageName) => {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 cursor-pointer';
            modal.onclick = () => document.body.removeChild(modal);

            // 创建图片容器
            const container = document.createElement('div');
            container.className = 'relative max-w-[90vw] max-h-[90vh] bg-white rounded-lg overflow-hidden';
            container.onclick = (e) => e.stopPropagation();

            // 创建图片
            const img = document.createElement('img');
            img.src = imageUrl;
            img.className = 'max-w-full max-h-full object-contain';
            img.alt = imageName || '预览图片';

            // 创建关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '✕';
            closeBtn.className = 'absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors';
            closeBtn.onclick = () => document.body.removeChild(modal);

            // 创建图片名称标签
            if (imageName) {
                const nameLabel = document.createElement('div');
                nameLabel.textContent = imageName;
                nameLabel.className = 'absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 text-sm';
                container.appendChild(nameLabel);
            }

            container.appendChild(img);
            container.appendChild(closeBtn);
            modal.appendChild(container);
            document.body.appendChild(modal);
        };

        // 处理问题点击
        const handleQuestionClick = (question) => {
            // 设置消息内容为问题文本
            message.value = question.text;

            // 调整输入框高度
            setTimeout(() => {
                adjustTextareaHeight();
            }, 0);

            // 注意：不自动发送，只是填充输入框
        };

        // 处理agent选择
        const selectAgent = (agentName) => {
            const newValue = selectedAgent.value === agentName ? null : agentName;
            selectedAgent.value = newValue;

            // 保存用户选择到cookie
            if (newValue) {
                setCookie('selectedAgent', newValue, { days: 30 });
            } else {
                setCookie('selectedAgent', '', { days: -1 }); // 删除cookie
            }
        };

        // 获取Agent按钮的样式类（配置化）
        const getButtonClasses = (agentId) => {
            return getAgentButtonClasses(agentId, selectedAgent.value === agentId);
        };

        // 检测是否为移动端
        const isMobile = ref(window.innerWidth <= 768);

        // 监听窗口大小变化
        const handleResize = () => {
            isMobile.value = window.innerWidth <= 768;
        };

        onMounted(() => {
            window.addEventListener('resize', handleResize);
        });

        // 获取Agent图标
        const getAgentIcon = (agentId) => {
            switch (agentId) {
                case 'general_chat_bot':
                    return ChatBubbleIcon;
                case 'deep_research_agent':
                    return AnalyticsIcon;
                case 'odps_autopilot_agent':
                    return DatabaseIcon;
                default:
                    return ChatBubbleIcon;
            }
        };

        // 组件挂载后初始化
        onMounted(() => {
            if (textareaRef.value) {
                textareaRef.value.style.height = '24px';
                console.log('ChatInput组件已挂载，图片粘贴功能已启用');
                console.log('textarea元素:', textareaRef.value);
                console.log('textarea事件监听器:', textareaRef.value.onpaste);
            } else {
                console.warn('ChatInput: textareaRef.value 为空');
            }
        });

        return {
            message,
            isLoading,
            isStreaming,
            textareaRef,
            textareaRows,
            sendMessage,
            interruptQuery,
            handleKeydown,
            handleInput,
            showQuestionPrompts,
            handleQuestionClick,
            isHistoryTransition,
            SendIcon,
            SquareIcon,
            WandSparklesIcon,
            ChatBubbleIcon,
            AnalyticsIcon,
            DatabaseIcon,
            // 提示词增强相关
            canEnhance,
            isEnhancing,
            enhancePrompt,
            // 图片上传相关
            uploadedImages,
            isUploading,
            uploadProgress,
            handlePaste,
            handleImageUpload,
            removeImage,
            openImagePreview,
            formatFileSize,
            // Agent选择相关（配置化）
            availableAgents,
            selectedAgent,
            selectAgent,
            getButtonClasses,
            inputBoxBorderClass,
            inputPlaceholder,
            footerText,
            // 新增：用于控制发送/中断按钮状态
            isStreamingOrPolling,
            // 移动端相关
            isMobile,
            getAgentIcon
        };
    },
    template: `
        <div class="w-full px-4 md:px-10 min-w-0 chat-input-container">
            <!-- 主输入区域 -->
            <div :class="['flex flex-col w-full border min-w-0 chat-input-box relative', inputBoxBorderClass]">
                <!-- 问题提示区域 - positioned absolutely to not affect layout flow -->
                <QuestionPrompts
                    :show="showQuestionPrompts"
                    :is-history-transition="isHistoryTransition"
                    @question-click="handleQuestionClick"
                    class="absolute bottom-full left-0 right-0 mb-2 z-10 w-full"
                />

                <!-- 图片预览区域 -->
                <div v-if="uploadedImages.length > 0" class="px-3 pt-3 pb-2">
                    <div class="flex flex-wrap gap-2">
                        <div
                            v-for="image in uploadedImages"
                            :key="image.id"
                            class="relative group"
                        >
                            <!-- 图片预览 -->
                            <div class="w-16 h-16 rounded-lg overflow-hidden border border-base-300 bg-base-200 relative cursor-pointer hover:opacity-80 transition-opacity">
                                <img
                                    v-if="image.previewUrl"
                                    :src="image.previewUrl"
                                    :alt="image.file?.name || 'Uploaded image'"
                                    class="w-full h-full object-cover"
                                    @click="openImagePreview(image.previewUrl, image.file?.name)"
                                    title="点击查看大图"
                                />

                                <!-- 上传进度遮罩 -->
                                <div
                                    v-if="image.uploading"
                                    class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
                                >
                                    <div class="text-white text-xs">{{ Math.round(image.progress) }}%</div>
                                </div>

                                <!-- 错误遮罩 -->
                                <div
                                    v-if="image.error"
                                    class="absolute inset-0 bg-red-500 bg-opacity-70 flex items-center justify-center"
                                    :title="image.error"
                                >
                                    <div class="text-white text-xs">✕</div>
                                </div>

                                <!-- 删除按钮 -->
                                <button
                                    v-if="!image.uploading"
                                    @click="removeImage(image.id)"
                                    class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                                    title="删除图片"
                                >
                                    ✕
                                </button>
                            </div>

                            <!-- 图片信息 -->
                            <div class="text-xs text-base-content/60 mt-1 max-w-16 truncate">
                                {{ image.file?.name || 'image' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="w-full px-3 pt-3 pb-1 chat-input-area">
                    <textarea
                        ref="textareaRef"
                        v-model="message"
                        class="w-full resize-none focus:outline-none border-0 bg-transparent min-w-0 p-0 text-[16px] leading-6 min-h-[24px] max-h-[200px] chat-input-textarea scrollbar-auto"
                        :placeholder="inputPlaceholder"
                        :rows="textareaRows"
                        @keydown="handleKeydown"
                        @input="handleInput"
                        @paste="handlePaste"
                        style="height: 24px; background-color: transparent;"
                    ></textarea>
                </div>

                <!-- 功能按钮区域 -->
                <div class="chat-input-footer" :class="isMobile ? 'mobile-footer' : 'desktop-footer'">
                    <!-- 桌面端：横向布局 -->
                    <div v-if="!isMobile" class="flex items-center justify-between py-1.5 px-3">
                        <div class="flex items-center gap-2">
                            <!-- Agent选择按钮（配置化动态生成） -->
                            <button
                                v-for="agent in availableAgents"
                                :key="agent.id"
                                :class="[getButtonClasses(agent.id), 'agent-button desktop-agent-button']"
                                @click="selectAgent(agent.id)"
                                :title="agent.title"
                            >
                                <span class="w-4 h-4 flex-shrink-0 mr-1.5" v-html="getAgentIcon(agent.id)"></span>
                                <span class="flex-shrink-0">{{ agent.name }}</span>
                            </button>
                            <span class="text-xs font-light tracking-wide chat-input-footer-text ml-2">
                                {{ footerText }}
                            </span>
                        </div>
                        <div class="flex items-center gap-2">
                            <!-- 提示词增强按钮 -->
                            <button
                                :class="[
                                    'px-2 py-1 rounded-full transition-all duration-200 disabled:cursor-not-allowed',
                                    'flex items-center justify-center',
                                    canEnhance ? 'hover:bg-base-200 text-base-content/80 hover:text-base-content' : 'opacity-50 text-base-content/40'
                                ]"
                                :disabled="!canEnhance"
                                @click="enhancePrompt"
                                :title="isEnhancing ? '正在优化...' : '优化提示词 (Ctrl+E)'"
                            >
                                <span v-if="isEnhancing" class="loading loading-spinner w-[18px] h-[18px]"></span>
                                <span v-else class="w-[18px] h-[18px]" v-html="WandSparklesIcon"></span>
                                <span class="ml-1 text-xs">优化提示词</span>
                            </button>

                            <!-- 发送按钮 -->
                            <button
                                :class="[
                                    'p-1.5 rounded-full transition-all duration-200 disabled:cursor-not-allowed chat-input-send-button',
                                    isStreamingOrPolling || ((message.trim() || uploadedImages.length > 0) && !isLoading && !isUploading) ? 'chat-input-send-button-active' : 'chat-input-send-button-inactive'
                                ]"
                                :disabled="(!message.trim() && uploadedImages.length === 0 && !isStreamingOrPolling) || (isLoading && !isStreamingOrPolling) || isUploading"
                                @click="sendMessage"
                                :title="isStreamingOrPolling ? '中断查询' : isUploading ? '正在上传图片...' : '发送'"
                            >
                                <span v-if="isLoading && !isStreamingOrPolling" class="loading loading-spinner loading-sm"></span>
                                <span v-else-if="isStreamingOrPolling" class="w-[18px] h-[18px] chat-input-interrupt-icon" v-html="SquareIcon"></span>
                                <span v-else class="w-[18px] h-[18px] chat-input-send-icon" v-html="SendIcon"></span>
                            </button>
                        </div>
                    </div>

                    <!-- 移动端：垂直布局 -->
                    <div v-else class="py-1.5 px-3">
                        <!-- 第一行：Agent按钮和右侧按钮 -->
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center gap-1.5">
                                <!-- Agent选择按钮（移动端只显示图标） -->
                                <button
                                    v-for="agent in availableAgents"
                                    :key="agent.id"
                                    :class="[getButtonClasses(agent.id), 'agent-button mobile-agent-button']"
                                    @click="selectAgent(agent.id)"
                                    :title="agent.title"
                                >
                                    <span class="w-4 h-4 flex-shrink-0" v-html="getAgentIcon(agent.id)"></span>
                                </button>
                            </div>

                            <div class="flex items-center gap-2">
                                <!-- 提示词增强按钮（移动端只显示图标） -->
                                <button
                                    :class="[
                                        'p-1.5 rounded-full transition-all duration-200 disabled:cursor-not-allowed',
                                        'flex items-center justify-center',
                                        canEnhance ? 'hover:bg-base-200 text-base-content/80 hover:text-base-content' : 'opacity-50 text-base-content/40'
                                    ]"
                                    :disabled="!canEnhance"
                                    @click="enhancePrompt"
                                    :title="isEnhancing ? '正在优化...' : '优化提示词'"
                                >
                                    <span v-if="isEnhancing" class="loading loading-spinner w-[18px] h-[18px]"></span>
                                    <span v-else class="w-[18px] h-[18px]" v-html="WandSparklesIcon"></span>
                                </button>

                                <!-- 发送按钮 -->
                                <button
                                    :class="[
                                        'p-1.5 rounded-full transition-all duration-200 disabled:cursor-not-allowed chat-input-send-button',
                                        isStreamingOrPolling || ((message.trim() || uploadedImages.length > 0) && !isLoading && !isUploading) ? 'chat-input-send-button-active' : 'chat-input-send-button-inactive'
                                    ]"
                                    :disabled="(!message.trim() && uploadedImages.length === 0 && !isStreamingOrPolling) || (isLoading && !isStreamingOrPolling) || isUploading"
                                    @click="sendMessage"
                                    :title="isStreamingOrPolling ? '中断查询' : isUploading ? '正在上传图片...' : '发送'"
                                >
                                    <span v-if="isLoading && !isStreamingOrPolling" class="loading loading-spinner loading-sm"></span>
                                    <span v-else-if="isStreamingOrPolling" class="w-[18px] h-[18px] chat-input-interrupt-icon" v-html="SquareIcon"></span>
                                    <span v-else class="w-[18px] h-[18px] chat-input-send-icon" v-html="SendIcon"></span>
                                </button>
                            </div>
                        </div>

                        <!-- 第二行：提示文本 -->
                        <div class="text-xs font-light tracking-wide chat-input-footer-text text-center">
                            {{ footerText }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-xs text-base-content/50 mt-2 text-center chat-input-hint">
                ChatBI 的回答未必准确无误，请仔细核查数据
            </div>
        </div>
    `
};
