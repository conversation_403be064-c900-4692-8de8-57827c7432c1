/**
 * Chat Input Component Styles
 *
 * These styles enhance the existing Tailwind classes without replacing them.
 * They provide theme-based styling using CSS variables following Apple/OpenAI aesthetics.
 */

/*
 * We're not setting dimensions or layouts properties here
 * to avoid conflicts with the existing Tailwind classes.
 * Instead, we're focusing on theming aspects.
 */

.chat-input-container {
  /* No layouts properties to avoid conflicts */
  transition: background-color var(--duration-normal), border-color var(--duration-normal);
  /* 防止移动端浏览器缩放 */
  touch-action: manipulation; /* 禁用双击缩放 */
}

.chat-input-box {
  /* Use CSS variables for theming while preserving the Tailwind layouts */
  border-color: var(--color-border-primary);
  background-color: var(--color-bg-primary);
  transition: background-color var(--duration-normal), box-shadow var(--duration-normal), border-radius var(--duration-normal);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03), 0 1px 2px rgba(0, 0, 0, 0.02);
  border-radius: 1.1rem; /* Very subtle increase from the original 1rem (rounded-2xl) */
}

/* Dark mode specific shadow and background */
[data-theme="dark"] .chat-input-box {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.07);
  background-color: var(--color-bg-primary) !important; /* Force dark background */
}

.chat-input-area {
  /* No layouts properties to avoid conflicts */
  transition: background-color var(--duration-normal);
  background-color: transparent;
}

/* Dark mode specific background for input area */
[data-theme="dark"] .chat-input-area {
  background-color: transparent !important;
}

.chat-input-textarea {
  /* Enhance the existing styles without replacing them */
  color: var(--color-text-primary);
  background-color: transparent !important; /* Force transparency */
  transition: color var(--duration-normal);
  /* 防止移动端浏览器缩放 */
  font-size: 16px !important; /* 确保字体大小至少16px，防止iOS Safari自动缩放 */
  touch-action: manipulation; /* 禁用双击缩放，但保留其他触摸手势 */
}

.chat-input-textarea::placeholder {
  color: var(--color-text-placeholder);
  transition: color var(--duration-normal);
}

.chat-input-footer {
  /* No layouts properties to avoid conflicts */
  transition: color var(--duration-normal);
}

/* 额外的防缩放规则 */
.chat-input-box,
.chat-input-area,
.chat-input-container * {
  /* 防止所有输入相关元素触发缩放 */
  touch-action: manipulation;
}

/* 确保在所有设备上输入框字体都足够大 */
@media (max-width: 640px) {
  .chat-input-textarea {
    font-size: 16px !important;
    height: 3.5rem !important;
  }
}

/* 移动端按钮样式优化 */
@media (max-width: 768px) {
  /* 移动端footer布局 */
  .mobile-footer {
    /* 移动端垂直布局 */
  }

  .desktop-footer {
    /* 桌面端保持原有布局 */
  }

  /* Agent按钮样式 */
  .mobile-agent-button {
    /* 移动端按钮：只显示图标，更紧凑 */
    padding: 0.5rem !important;
    min-width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
  }

  .desktop-agent-button {
    /* 桌面端按钮：图标+文字 */
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
    white-space: nowrap;
  }

  /* 移动端提示文本样式 */
  .mobile-footer .chat-input-footer-text {
    font-size: 0.65rem;
    line-height: 1.2;
    color: var(--color-text-tertiary);
    margin-top: 0.25rem;
    text-align: left; /* 移动端左对齐 */
  }

  /* 确保图标在移动端按钮中居中 */
  .mobile-agent-button svg {
    width: 16px !important;
    height: 16px !important;
  }

  /* 桌面端图标样式 */
  .desktop-agent-button svg {
    width: 16px !important;
    height: 16px !important;
  }

  /* 移动端按钮间距优化 */
  .mobile-footer .flex.items-center.gap-1\.5 {
    gap: 0.375rem; /* 6px */
  }

  /* 移动端右侧按钮区域 */
  .mobile-footer .flex.items-center.gap-2 {
    gap: 0.5rem; /* 8px */
  }
}

/* Agent按钮通用样式 */
.agent-button {
  transition: all 0.2s ease;
  border: 1px solid transparent;
  font-weight: 500;
}

.agent-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.agent-button:active {
  transform: translateY(0);
}

/* 确保图标颜色继承按钮文字颜色 */
.agent-button svg {
  color: currentColor;
}

.chat-input-footer-text {
  color: var(--color-text-tertiary);
  font-size: 0.8rem;
  transition: color var(--duration-normal);
}

/* Active state - when there's text in the input */
.chat-input-send-button-active {
  background-color: var(--color-text-primary);
  color: white;
}

.chat-input-send-button-active:hover {
  opacity: 0.9; /* Slightly lighter on hover */
}

.chat-input-send-button-active:active {
  opacity: 0.8; /* Even lighter on active/press */
}

/* Inactive state - when input is empty */
.chat-input-send-button-inactive {
  background-color: var(--color-button-bg);
  opacity: 0.7;
}

/* 中断图标样式 */
.chat-input-interrupt-icon {
  color: currentColor;
}

.chat-input-send-icon,
.chat-input-interrupt-icon {
  color: currentColor;
  transition: color var(--duration-normal);
}

/* Dark mode adjustments */
[data-theme="dark"] .chat-input-send-button-active {
  background-color: var(--color-text-primary); /* Use text color which is light in dark mode */
  color: var(--color-text-inverse); /* Use inverse text color (dark) */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .chat-input-send-button-inactive {
  background-color: var(--color-button-bg);
  opacity: 0.5;
}

.chat-input-hint {
  color: var(--color-text-secondary);
  font-size: 0.8rem;
  transition: color var(--duration-normal), opacity var(--duration-normal);
}

/* Agent选择按钮样式 */
/* 问答按钮 - 蓝色主题 */
.chat-input-footer button.bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  color: rgb(37, 99, 235) !important;
}

/* 深度分析按钮 - 紫色主题 */
.chat-input-footer button.bg-purple-50 {
  background-color: rgba(147, 51, 234, 0.1) !important;
  border-color: rgba(147, 51, 234, 0.3) !important;
  color: rgb(126, 34, 206) !important;
}

/* 暗色模式下的agent按钮样式 */
[data-theme="dark"] .chat-input-footer button.bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.2) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  color: rgb(147, 197, 253) !important;
}

[data-theme="dark"] .chat-input-footer button.bg-purple-50 {
  background-color: rgba(147, 51, 234, 0.2) !important;
  border-color: rgba(147, 51, 234, 0.4) !important;
  color: rgb(196, 181, 253) !important;
}

/* 输入框边框颜色同步 */
.chat-input-box.border-blue-300 {
  border-color: rgba(59, 130, 246, 0.3) !important;
}

.chat-input-box.border-purple-300 {
  border-color: rgba(147, 51, 234, 0.3) !important;
  background-color: rgba(147, 51, 234, 0.02) !important; /* 非常淡的紫色背景 */
}

/* 暗色模式下的输入框边框颜色 */
[data-theme="dark"] .chat-input-box.border-blue-300 {
  border-color: rgba(59, 130, 246, 0.4) !important;
}

[data-theme="dark"] .chat-input-box.border-purple-300 {
  border-color: rgba(147, 51, 234, 0.4) !important;
  background-color: rgba(147, 51, 234, 0.05) !important; /* 暗色模式下稍微深一点的紫色背景 */
}

/* 提示词增强按钮样式 */
.chat-input-footer button .loading.loading-spinner {
  width: 18px !important;
  height: 18px !important;
  border-width: 2px !important;
  /* 确保 spinner 完美居中 */
  display: block;
  margin: 0;
}

/* 深度分析模式下的发送按钮样式 */
.chat-input-box.border-purple-300 .chat-input-send-button-active {
  background-color: rgb(126, 34, 206) !important; /* 紫色发送按钮 */
  color: white !important;
}

.chat-input-box.border-purple-300 .chat-input-send-button-active:hover {
  background-color: rgb(107, 33, 168) !important; /* 悬停时稍深的紫色 */
}

/* 暗色模式下的深度分析发送按钮 */
[data-theme="dark"] .chat-input-box.border-purple-300 .chat-input-send-button-active {
  background-color: rgb(147, 51, 234) !important;
  color: white !important;
}

[data-theme="dark"] .chat-input-box.border-purple-300 .chat-input-send-button-active:hover {
  background-color: rgb(126, 34, 206) !important;
}
