# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

.DS_Store
.activate.sh
*.csv
np/
*.ckpt
venv_np/
lightning_logs/
pid.txt
爬虫商品匹配/data/
CRM-拜访记录搜索/data/
CRM-拜访记录搜索/data_lightrag/
CRM-ChatBI/nohup.out
CRM-ChatBI/restart_chat_bi.sh
data/
nohup.out
CRM-拜访记录搜索/data/
CRM-拜访记录搜索/data_lightrag/
browser-use/py3_12/
ddl/
lightrag_data/
qiyu_audio_data/ChatBI-MySQL/history/
ChatBI-MySQL/history/*
.idea/*
.idea
.aider*
.activate.sh
.deactivate.sh
ChatBI-MySQL/.vscode/
.kirologs/
app.pid.log
.kiro
.vscode
.claude
test_streaming_result_*.md
.claude/
*_review_*.md
chatbi_data_only_20250829.sql
chatbi_data_only_*.sql
test_results/**/**

# ClaudePoint checkpoint system
.claudepoint/
resources/odps_raw_tables/